Issue Type,Key,Priority,Summary,Creator,Created,Date of First Response,Redeclared,Description.1,cleaned_description,sentiment,Resolution Time (Days),days_old,temporal_decay,sentiment_impact,decayed_ticket_count,ticket_impact,priority_impact,Issue_Type_impact,urgency_score
Incident,OEKB-6654,Medium,M12 PROD - EXOF0003213841 - Market Entitlement Calculation incorrect,<PERSON><PERSON>,2025-02-05 10:40:00,2025-02-05 10:48:00,NO,"EXOF0003213841 
We processed one client payment for fractions (0,47222) via Ad Hoc Booking in USD (client 227300) on 03/02. 

For the remaining fractional positions (in total 3,02778), we updated the Fraction Price by selecting all clients, but without 227300, and calculated the client entitlements accordingly on 04/02. 
Client payments were processed correctly. 

When checking Market Entitlements, we see that the calculation (of *3,5*) was incorrect, due to the fact, that also the position of 227300 was taken into consideration. 

Could you please check? 
Thanks, <PERSON>",exof processed one client payment fraction via ad hoc booking usd client remaining fractional position total updated fraction price selecting client without calculated client entitlement accordingly client payment processed correctly checking market entitlement see calculation incorrect due fact also position taken consideration could please check thanks bert,-0.04618646018207073,0.0,103,0.1796640309893994,0.2615466150455177,5.****************,0.2,0.1,0.15,56.73%
Incident,OEKB-6653,Medium,Dashboard alert - Validate Manual Creation / Updates - View Problem,<PERSON>mann GEROLD,2025-02-05 10:11:00,2025-02-05 10:22:00,NO,"It is concerning the Dashboard alert: Validate Manual Creation / Updates (under Event validation) 

In the In the Do to List MegaCor (where all Dashboard alert are shown) you see that there is one dashboard alert - see attachment (name of attachement: Event validation = 1) for Validate Manual Creation / Updates. 

If I click on this “1” a new window with the search results pops up - but there is no result (nothing to validate) - see attachment (name of attachment: But nothing to validate) 



If I go to the menu to the menu point “Validate/Reject Manual Creation/Updates the you can see that there is one manual creation to validate (or reject). - see attachment (name of attachment: menu - one to validate). 

It seems to me, that this issue always appears if I create manually a new event - in this case you can not see the new created event unter the Dashboard alert . 



Can you please check? 



",concerning dashboard alert validate manual creation update event validation list megacor dashboard alert shown see one dashboard alert see attachment name attachement event validation validate manual creation update click new window search result pop result nothing validate see attachment name attachment nothing validate go menu menu point validatereject manual creationupdates see one manual creation validate reject see attachment name attachment menu one validate seems issue always appears create manually new event case see new created event unter dashboard alert please check,0.0016500968486070633,0.0,104,0.17669444575659674,0.24958747578784823,1.****************,0.*****************,0.1,0.15,34.14%
Requirement,OEKB-6652,Medium,New User Profile CSD Admin for Externals,Nikolay TSONKOV,2025-01-31 14:56:00,,NO,"Hello Emna, 

we will need one new Userprofile named CSD Admin for Externals , where rights should be ReadOnly except for the Menus where IT Admins should be able to work. such as Input/Output Devices, Quartz, Jobs Executions etc 

but rights to Business related Data should be Read Only, no rights to validate or reject 

BR, 

Niki",hello emna need one new userprofile named csd admin external right readonly except menu admins able work inputoutput device quartz job execution etc right business related data read right validate reject br niki,0.04815080389380455,0.0,108,0.16529888822158653,0.23796229902654886,2.***************,0.*****************,0.1,0.09,28.25%
Incident,OEKB-6649,Major,M12 PROD - Update from Referential - URGENT,Dalibor VIDIC,2025-01-31 09:39:00,2025-01-31 09:57:00,NO,"Hello, 

It has come to our attention that the three INTR events listed below have received an update from referentials and as a result important event data has changed (Coupon Date, Price, Interest End, Interest number of days). All of these changes are not correct. 

AT0000A2MKT0 - INTR00160369 

AT0000A2MJH7 - INTR00160368 

AT0000A2UVU8 - INTR00160372 

All three events have been ‘crosschecked’ by us and have the status ‘Activated’. 

Please check why these changes have been made. 

Thank you and BR, Dali 

",hello come attention three intr event listed received update referentials result important event data changed coupon date price interest end interest number day change correct atamkt intr atamjh intr atauvu intr three event crosschecked u status activated please check change made thank br dali,-0.016042761504650116,0.0,109,0.16256673791438075,0.25401069037616253,3.***************,0.*****************,0.13,0.15,45.87%
Information Request,OEKB-6648,Medium,Failed Recieved Sese.025 and sese024,Nikolay TSONKOV,2025-01-30 12:01:00,2025-01-30 12:39:00,NO,"Hello Emna, colleagues 

in M12 MC via the dashboards we find entries in failed sese024 and failed sese025 



we see some of the entries e.g from failed sese025 are with date M12 Go Live date 

but the entries from failed sese 25 we do not know what do they repsent 

attached are extractions from both of those 

can you pls advice on the next steps and what will be the effect of those actions? 



",hello emna colleague mc via dashboard find entry failed sese failed sese see entry eg failed sese date go live date entry failed sese know repsent attached extraction pls advice next step effect action,-0.1339175794273615,0.0,109,0.16256673791438075,0.28347939485684037,2.***************,0.*****************,0.1,0.06,30.58%
Information Request,OEKB-6647,Medium,M12 PROD - How to edit failed MT564 IN in MegaBroker?,Bertram Schon,2025-01-29 10:02:00,2025-01-29 10:48:00,NO,"Hi, 
due to a mapping error in 3i, we received MT564 CHAN today in status “Parsing and Mapping Failed”. 

My question: 
is there a way to correct the wrong value in MegaBroker and replay the message? 
If yes, could you please explain how we can do this. 
If not, can we correct the files manually and send it directly into PROD via WinSCP [~oekb-nikts] ? 

Thanks for your quick response\! 
BR Bert",hi due mapping error received mt chan today status parsing mapping failed question way correct wrong value megabroker replay message yes could please explain correct file manually send directly prod via winscp oekbnikts thanks quick response br bert,-0.02231874316930771,0.0,111,0.1572371663136276,0.2555796857923269,5.****************,0.2,0.1,0.06,42.34%
Information Request,OEKB-6646,Medium,M12 PROD - Validate Conflicting Updates - REDM Events,Dalibor VIDIC,2025-01-28 14:50:00,2025-01-28 15:14:00,NO,"Hello, 

On 25 January, 151 REDM events were updated and are awaiting verification. However, it is not clear to us what and why something has been updated here and how we should deal with it. 

For example, the 'Quotation Mode' is updated for an event even though the 'Quotation Mode' is correct in the event. But there are also other updates. 

Please check\! 

Thank you and BR, Dali",hello january redm event updated awaiting verification however clear u something updated deal example quotation mode updated event even though quotation mode correct event also update please check thank br dali,-0.03244965896010399,0.0,111,0.1572371663136276,0.258112414740026,3.***************,0.*****************,0.1,0.06,28.48%
Incident,OEKB-6642,Major,alert for Flow Failure Alert,Nikolay TSONKOV,2025-01-28 08:30:00,2025-01-28 10:56:00,NO,"HI Emna, colleagues, 

today we recieved two Alerts e.g Flow Failure Alert 



attached 

can you pls advice if any actions needed",hi emna colleague today recieved two alert eg flow failure alert attached pls advice action needed,-0.08046099543571472,0.0,112,0.****************,0.2701152488589287,2.***************,0.*****************,0.13,0.15,46.57%
Incident,OEKB-6641,Major,+++ URGENT +++ M12 PROD - PROX0003213896 - Validate/Reject of update failed,Bertram Schon,2025-01-27 11:41:00,2025-01-27 11:43:00,NO,"PROX0003213896 
was set up according to seev.001 IN (attached). 
After updating (adding ANOU, deleting Participation Method MAIL, amended Client Deadline time), we noticed that we are not able to validate or reject the draft. 

When clicking on the relevant row, error “Index 1 out of bounds for length 1” popped up (see screenshot). 

Could you please check urgently? 

Thanks, Bert 
",prox set according seev attached updating adding anou deleting participation method mail amended client deadline time noticed able validate reject draft clicking relevant row error index bound length popped see screenshot could please check urgently thanks bert,-0.013543490320444107,0.0,112,0.****************,0.***************,5.****************,0.2,0.13,0.15,60.01%
Incident,OEKB-6638,Major,M12 PROD - SHDS0003213872 - Disclosure Response created although Invalid Instruction was received,Bertram Schon,2025-01-27 08:15:00,2025-01-27 08:17:00,NO,"SHDS0003213872 
As there is an ELIG pos. on a blocked account (222138), standing instruction was created by the job on 24/07, 19:45 with status “InvalidData”. 

In such a case, the system should 
a) NOT consolidate and NOT generate the response (before the user had the chance to repair the instruction) 
b) SEND an E-Mail-alert 

However, for this instruction no alert was received and the response seev.047 was created (see screenshot) with wrong position. 

Could you please check urgently? 

Thanks, Bert",shds elig po blocked account standing instruction created job status invaliddata case system consolidate generate response user chance repair instruction b send emailalert however instruction alert received response seev created see screenshot wrong position could please check urgently thanks bert,-0.*****************,0.0,113,0.*****************,0.****************,5.****************,0.2,0.13,0.15,62.98%
Requirement,OEKB-6637,Medium,Create E-mail Alerts to Replicate RPA Alert,Nikolay TSONKOV,2025-01-24 11:52:00,,NO,"Hello Emna,colleagues 

we will need to create E-mail Alerts to monitor same process which are currently monitord from the RPA 

i will come additionally with Full Scope of the Alerts we will need to create and monitro",hello emnacolleagues need create email alert monitor process currently monitord rpa come additionally full scope alert need create monitro,0.001669304445385933,0.0,115,0.****************,0.*****************,2.***************,0.*****************,0.1,0.09,29.99%
Incident,OEKB-6636,Medium,Error Message when clicking on Show History for Financial Instruments for ISIN AT0000306691 =,Wallmann GEROLD,2025-01-23 14:43:00,2025-01-23 14:48:00,NO,"ISIN = AT0000306691 

When I go to FINANCIAL INSTRUMENTS and click on Show History there is an alert - see attachment. 

Please can you check. 

For this ISIN there is also this e-mail alert. 



Event Code : Failed_Run_Loan_Schedule 
Description : Failed_Run_Loan_Schedule 
Failed calculation of loan schedule for identifier :AT00003066910 for this reason : Cannot invoke ""com.vermeg.services.lon.loanEnum.CouponRateType.toString()"" because the return value of ""com.vermeg.services.lon.Loan.getCouponRateType()"" is null 



Can you please check.",isin go financial instrument click show history alert see attachment please check isin also email alert event code failedrunloanschedule description failedrunloanschedule failed calculation loan schedule identifier reason invoke comvermegserviceslonloanenumcouponratetypetostring return value comvermegserviceslonloangetcouponratetype null please check,-0.05420177988708019,0.0,116,0.14466517663899506,0.26355044497177005,1.****************,0.*****************,0.1,0.15,36.23%
Information Request,OEKB-6632,Medium,"""Failed Received Data CA"" does not load in MegaBroker",Ana Maria Galic,2025-01-22 10:25:00,2025-01-22 10:53:00,NO,"Hi, 

I wanted to check something in Megabroker today and then realized that the screen ""Failed Received Data CA"" won’t load, I can’t see any entries. 
Can you please check what’s the issue here? 

Thank you 
BR Ana 

",hi wanted check something megabroker today realized screen failed received data ca wont load cant see entry please check whats issue thank br ana,-0.04091331362724304,0.0,118,0.1399224880243094,0.26022832840681076,1.****************,0.037820429797300145,0.1,0.06,18.71%
Information Request,OEKB-6629,Medium,M12 PROD Linux Patching 23.01.2025 we will need someone to support us,Nikolay TSONKOV,2025-01-21 14:50:00,,NO,"Hi Emna, colleagues, 

thursday 23.01.2025 at 16:30 there is going to be patching on the production enviroments from M12 and Issuer Platfform 



i have sent a meeting on which also Consol and Montran are invited, pls can we ask someone from your side to support us as there is a need to follow appropriate stop of M12 before patching is done 



can you pls share with us who can support us",hi emna colleague thursday going patching production enviroments issuer platfform sent meeting also consol montran invited pls ask someone side support u need follow appropriate stop patching done pls share u support u,0.11659980192780495,0.0,118,0.1399224880243094,0.22085004951804876,2.***************,0.*****************,0.1,0.06,21.18%
Information Request,OEKB-6627,Medium,Followup on ticket OEKB-6456 add Delete Button on Repair Recieved Feed Screen,Nikolay TSONKOV,2025-01-20 08:58:00,2025-01-28 13:46:00,NO,"Hello, as discussed lets please add button delete so we can delete messages which are not needed 

menu: Repair Recieved Feed 



for Nadia 



followup from Ticket OEKB-6456 



pls remove the solution from ticket OEKB-6456 no need to hide any entries from the result screen",hello discussed let please add button delete delete message needed menu repair recieved feed nadia followup ticket oekb pls remove solution ticket oekb need hide entry result screen,-0.4497064631432295,8.0,120,0.1353352832366127,0.36242661578580737,2.***************,0.*****************,0.1,0.06,42.42%
Information Request,OEKB-6626,Major,M12 to SAP,Nikolay TSONKOV,2025-01-20 07:51:00,2025-01-20 08:07:00,NO,"Dear colleagues, 

there was outage during the Weekend on the SAP side 



i think today the connection M12 MB to SAP does not work 



Hier is the Error msg 

attached the default logs from M12 MC and MB 



i think restart of M12 may help , but i need your opinion 





",dear colleague outage weekend sap side think today connection mb sap work hier error msg attached default log mc mb think restart may help need opinion,0.007546946406364441,0.0,120,0.1353352832366127,0.2481132633984089,2.***************,0.*****************,0.13,0.06,29.77%
Information Request,OEKB-6625,Major,+++ URGENT +++ M12 PROD - TEND0003213516 +++ MT564 NACKed in SWIFT Alliance,Bertram Schon,2025-01-17 14:19:00,2025-01-17 14:27:00,NO,"TEND0003213516 
We tried to send MT564, MT568 NEWM, but noticed that MT564 messages have been NACKed in SWIFT Alliance with NACK code 
\{405:T89029}. 

We assume that the error refers to one the WEBB fields => 
:70G::WEBB//[https://www.rosenbauer.com|https://www.rosenbauer.com] 
:70G::WEBB//[https://robau-beteiligung.at/|https://robau-beteiligung.at/] 

Could you please check (full message below) and let us know how we can resend MT564 NEWM (as MT568 NEW are already sent successfully)? 

Thanks, Bert 


",tend tried send mt mt newm noticed mt message nacked swift alliance nack code assume error refers one webb field gwebb gwebb could please check full message let u know resend mt newm mt new already sent successfully thanks bert,0.07635917887091637,0.0,122,0.13089846490974433,0.2309102052822709,5.****************,0.2,0.13,0.06,43.14%
Incident,OEKB-6623,Minor,Test ticket for ConSol for interface implementation,Sonja TAGHIPOUR,2025-01-17 08:54:00,2025-01-17 09:00:00,NO,"This is a test ticket for ConSol for interface implementation 

@Emna can you pls proceed as follows 

# create one test ticket for each application 
*=>* [*@<EMAIL>*|mailto:<EMAIL>] *and/or* 
[@<EMAIL>|mailto:<EMAIL>] 

Please use: 
## ""Test ticket for ConSol for interface implementation"" as the *small description.* 
and something like ""This is a test ticket for ConSol for interface implementation"" as the description. 
# Accept your ticket and write some update into it 

*=>* [*@Emna El Mekki*|mailto:<EMAIL>] *and* [*@Adrian 
Mărincean*|mailto:<EMAIL>] 

# *Write some update into the tickets after acceptance* 

*=>* [*@<EMAIL>*|mailto:<EMAIL>] *and/or* [*@<EMAIL>*|mailto:<EMAIL>] 

# Close the tickets 
*=>* [*@<EMAIL>*|mailto:<EMAIL>] *and/or* [*@<EMAIL>*|mailto:<EMAIL>] 

",test ticket consol interface implementation emna pls proceed follows create one test ticket application sonjataghipouroekbcsdatmailtosonjataghipouroekbcsdat andor lukaszwalczukoekbcsdatmailtolukaszwalczukoekbcsdat please use test ticket consol interface implementation small description something like test ticket consol interface implementation description accept ticket write update emna el mekkimailtoeelmekkivermegcom adrian mrinceanmailtoamarinceanmontrancom write update ticket acceptance sonjataghipouroekbcsdatmailtosonjataghipouroekbcsdat andor lukaszwalczukoekbcsdatmailtolukaszwalczukoekbcsdat close ticket sonjataghipouroekbcsdatmailtosonjataghipouroekbcsdat andor lukaszwalczukoekbcsdatmailtolukaszwalczukoekbcsdat,0.003384893760085106,0.0,123,0.12873490358780423,0.24915377655997872,0.*****************,0.0045637065289876105,0.07,0.15,21.06%
Incident,OEKB-6622,Medium,User FRG can not login in M12 PROD,Nikolay TSONKOV,2025-01-17 08:42:00,2025-01-17 08:55:00,NO,"Hello dears, 

Gerhard can not login in M12 MC PROD 



i checked his user is active and it is not blocked, 

his user is also not locked in the Active Directory 



would you have any ideas what else could we check? 



BR, 

Niki",hello dear gerhard login mc prod checked user active blocked user also locked active directory would idea else could check br niki,-0.11709359847009182,0.0,123,0.12873490358780423,0.27927339961752295,2.***************,0.*****************,0.1,0.15,43.45%
Incident,OEKB-6619,Medium,M12 PROD - PROX0003212890 - Input of Registration Deadline TIME does not work properly,Bertram Schon,2025-01-16 13:11:00,2025-01-16 13:16:00,NO,"PROX0003212890 
We noticed that the input of a Client and Market *Registration Deadline TIME* does not work properly. 
When we input a value, it is automatically set to 00:00:00:000. 

We then have to overwrite it to, for instance, 22:59:59:000. 

Could you please check and provide the same input facility as for other deadline times (e.g. Voting Deadline) where it works properly? 

Thanks, Bert 
",prox noticed input client market registration deadline time work properly input value automatically set overwrite instance could please check provide input facility deadline time eg voting deadline work properly thanks bert,0.002327714115381241,0.0,123,0.12873490358780423,0.2494180714711547,5.****************,0.2,0.1,0.15,54.91%
Information Request,OEKB-6618,Medium,M12 PROD: no information about seev.004 InvalidData,Stefan RIBISCH,2025-01-16 12:57:00,2025-01-16 13:03:00,YES,"Yesterday we received first seev.004 in production and had some troubles with that (see also OEKB-6615). 

Unfortunately we were just aware of this incoming seev.004 due to an email of our client which wanted to have receipt of this SWIFT confirmed. Otherwise it’s not possible for us to be aware of a new seev.004 - alert has not been created. 

Could you please eplain how we should be aware of incoming seev.004 in the future? Maybe an alert would be the solution but it doesn’t work? Should I create a bug for that resp. could you fix this with this ticket? 

Thanks for your support. 

BR, stefan",yesterday received first seev production trouble see also oekb unfortunately aware incoming seev due email client wanted receipt swift confirmed otherwise possible u aware new seev alert created could please eplain aware incoming seev future maybe alert would solution doesnt work create bug resp could fix ticket thanks support br stefan,-0.3731032647192478,0.0,123,0.12873490358780423,0.34327581617981195,0.*****************,0.0054320283355181135,0.1,0.06,26.31%
Information Request,OEKB-6616,Medium,Manual Creation of Financial Instrument,Nikolay TSONKOV,2025-01-15 10:09:00,2025-01-31 15:52:00,NO,"Hello Emna, Colleagues, 



there are 8 ISINS which need to be created manually as they do not exist in the WDBO 



those isin were also in M10 

AU00AU293245 

CA83422D9907 

US524ESC1001 

US978ESC1010 

QOXABKAMA911 

QOXABKAMA762 

USY481009972 

QOXABKAMA739 

ideally we would like if is there a way to export from M10 and import those in M12? if not then we try to set those manully up 

but while setting those up we face 3 questions 

e.g 

* the Drop Down List Values Field Security Classification in M12 differs fom M10 
* There is field Place of Safekeeping in M12 where there is no such field in M10 ,please advice what value we should put there 
* issuer some issuers which exist in M10 are not to be found in M12.How to proceed with this. i ask as i tought there was migration from M10 to M12 and i tought those issuers should be available in M12 

",hello emna colleague isins need created manually exist wdbo isin also auau cad usesc usesc qoxabkama qoxabkama usy qoxabkama ideally would like way export import try set manully setting face question eg drop list value field security classification differs fom field place safekeeping field please advice value put issuer issuer exist found mhow proceed ask tought migration tought issuer available,0.010399304330348969,16.0,125,0.12451447144412296,0.24740017391741276,2.***************,0.*****************,0.1,0.06,25.17%
Incident,OEKB-6615,Critical,+++ URGENT +++ M12 PROD - PROX0003212129 - Repair Instruction failed,Bertram Schon,2025-01-15 08:48:00,2025-01-15 08:53:00,NO,"PROX0003212129 
We received seev.004 which moved to status “Invalid Data” (Entitlement Fixing Date not reached yet). 
When trying to repair, this action failed (after pressing “Repair”-button, nothing happened, status stays the same). 
Moreover, we noticed that when checking “HisRuleError”, the below error (screenshot) appeared. 

Please urgently check as we have to repair the CI and send MI asap. 

Thanks, Bert",prox received seev moved status invalid data entitlement fixing date reached yet trying repair action failed pressing repairbutton nothing happened status stay moreover noticed checking hisruleerror error screenshot appeared please urgently check repair ci send mi asap thanks bert,-0.35797858983278275,0.0,125,0.12451447144412296,0.3394946474581957,5.****************,0.2,0.15,0.15,75.92%
Incident,OEKB-6614,Major,M12 PROD - - Error in the Generated Schedule AT0000A26861,Dalibor VIDIC,2025-01-15 08:43:00,2025-01-15 08:57:00,NO,"Hello, 

Please check why the 'Loan Schedule' was not created correctly for the AT0000A26861. 

The next coupon date should be the 12th of February 2025 and the last coupon date was the 12th of November 2024 but M12 calculates the coupon from 13.11.2024. 



Thank you and BR, Dali",hello please check loan schedule created correctly ata next coupon date th february last coupon date th november calculates coupon thank br dali,0.008572712540626526,0.0,125,0.12451447144412296,0.24785682186484337,3.***************,0.*****************,0.13,0.15,44.94%
Incident,OEKB-6610,Major,M12 PROD - INTR0003211997 and INTR0003211988 update not possible URGENT,Dalibor VIDIC,2025-01-13 14:50:00,2025-01-13 15:00:00,YES,"Hello, 

Unfortunately, it was not possible to update the two ISINs mentioned above. Please check this urgently. 



Thank you and BR, Dalibor",hello unfortunately possible update two isins mentioned please check urgently thank br dalibor,0.019001144915819168,0.0,126,0.1224564282529819,0.2452497137710452,3.***************,0.*****************,0.13,0.15,44.55%
Incident,OEKB-6609,Major,M12 PROD - AT0000A1PHY1 - Error in the Generated Schedule,Dalibor VIDIC,2025-01-13 14:17:00,2025-01-13 14:28:00,NO,"Hello, 

Please check why the 'Loan Schedule' was not created correctly for the above mentioned ISIN. 

The next coupon date should be the 11th of February 2025 and the last coupon date was the 11th of November 2024. 

However, M12 calculates the coupon from 13.11.2024. 

Please check\! 



Thank you and BR, Dalibor",hello please check loan schedule created correctly mentioned isin next coupon date th february last coupon date th november however calculates coupon please check thank br dalibor,-0.053846390917897224,0.0,126,0.1224564282529819,0.2634615977294743,3.***************,0.*****************,0.13,0.15,47.28%
Incident,OEKB-6606,Medium,M12 PROD - Failed Received MT564,Dalibor VIDIC,2025-01-13 08:34:00,2025-01-13 08:37:00,NO,"Hello, 

on 10 January (23:58), we received three MT564REPE messages with an error message. The error message is not clear to us. 



Please check\! 



Thank you and BR, Dalibor",hello january received three mtrepe message error message error message clear u please check thank br dalibor,-0.****************,0.0,127,0.*****************,0.*****************,3.***************,0.*****************,0.1,0.15,58.79%
Information Request,OEKB-6604,Medium,Some duplicates in Clients,Nikolay TSONKOV,2025-01-10 09:32:00,2025-01-13 07:39:00,NO,"Hi Emna, 

i noticed some clients based on the name are 2 times available in PROD. Both entries are active, the difference is in field identifier . in case of duplicate , one of the entry is having the account number and the other entry is having the SWIFT code in tht field 



i think the diffrence comes from the fact that initially the client came via the client interface defined with SWIFT code in field Identifier and then after some time the value was changed to Account Number 

e.g you can see the both customer files used by the interface that LGT BANK AG in FIeld custNumber in the file as per 08.01.2025 differs in comparison with the same file as per 20.11.2024 



in the print screen you can see there are also some other similar cases 

how to treat those duplicates? 

i checked if i search for client eligible position only the client is available in the search field 

BR, 

Niki",hi emna noticed client based name time available prod entry active difference field identifier case duplicate one entry account number entry swift code tht field think diffrence come fact initially client came via client interface defined swift code field identifier time value changed account number eg see customer file used interface lgt bank ag field custnumber file per differs comparison file per print screen see also similar case treat duplicate checked search client eligible position client available search field br niki,-0.005506109446287155,2.0,130,0.*****************,0.****************,2.***************,0.*****************,0.1,0.06,25.76%
Incident,OEKB-6603,Medium,IGNORE Button is missing now for several dashboard alerts,Wallmann GEROLD,2025-01-10 08:48:00,2025-01-10 11:00:00,NO,"For several Dashboard alerts the IGNORE button is now missing. 

It seems to me that this issue appears since the new version was installed on 07.01.2025 

All Dashboard alerts under LOAN Schedule: 

* INTP Missing 
* REDM price missing 
* MCAL price missing 
* PCAL price/rate missing 
* PRED price/rate/factor missing 
* PCAL verification 
* PRED verification 
* No INTP Payment 



Dashboard alert under Client Entitlement 

* Client Entitlement Not Calculated 



Please see some samples attached\! 



Please can you check and repair. 





",several dashboard alert ignore button missing seems issue appears since new version installed dashboard alert loan schedule intp missing redm price missing mcal price missing pcal pricerate missing pred priceratefactor missing pcal verification pred verification intp payment dashboard alert client entitlement client entitlement calculated please see sample attached please check repair,-0.5402081105858088,0.0,130,0.*****************,0.3850520276464522,1.****************,0.*****************,0.1,0.15,54.46%
Incident,OEKB-6602,Medium,M12 PROD - MEET events - New events created due to incoming MT564 REPL although events with same Market Ref. are already cancelled/closed,Bertram Schon,2025-01-10 08:37:00,2025-01-10 08:39:00,NO,"Please note that there are several MEET events which were set up due to incoming MT564 NEWM (example attached) and already closed. 

For these closed events, we yesterday received MT564 REPL (attached), each *with the same CORP.* 

These messages should fail with error: 
*Received CA feed with main reference …. fail with reason : Closed/ Cancelled Event exists for CA Market Reference: \{\{marketReference}}* 
and should not create a new event. 

However, due to these REPL messages, new events have been created (see screenshot). 

Could you please check? 
Thanks, Bert 

",please note several meet event set due incoming mt newm example attached already closed closed event yesterday received mt repl attached corp message fail error received ca feed main reference fail reason closed cancelled event exists ca market reference marketreference create new event however due repl message new event created see screenshot could please check thanks bert,-0.12566128745675087,0.0,130,0.*****************,0.2814153218641877,5.****************,0.2,0.1,0.15,59.71%
Incident,OEKB-6601,Medium,M12 PROD - PROX0003213163 - alert with over 10000 results still appears in PROX events,Ana Maria Galic,2025-01-10 08:27:00,2025-01-10 08:29:00,NO,"Hi, 

same issue as in OEKB-6484 still appears in PROX events. 
For other events the popup does not appear anymore after clicking on “compare draft”. 
Can you please check an apply the same for PROX events? 

Thanks, 
BR Ana",hi issue oekb still appears prox event event popup appear anymore clicking compare draft please check apply prox event thanks br ana,-0.014783207327127457,0.0,130,0.*****************,0.25369580183178186,1.****************,0.037820429797300145,0.1,0.15,31.23%
Incident,OEKB-6600,Medium,M12 PROD - PROX0003213174 - seev.001 NEWM not sent to WM (DEF002),Bertram Schon,2025-01-09 11:33:00,2025-01-09 11:36:00,NO,"I have again an issue with seev.001 sent to DEF002 (WMDEFF). 
Today, the system generates a NEWM to DEF002 although the event was not cross-checked at this time (see screenshot 1). 
However, this message was not sent at all (not available in MegaBroker). 

After cross-check/update of event, seev.001 was sent to clients as NEWM and to DEF002 as REPL (see screenshot 2). 

Since no NEWM should be sent if event is not cross-checked, could you please check the configuration for DEF002 and make sure that notifications are sent (as NEWM), only after event activation? 

Thanks, Bert",issue seev sent def wmdeff today system generates newm def although event crosschecked time see screenshot however message sent available megabroker crosscheckupdate event seev sent client newm def repl see screenshot since newm sent event crosschecked could please check configuration def make sure notification sent newm event activation thanks bert,0.009595194831490517,0.0,130,0.*****************,0.24760120129212737,5.****************,0.2,0.1,0.15,54.64%
Incident,OEKB-6598,Medium,"M12 PROD - PROX0003213113, PROX0003213108, XMET0003213109, MEET0003213107 - New events created due to incoming seev.001/MT564 REPL although events already exists with same Ref.Date",Bertram Schon,2025-01-08 11:29:00,2025-01-08 11:39:00,NO,"Same issue as reported in OEKB-6556 (which was retested successfully in QAS). 

Could you please check\! 
Thanks, Bert",issue reported oekb retested successfully qas could please check thanks bert,0.030630532652139664,0.0,131,0.11266535284961249,0.24234236683696508,5.****************,0.2,0.1,0.15,53.85%
Incident,OEKB-6597,Major,M12 PROD - PROX0003213006 - seev.001 OUT REPL failed in MegaBroker,Bertram Schon,2025-01-08 10:57:00,2025-01-08 11:01:00,NO,"PROX0003213006 
We want to send seev.001 REPL (“Authorised Proxy”-details were added - see screenshot) but messages failed in MegaBroker with error (FlowIn attached): 


*_*****Message Generation Failed:_* 
*_PALM-15053: can't find the mandatory FieldDescription with name :  Id and alias :  Document.MtgNtfctn.Mtg.PrxyChc.PrxyChc_Choice_0.Prxy.AuthrsdPrxy.PrsnDtls.PrssgndPrxy.PrssgndPrxy_Choice_0.NtrlPrsn.Id.Id in the elements to write._* 

Could you please check? 
Thanks, Bert 


",prox want send seev repl authorised proxydetails added see screenshot message failed megabroker error flowin attached message generation failed palm cant find mandatory fielddescription name id alias documentmtgntfctnmtgprxychcprxychcchoiceprxyauthrsdprxyprsndtlsprssgndprxyprssgndprxychoicentrlprsnidid element write could please check thanks bert,-0.0751834325492382,0.0,131,0.11266535284961249,0.26879585813730955,5.****************,0.2,0.13,0.15,62.32%
Incident,OEKB-6596,Critical,Pending Trades Batch #5 von 07.01.2025 nicht hochgeladen und verarbeitet,Nikolay TSONKOV,2025-01-08 08:56:00,2025-01-08 09:00:00,NO,"Dear colleagues, 

we noticed that the Pending Trades Batch #5 from 07.01.2025 is not uploaded and processed 



pls for support",dear colleague noticed pending trade batch uploaded processed pls support,0.308968260884285,0.0,132,0.11080315836233387,0.17275793477892876,2.***************,0.*****************,0.15,0.15,34.97%
Incident,OEKB-6593,Major,M12 PROD - Client 203100 - no LEI migrated although available in M10,Bertram Schon,2025-01-03 09:01:00,2025-01-03 09:17:00,NO,"Today I found out that there is no LEI (which is mandatory for sending seev.047) available in M12 for client 203100 (see screenshot 1, only BIC is available), although LEI is available in M10 (see screenshot 2). 

Could you please check and advise why the LEI was not migrated and also please explain how to add the LEI manually, as I’m lost in the EDIT screen (screenshot 3). 

Thanks\! 
BR Bert",today found lei mandatory sending seev available client see screenshot bic available although lei available see screenshot could please check advise lei migrated also please explain add lei manually im lost edit screen screenshot thanks br bert,0.03616270609200001,0.0,137,0.10194382697455369,0.240959323477,5.****************,0.2,0.13,0.15,58.14%
Information Request,OEKB-6592,Major,M12 PROD - INTR0003208655 - wrong Payment Date,Dalibor VIDIC,2025-01-03 08:17:00,2025-01-03 08:35:00,NO,"Hello, 

The correct payment date for the INTR Payment for the event above is 06/01/2025. But an automatic update (27.12.2024) changed the payment date to 07/01/2025 without the event having the status ‘Blocked critical’. 

Please check\! 

Thank you and BR, Dalibor 

",hello correct payment date intr payment event automatic update changed payment date without event status blocked critical please check thank br dalibor,-0.025494378060102463,0.0,137,0.10194382697455369,0.2563735945150256,3.***************,0.*****************,0.13,0.06,32.72%
Incident,OEKB-6591,Medium,M12 PROD - To Do List - WRTC events should not be listed under Corporate Action Events,Bertram Schon,2025-01-02 14:24:00,2025-01-02 14:33:00,NO,"Same issue detected as reported with OEKB-6464 - 
WRTC events should not be available in To Do List under “CORPORATE ACTION MANIPULATION”, but still are (detected today under “Events with incoming comments” (see screenshot). 

Could you please move it to ""DIVIDEND EVENT MANIPULATION""? 

Thanks, Bert 

",issue detected reported oekb wrtc event available list corporate action manipulation still detected today event incoming comment see screenshot could please move dividend event manipulation thanks bert,-0.09435121715068817,0.0,137,0.10194382697455369,0.27358780428767204,5.****************,0.2,0.1,0.15,58.54%
Incident,OEKB-6590,Medium,"M12 PROD - PROX Events - ""Announcement Date"" not mapped",Bertram Schon,2025-01-02 13:44:00,2025-01-02 13:46:00,NO,"We noticed that the “Announcement Date” received from seev.001 IN (full message attached) => 

*<AnncmntDt>* 
*<Dt>2025-01-02</Dt>* 
*</AnncmntDt>* 

is not available in the event data (see screenshot) and seev.001 OUT. 

Could you please check and map this date accordingly? 

Many thanks, Bert 



",noticed announcement date received seev full message attached anncmntdt dtdt anncmntdt available event data see screenshot seev could please check map date accordingly many thanks bert,0.02147287130355835,0.0,137,0.10194382697455369,0.2446317821741104,5.****************,0.2,0.1,0.15,54.19%
Incident,OEKB-6589,Major,M12 PROD - DVCA0003211225 - Update not possible - URGENT,Dalibor VIDIC,2025-01-02 10:50:00,2025-01-02 10:56:00,NO,"Hello,  

the update is not possible in the above-mentioned DVCA Event, as the update is not saved. 

Please check urgently\! 



Thank you and BR, Dalibor",hello update possible abovementioned dvca event update saved please check urgently thank br dalibor,0.02282620407640934,0.0,137,0.10194382697455369,0.24429344898089767,3.***************,0.*****************,0.13,0.15,44.41%
Incident,OEKB-6588,Medium,AGAIN PCAL events were created wrongly see Ticket 6461,Wallmann GEROLD,2025-01-02 10:32:00,2025-01-02 10:52:00,NO,"We had again the the same issue for three ISINs - 

PCAL event was created - but it is MCAL event. 

See attachment for ISINS concerned. 



As workaround I closed the wrong PCAL events. 

Please can you check and repair. 

Thank you 



Gerold 

",issue three isins pcal event created mcal event see attachment isins concerned workaround closed wrong pcal event please check repair thank gerold,-0.****************,0.0,138,0.*****************,0.*****************,1.****************,0.*****************,0.1,0.15,47.55%
Information Request,OEKB-6587,Major,For BPUT00013607 - NOAC was created by error,Wallmann GEROLD,2025-01-02 09:27:00,2025-01-02 09:30:00,NO,"For BPUT00013607 / ISIN AT0000494984 

There is a CASH Instruction for 280 shs for account 227210 

This is equal to the total holding for this account 227210 

This means the client instructed for the whole position on account 227210 CASH\! 

But after deadline the default option “NOAC” was created for this account 227210 and swift MT567 was sent to client with NOAC - this is not correct - as the correct action is CASH \! 



Please can you check and repair. 

",bput isin cash instruction shs account equal total holding account mean client instructed whole position account cash deadline default option noac created account swift mt sent client noac correct correct action cash please check repair,-0.****************,0.0,138,0.*****************,0.****************,1.****************,0.*****************,0.13,0.06,28.57%
Incident,OEKB-6586,Major,No swift MT566 for adhoc Booking was sent,Wallmann GEROLD,2025-01-02 09:18:00,2025-01-02 09:34:00,NO,"For ISIN US30303M1027 / main ref = DVCA0003211617 I created a Adhoc Cash Booking for client 222100 on 31.12.2024 

The booking is in the Status Created - but no swift MT566 was sent. 

It is also not possible to send the swift MT566 manually (There is the message that no new message is generated). 

Please check and repair. 



",isin usm main ref dvca created adhoc cash booking client booking status created swift mt sent also possible send swift mt manually message new message generated please check repair,0.*****************,0.0,138,0.*****************,0.*****************,1.****************,0.*****************,0.13,0.15,37.17%
Incident,OEKB-6585,Medium,"M12 PROD - ""Remove Invalid Disclosure Response"" failed",Bertram Schon,2025-01-02 08:59:00,2025-01-02 09:01:00,NO,"When trying to remove 2 invalid disclosure responses (see screenshot 1), via “Instructions/ShareHolder Disclosure Event/Remove Invalid Disclosure Response” (screenshot 2), no results were found (see screenshot 3), so, I was not able to remove themed that there are no results. 


In addition, I found out that there is another ticket (OEKB-6283) deadling with the “Remove Invalid Disclosure Response”-functionality, but I guess this was a different bug (ticket can be closed after fixing of this new one). 


Could you please check\! 
Thanks, Bert",trying remove invalid disclosure response see screenshot via instructionsshareholder disclosure eventremove invalid disclosure response screenshot result found see screenshot able remove themed result addition found another ticket oekb deadling remove invalid disclosure responsefunctionality guess different bug ticket closed fixing new one could please check thanks bert,-0.10471880622208118,0.0,138,0.*****************,0.2761797015555203,5.****************,0.2,0.1,0.15,58.93%
Incident,OEKB-6584,Medium,Monitor US-TAX Datapool - not possible to input currency for reportable amount,Wallmann GEROLD,2025-01-02 08:56:00,2025-01-02 09:05:00,NO,"I tried to create a entry in the US-TAX data pool. 

But it is not possible to input the payment currency for the reportable amount. 

Anyway - this currency for the reportable amounts must always be USD. 

Additionally the fields Reportable Gross Amount, Reportable Net Amount and Reportable Tax amount should be editable by the user. 



Please correct.",tried create entry ustax data pool possible input payment currency reportable amount anyway currency reportable amount must always usd additionally field reportable gross amount reportable net amount reportable tax amount editable user please correct,-0.012440422549843788,0.0,138,0.*****************,0.25311010563746095,1.****************,0.*****************,0.1,0.15,34.67%
Incident,OEKB-6583,Medium,M12 PROD - Send Disclosure Response - Check Box/Send All missing,Bertram Schon,2025-01-02 08:39:00,2025-01-02 08:58:00,YES,"Could you please check the “Send Disclosure Response”-screen (screenshot below) and provide us with a “Send All”-functionality, respectively with a check box to be able to select all rows. 

Thanks, 
Bert 

",could please check send disclosure responsescreen screenshot provide u send allfunctionality respectively check box able select row thanks bert,0.02432435005903244,0.0,138,0.*****************,0.2439189124852419,5.****************,0.2,0.1,0.15,54.09%
Information Request,OEKB-6582,Major,DB Usage Growth Exponentially,Nikolay TSONKOV,2025-01-02 08:27:00,2025-01-02 08:58:00,NO,"Dears , pls lets analyse why starting oktober the DB utilisation started growing exponentially 



we are expiriencing now very offten alerts for more than 99% usage of the tablespace for both QAS and PROD 

",dear pls let analyse starting oktober db utilisation started growing exponentially expiriencing offten alert usage tablespace qas prod,0.6137659624218941,0.0,138,0.*****************,0.09655850939452648,2.***************,0.*****************,0.13,0.06,7.04%
Incident,OEKB-6581,Medium,"M12 PROD - MT564 IN - Impact failed with error ""Referential-0010: The date is null""",Bertram Schon,2024-12-31 08:55:00,2024-12-31 09:01:00,NO,"We received the attached MT564, but the message failed with error 
""Referential-0010: The date is null"". 

Could you please check? 
Thanks, Bert",received attached mt message failed error referential date null could please check thanks bert,-0.046869225800037384,0.0,140,0.09697196786440505,0.26171730645000935,5.****************,0.2,0.1,0.15,56.76%
Incident,OEKB-6580,Medium,"M12 PROD - Update Fraction Price - ""Ignore"" does not work",Bertram Schon,2024-12-30 08:31:00,2024-12-30 08:34:00,YES,"When trying to ignore items of the “Update Fraction Price”-list, it turned out that, after clicking the “Ignore”-button, there is no action - items still remain in the list, even after refreshing. 

Could you please check? 
Thanks, Bert 


",trying ignore item update fraction pricelist turned clicking ignorebutton action item still remain list even refreshing could please check thanks bert,-0.046423496678471565,0.0,141,0.09536916221554961,0.2616058741696179,5.****************,0.2,0.1,0.15,56.74%
Information Request,OEKB-6579,Medium,MegaBroker User Rights,Nikolay TSONKOV,2024-12-27 12:41:00,2024-12-27 14:08:00,NO,"Dear colleagues, 

i have noticed that me and Lukas we have in MegBroker usergroup VermegAdmin, where Sonja hat usergroup CSD Admin 



i think we do not need the VermegAdmin rights me and Lukasz, can we agree that i set my and Lukasz User to have CSD ADmin Rights in MB PROD ? 

",dear colleague noticed lukas megbroker usergroup vermegadmin sonja hat usergroup csd admin think need vermegadmin right lukasz agree set lukasz user csd admin right mb prod,0.022890491411089897,0.0,143,0.09224258918435976,0.24427737714722753,2.***************,0.*****************,0.1,0.06,24.7%
Information Request,OEKB-6577,Medium,how can filter out tickets based on the Fix Version,Nikolay TSONKOV,2024-12-23 15:27:00,2024-12-30 08:27:00,NO,"Dear coleagues, 

pls help me setting up my filter 

i try to filter out based on 

Fix Version column but somehow i do not get the results 



you can see in my screen that i see tickets where in Column Fix Versions/s is set Hotfix 13/12/2024 

however when i try to search based on this column no results are found 



",dear coleagues pls help setting filter try filter based fix version column somehow get result see screen see ticket column fix versionss set hotfix however try search based column result found,-0.01681344583630562,6.0,147,0.0862935864993705,0.2542033614590764,2.***************,0.*****************,0.1,0.06,26.19%
Incident,OEKB-6576,Medium,"M12 PROD - DVOP0003209813 - MP Status still ""WaitingCashBookingConfirmation""",Bertram Schon,2024-12-23 10:03:00,2024-12-23 10:41:00,NO,"DVOP0003209813 
MP was validated and is now still in status “WaitingCashBookingConfirmation”. 

When checking MP details, it seems that the payment was already done 20 minutes ago, but the Payment Status was not updated\!? 

Could you please check\! 
Thanks, Bert 


",dvop mp validated still status waitingcashbookingconfirmation checking mp detail seems payment already done minute ago payment status updated could please check thanks bert,0.010996125638484955,0.0,148,0.0848672789700174,0.24725096859037876,5.****************,0.2,0.1,0.15,54.59%
Information Request,OEKB-6575,Medium,3i Messages not received in MegaCor,Nikolay TSONKOV,2024-12-23 09:52:00,2024-12-23 10:47:00,NO,"Hello, we recieved attached notification for the following messages 

i checked in MegaBroker and i see some messages indeed have error 

should we take any actions or to ignore those? 



BR, 

NIki",hello recieved attached notification following message checked megabroker see message indeed error take action ignore br niki,-0.27616577968001366,0.0,148,0.0848672789700174,0.3190414449200034,2.***************,0.*****************,0.1,0.06,35.91%
Incident,OEKB-6574,Major,M12 PROD - seev.001 failed in MegaBroker,Bertram Schon,2024-12-23 09:21:00,2024-12-23 09:24:00,NO,"Could you please urgently check why the attached seev.001 IN failed in MegaBroker\! 
I coincidentally found out that this message which was received accordingly in SWIFT Alliance, was not received in MegaCor. 

Thanks, Bert",could please urgently check attached seev failed megabroker coincidentally found message received accordingly swift alliance received megacor thanks bert,0.30463023390620947,0.0,148,0.0848672789700174,0.17384244152344763,5.****************,0.2,0.13,0.15,48.08%
Incident,OEKB-6573,Blocker,+++ URGENT +++ M12 PROD - DVOP0003209813 - Saving of update failed,Bertram Schon,2024-12-23 08:33:00,2024-12-23 08:35:00,NO,"DVOP0003209813 
I have to update event data (add final cash rates, comments…), when trying to save nothing happened and an completely different old events appeared in the screen (same bug as reported many times before). 

Could you please urgently check as I have to process payments TODAY\! 

Thanks, Bert",dvop update event data add final cash rate comment trying save nothing happened completely different old event appeared screen bug reported many time could please urgently check process payment today thanks bert,-0.025500845164060593,0.0,148,0.0848672789700174,0.25637521129101515,5.****************,0.2,0.14,0.15,61.96%
Incident,OEKB-6569,Major,M12 PROD - PCAL0003208884 Update not possible - URGENT,Dalibor VIDIC,2024-12-18 14:35:00,2024-12-18 14:38:00,NO,"Hello, 

The above-mentioned PCAL event cannot be updated. 

Please check\! 



Thank you and BR, Dalibor",hello abovementioned pcal event updated please check thank br dalibor,0.015521122142672539,0.0,152,0.07939393227707822,0.24611971946433187,3.***************,0.*****************,0.13,0.15,44.68%
Incident,OEKB-6568,Major,M12 PROD - DVCA0003211329 - Client Payment WaitingCashBookingConfirmation,Dalibor VIDIC,2024-12-18 14:06:00,2024-12-18 14:25:00,NO,"Hello, 

There are stock exchange claims in the above-mentioned DVCA event. 

Unfortunately, two client bookings have the status “WaitingCashBookingConfirmation” 

Please check\! 



Thank you and BR, Dalibor",hello stock exchange claim abovementioned dvca event unfortunately two client booking status waitingcashbookingconfirmation please check thank br dalibor,-0.019769329577684402,0.0,152,0.07939393227707822,0.2549423323944211,3.***************,0.*****************,0.13,0.15,46.01%
Incident,OEKB-6567,Major,+++ URGENT +++ M12 PROD - DVSE0003211480 - Client Payments CASH OUT automatically generated in error,Bertram Schon,2024-12-18 11:00:00,2024-12-18 11:05:00,NO,"DVSE0003211480 
was set up including Tax Movement. 
We generated MP *SECU* manually, after the MP was created, we noticed that Client Payments SECU were generated accordingly, however, *also Client Payments CASH OUT were generated in error\!* 

Could you please check this behavior urgenty\! 

Thanks, 
Bert",dvse set including tax movement generated mp secu manually mp created noticed client payment secu generated accordingly however also client payment cash generated error could please check behavior urgenty thanks bert,0.0016172640025615692,0.0,152,0.07939393227707822,0.2495956839993596,5.****************,0.2,0.13,0.15,59.44%
Incident,OEKB-6566,Major,"+++ URGENT +++ M12 PROD - DVSE0003211480 - Client Payments stuck in Status ""WaitingCancellation"" but must be reversed",Bertram Schon,2024-12-18 10:58:00,2024-12-18 11:03:00,NO,"DVSE0003211480 
CASH OUT Client Payments were automatically generated for unknown reasons (separate ticket will be created). 
We wanted to cancel those before they settle in T2S using the “CancelT2SInstruction”. 
Unfortunately, the instructions settle in T2S (canc request was obviously sent too late). 
Now the CP are still in status “WaitingCancellation”, but we must reverse the payments. 

Could you please explain how we can proceed now? 

Thanks, Bert",dvse cash client payment automatically generated unknown reason separate ticket created wanted cancel settle t using canceltsinstruction unfortunately instruction settle t canc request obviously sent late cp still status waitingcancellation must reverse payment could please explain proceed thanks bert,-0.01559276133775711,0.0,152,0.07939393227707822,0.2538981903344393,5.****************,0.2,0.13,0.15,60.08%
Information Request,OEKB-6565,Major,M12 PROD - Ad Hoc Booking CASH - How to edit an event if entitlements are calculated and must not be cancelled,Bertram Schon,2024-12-17 15:29:00,2024-12-17 15:44:00,NO,"Hi, 
we sometimes face the situation that we have to process a cash payment although an event was set up with no cash movement. 
Example: 
DVSE event 1 for 1 (no cash movement set up), secu payments done, later we receive information that the event is taxable. 

In M10, we were able to amend the rounding in indicator to DOWN WITH CASH and added the currency with the EDIT-functionality - no cancellation of entitlemens had to be processed, no notifications were sent. 

In M12, we do not know about such a functionality. 

Could you please let us know how we can handle such a case. 

Thanks, Bert",hi sometimes face situation process cash payment although event set cash movement example dvse event cash movement set secu payment done later receive information event taxable able amend rounding indicator cash added currency editfunctionality cancellation entitlemens processed notification sent know functionality could please let u know handle case thanks bert,-0.0027203410863876343,0.0,153,0.07808166600115317,0.2506800852715969,5.****************,0.2,0.13,0.06,46.1%
Incident,OEKB-6564,Major,M12 PROD - DVCA0003210736 - Cancellation not possible,Dalibor VIDIC,2024-12-17 14:23:00,2024-12-17 14:27:00,NO,"Hello, 

The DVCA event mentioned above will not take place and my intention was to withdraw it. Unfortunately it is not possible as there seems to be a payment. 

There is a market payment available which is not correct as there was no payment in this ISIN. 

Please check how this payment was made and how we can cancel it. 



Thank you and BR, Dalibor 

",hello dvca event mentioned take place intention withdraw unfortunately possible seems payment market payment available correct payment isin please check payment made cancel thank br dalibor,-0.186049060896039,0.0,153,0.07808166600115317,0.29651226522400975,3.***************,0.*****************,0.13,0.15,52.24%
Incident,OEKB-6563,Medium,DASHBOARD alert: REDM loan calc error,Wallmann GEROLD,2024-12-17 12:05:00,2024-12-17 12:10:00,NO,"Dashboard alert “REDM loan calc error” under ”static data”: 

When I try to IGNORE the Dashboard alert for ISIN AT0000306691 - there is an alert popping up. 

See attachment. 

Can you please check. 

Thank you 



GEROLD 

",dashboard alert redm loan calc error static data try ignore dashboard alert isin alert popping see attachment please check thank gerold,-0.12601935863494873,0.0,153,0.07808166600115317,0.2815048396587372,1.****************,0.*****************,0.1,0.15,38.93%
Requirement,OEKB-6562,Major,"M12 PROD - seev.001 - ""Confirmation Of Holding Required"" tag to be added",Bertram Schon,2024-12-17 10:38:00,2024-12-17 13:28:00,NO,"Could you please check feasibility and cost estimation of the addition of the field ""Confirmation Of Holding Required"" to the PROX screens and mapping to seev.001 OUT. 

<NtfctnGnlInf> 
<NtfctnTp>NEWM</NtfctnTp> 
<NtfctnSts> 
<EvtCmpltnsSts>COMP</EvtCmpltnsSts> 
<EvtConfSts>CONF</EvtConfSts> 
</NtfctnSts> 
*<ConfOfHldgReqrd>false</ConfOfHldgReqrd>* 
</NtfctnGnlInf> 

Incoming seev.001 including this field attached. 

Thanks, Bert",could please check feasibility cost estimation addition field confirmation holding required prox screen mapping seev ntfctngnlinf ntfctntpnewmntfctntp ntfctnsts evtcmpltnsstscompevtcmpltnssts evtconfstsconfevtconfsts ntfctnsts confofhldgreqrdfalseconfofhldgreqrd ntfctngnlinf incoming seev including field attached thanks bert,0.015719113871455193,0.0,153,0.07808166600115317,0.2460702215321362,5.****************,0.2,0.13,0.09,49.91%
Information Request,OEKB-6558,Major,M12 PROD/QAS - seev.004 failed for unknown reasons,Bertram Schon,2024-12-13 13:09:00,2024-12-13 13:40:00,NO,"Could you please check the following error in MegaBroker. 
I imported this seev.004: 
[^FlowIn_26280070_seev004_241213_01.txt] 
including element type ""GblVoteInstr"": 

!image-2024-12-13-14-09-01-124.png! 

Although, the end-tag is available, the following error popped up in MegaBroker: 

!image-2024-12-13-14-09-49-228.png! 

Please check! 
Thanks, Bert",could please check following error megabroker imported seev flowinseevtxt including element type gblvoteinstr imagepng although endtag available following error popped megabroker imagepng please check thanks bert,-0.011521637439727783,0.0,157,0.07304594394698456,0.25288040935993195,5.****************,0.2,0.13,0.06,46.43%
Incident,OEKB-6556,Medium,M12 PROD - PROX0003211776 - New event created due to incoming seev.001 REPL although event already exists with same Ref.Date,Bertram Schon,2024-12-13 10:36:00,2024-12-13 10:42:00,NO,"Today, we received seev.001 REPL from our custodian for the existing meeting event (MainRef. PROX0003211746) with Ref.Date 22/12/2024: 

[^FlowIn_120494512.ID_414d512062697a74616c6b2e71756575a15e38672ddffe21] 

However, this message was not assigned to the correct event (PROX0003211746) but led to the creation of a new event (PROX0003211776) which is not understandable for us => 


!image-2024-12-13-11-42-03-697.png! 



Could you please check this behavior? 

Thanks, Bert",today received seev repl custodian existing meeting event mainref prox refdate flowiniddacbeaeddffe however message assigned correct event prox led creation new event prox understandable u imagepng could please check behavior thanks bert,0.056396616622805595,0.0,158,0.07183860068931329,0.2359008458442986,5.****************,0.2,0.1,0.15,52.89%
Incident,OEKB-6555,Critical,+++ URGENT +++ M12 PROD - PROX0003211689 - <IssrDdlnForVtng> must be filled (even if unknown),Bertram Schon,2024-12-12 15:20:00,2024-12-12 15:30:00,NO,"PROX0003211689  
<IssrDdlnForVtng> was received in seev.001 with value UKWN => 


!image-2024-12-12-16-18-14-326.png! 


However, this field was not mapped into the outgoing seev.001 which is an error due to the fact that it is a mandatory field => 

!image-2024-12-12-16-19-43-564.png! 

Could you please urgently provide a hot fix, we need to send correct seev.001 to clients! 

Thanks, Bert",prox issrddlnforvtng received seev value ukwn imagepng however field mapped outgoing seev error due fact mandatory field imagepng could please urgently provide hot fix need send correct seev client thanks bert,0.25693976879119873,0.0,158,0.07183860068931329,0.18576505780220032,5.****************,0.2,0.15,0.15,52.86%
Information Request,OEKB-6553,Critical,"+++ URGENT +++ M12 PROD - PROX0003211689 - seev.001 OUT - <PrcgTxtForNxtIntrmy> contains ""&#10"" for unknown reasons",Bertram Schon,2024-12-12 10:58:00,2024-12-12 11:06:00,NO,"PROX0003211689  
We have sent seev.001 to clients containing the following <PrcgTxtForNxtIntrmy> text (which was added manually) => 

!image-2024-12-12-11-51-55-532.png! 

When checking FlowOut 
[^flowOut_120412058.xml] 
the text looks fine => 

!image-2024-12-12-11-54-02-052.png! 


However, when checking  
FlowIn 
[^FlowIn_120490638.xml] 
we noticed that the text contains *{color:#de350b}""&#10""{color}* (maybe instead of a blank space?!) =>  


!image-2024-12-12-11-55-32-773.png! 



This may lead to problems for our clients when trying to process the message!? 

Could you please urgently check? 

Thanks, Bert",prox sent seev client containing following prcgtxtfornxtintrmy text added manually imagepng checking flowout flowoutxml text look fine imagepng however checking flowin flowinxml noticed text contains colordebcolor maybe instead blank space imagepng may lead problem client trying process message could please urgently check thanks bert,-0.2193375676870346,0.0,158,0.07183860068931329,0.30483439192175865,5.****************,0.2,0.15,0.06,57.23%
Incident,OEKB-6552,Major,M12 PROD - PROX0003211689 - RegnSctiesDdln/RegnSctiesMktDdln TIME is missing,Bertram Schon,2024-12-12 09:04:00,2024-12-12 09:17:00,NO,"PROX0003211689  
was set up according to incoming seev.001 (attached) including RegnSctiesDdln/RegnSctiesMktDdln (date and time) => 


!image-2024-12-12-10-02-25-447.png! 

When checking seev.001 OUT, we noticed that the time is missing in both fields => 

!image-2024-12-12-10-03-49-998.png! 



As time is an important information, please check and add it to the mapping! 

Thanks, Bert",prox set according incoming seev attached including regnsctiesddlnregnsctiesmktddln date time imagepng checking seev noticed time missing field imagepng time important information please check add mapping thanks bert,-0.010081158950924873,0.0,159,0.0706512130604296,0.2525202897377312,5.****************,0.2,0.13,0.15,59.88%
Incident,OEKB-6551,Major,M12 PROD - DVCA Event - Input FX Rate not clear URGENT,Dalibor VIDIC,2024-12-11 13:53:00,2024-12-11 14:04:00,NO,"Hello, 

There is a currency conversion in the DVCA0003210365 

The dashboard alert appeared accordingly, but I noticed the following. 

It looks as if the request will be split between the individual clients instead of the total amount as it was before. 

This means that in our case we have to enter the exchange rate four times instead of just once? 

And if we have 20 clients in an event then we have to enter the exchange rate 20 times? 

Unfortunately, this is not possible for us, as after entering the exchange rate we have to check that the amount converted to EUR is the same as the amount credited to us by the Custodian. 

  

Please check! 

  

Thank you and BR, Dalibor",hello currency conversion dvca dashboard alert appeared accordingly noticed following look request split individual client instead total amount mean case enter exchange rate four time instead client event enter exchange rate time unfortunately possible u entering exchange rate check amount converted eur amount credited u custodian please check thank br dalibor,0.012919370085000992,0.0,159,0.0706512130604296,0.24677015747874975,3.***************,0.*****************,0.13,0.15,44.78%
Incident,OEKB-6549,Medium,Missing MT566 from issuer platfrom in MC,Nikolay TSONKOV,2024-12-11 10:41:00,2024-12-12 17:15:00,NO,"Hi Niki and Omar, 

  

Today we swift a lot of swifts MT566 from 3i in M12. Everything was ok. 

  

But one swift MT566 is missing in M12 – for ISIN AT0000A28FL2 

  

This is the swift out of 3i: 

  

{1:F01HYPNATWWAXXX6965000207}\{2:O5660000000000ISPOATXXXXXX00000000000000000000N}{3:\{113:0001}{108:2412110000968323}}{4: 

:16R:GENL 

:20C::CORP//INTR0003206392 

:20C::SEME//20241211S0002153 

:20C::COAF//INTR0003206392 

:23G:NEWM 

:22F::CAEV//INTR 

:16S:GENL 

:16R:USECU 

:95P::ACOW//OCSDATWWXXX 

:97A::SAFE//OCSD9232100 

:35B:ISIN AT0000A28FL2 

V HYPO-WOHN WA19-30/11VBG 

:16R:FIA 

:11A::DENO//EUR 

:98A::COUP//20241211 

:98A::MATU//20300911 

:98A::ISSU//20190911 

:92A::INTR//3,462 

:16S:FIA 

:93B::CONB//FAMT/3000000,0 

:16S:USECU 

:16R:CADETL 

:98A::XDTE//20241211 

:98A::RDTE//20241210 

:69A::INPE//20240911/20241210 

:92A::INTR//3,462 

:16S:CADETL 

:16R:CACONF 

:13A::CAON//001 

:22H::CAOP//CASH 

:16R:CASHMOVE 

:22H::CRDB//CRED 

:19B::PSTA//EUR26253,5 

:98A::POST//20241211 

:98A::VALU//20241211 

:98A::PAYD//20241211 

:16S:CASHMOVE 

:16S:CACONF 

-} 

  

  

Please can you check, what happened with this swift MT566 and why it didn´t reach M12? 

  

Thank you 

  

Gerold",hi niki omar today swift lot swift mt everything ok one swift mt missing isin atafl swift fhypnatwwaxxxoispoatxxxxxxn rgenl ccorpintr csemes ccoafintr gnewm fcaevintr sgenl rusecu pacowocsdatwwxxx asafeocsd bisin atafl v hypowohn wavbg rfia adenoeur acoup amatu aissu aintr sfia bconbfamt susecu rcadetl axdte ardte ainpe aintr scadetl rcaconf acaon hcaopcash rcashmove hcrdbcred bpstaeur apost avalu apayd scashmove scaconf please check happened swift mt didnt reach thank gerold,0.021966608241200447,1.0,159,0.0706512130604296,0.2445083479396999,2.***************,0.*****************,0.1,0.15,38.23%
Incident,OEKB-6548,Major,M12 PROD - Wrong Payment date for some ISIN`s on 06.01.2025 - URGENT,Dalibor VIDIC,2024-12-10 15:56:00,2024-12-10 16:00:00,NO,"Hello, 

For the ISINs listed below with the Target ""No"", the payment date in M12 is 06.01.2025 (Epiphany = Holiday in Austria) instead of 07.01.2025 

AT0000A0T7F7 

AT0000A1Z7U7 

AT000B036942 

AT000B067236 

AT000B067244 

Please check as soon as possible the reason for the wrong payment date! 

  

Thank you and BR, Dalibor",hello isins listed target payment date epiphany holiday austria instead atatf atazu atb atb atb please check soon possible reason wrong payment date thank br dalibor,-0.010366460308432579,0.0,160,0.06948345122280154,0.25259161507710814,3.***************,0.*****************,0.13,0.15,45.65%
Incident,OEKB-6546,Major,M12 PROD - Conflicting updates - Error message,Dalibor VIDIC,2024-12-10 08:55:00,2024-12-10 09:01:00,NO,"Hello, 

The two REDM events listed below are in the 'Blocked Cricital' status because the redemption price is being updated. 

REDM0003203295 

REDM0003203290 

However, if you try to validate it, an error message appears. 

  

Please check! 

  

Thank you and BR, Dalibor",hello two redm event listed blocked cricital status redemption price updated redm redm however try validate error message appears please check thank br dalibor,-0.11227755807340145,0.0,161,0.06833499079063209,0.27806938951835036,3.***************,0.*****************,0.13,0.15,49.47%
Incident,OEKB-6545,Major,M12 PROD - Events automatically closed,Dalibor VIDIC,2024-12-09 12:44:00,2024-12-09 12:48:00,NO,"Hello,  

we have noticed that migrated events with the status CA Status ""Waiting Payment"" are now automatically closed, which should not be the case. 

Only events with a CA status ""Confirmed"" or ""Cancelled"" should be automatically (by automatic job) transformed to a GR status ""Closed"" after 90 target days.  

  

The following Events are affected: 

INTR00155198 

INTR00164703 

INTR00152640 

INTR00152372 

Please change the status of these events back to ""Activated"" and ""Waiting Payment"". 

  

Thank you and BR, Dalibor 

  

 ",hello noticed migrated event status ca status waiting payment automatically closed case event ca status confirmed cancelled automatically automatic job transformed gr status closed target day following event affected intr intr intr intr please change status event back activated waiting payment thank br dalibor,-0.06922466866672039,0.0,161,0.06833499079063209,0.2673061671666801,3.***************,0.*****************,0.13,0.15,47.86%
Incident,OEKB-6544,Major,M12 PROD - Update Issue INTR00159408,Dalibor VIDIC,2024-12-06 14:30:00,2024-12-06 14:34:00,NO,"Hello,  

I have updated the price (3 => 3,18) for the above mentioned event but you can't see the price update in the Compare screen.  

Please note that the person who does the validation must always see what the update has been. 

After validation, the price (3.18) is displayed in the event. 

Please check! 

  

Thank you and BR, Dalibor",hello updated price mentioned event cant see price update compare screen please note person validation must always see update validation price displayed event please check thank br dalibor,-0.0008343420922756195,0.0,164,0.06500225396303454,0.2502085855230689,3.***************,0.*****************,0.13,0.15,45.3%
Incident,OEKB-6543,Major,M12 PROD - Reconciliation Mismatch with Market Cash Payment - URGENT,Dalibor VIDIC,2024-12-06 11:01:00,2024-12-06 11:08:00,NO,"Hello,  

although the market payment was successful, no client payment was made in the following cases: 

INTR0003210024 

INTR00166187 

INTR00166191 

INTR00166218 

INTR00166206 

INTR00166207 

INTR00166204 

INTR00166199 

INTR00166200 

INTR00166184 

INTR00166183 

INTR0003210028 

There is a Reconciliation Mismatch with Market Cash Payment and I believe that this is again due to the cent difference between collection letter and the market entitlement. 

  

Please check urgently! 

  

Thank you and BR, Dalibor",hello although market payment successful client payment made following case intr intr intr intr intr intr intr intr intr intr intr intr reconciliation mismatch market cash payment believe due cent difference collection letter market entitlement please check urgently thank br dalibor,0.008655335754156113,0.0,164,0.06500225396303454,0.24783616606146097,3.***************,0.*****************,0.13,0.15,44.94%
Incident,OEKB-6542,Medium,M12 PROD - SHDS0003211460 - Event not activated,Bertram Schon,2024-12-06 10:09:00,2024-12-06 10:15:00,NO,"SHDS0003211460 
was cross-checked via ""Set as Reliable""-functionality. 

However, the event was not activated automatically (which should be the case) => 

!image-2024-12-06-11-09-40-728.png! 

Could you please check? 
Thanks, Bert",shds crosschecked via set reliablefunctionality however event activated automatically case imagepng could please check thanks bert,0.02649225853383541,0.0,165,0.06392786120670757,0.24337693536654115,5.****************,0.2,0.1,0.15,54.01%
Information Request,OEKB-6541,Major,M12 PROD - Repair Received Feed - IFAS File - URGENT,Dalibor VIDIC,2024-12-05 14:39:00,2024-12-05 14:44:00,NO,"Hello,  

In the menu item 'Repair Received Feed' there are two ISINs (AT0000A25QJ9, AT0000A1TWJ3) with an error message. It is not clear to us why no DVCA events have been created here? 

It is also not possible to create a new event (using ForceCreateNewEvent). 

  

Please check! 

  

Thank you and BR, Dalibor",hello menu item repair received feed two isins ataqj atatwj error message clear u dvca event created also possible create new event using forcecreatenewevent please check thank br dalibor,-0.007479196414351463,0.0,165,0.06392786120670757,0.25186979910358787,3.***************,0.*****************,0.13,0.06,32.04%
Incident,OEKB-6540,Major,M12 PROD - PCAL0003202374 Update not possible - URGENT,Dalibor VIDIC,2024-12-05 12:05:00,2024-12-05 12:30:00,NO,"Hello, 

The event mentioned above cannot be updated. In addition, an NL ISIN is also displayed in the PCAL event!? 

  

Please check! 

  

Thank you and BR, Dalibor",hello event mentioned updated addition nl isin also displayed pcal event please check thank br dalibor,-0.007547175511717796,0.0,165,0.06392786120670757,0.25188679387792945,3.***************,0.*****************,0.13,0.15,45.55%
Information Request,OEKB-6539,Major,M12 PROD - Send RMDR (manually) does not work,Bertram Schon,2024-12-04 15:37:00,2024-12-04 15:45:00,NO,"Hi, 
we have 4 EXOF events which are not activated because of the fact that the SECU IN ISIN is not known. 

As far as I know, no RMDR are sent automatically if an event is not activated. 
Hence, we tried to send RMDR manually via the ""Send Reminder""-functionality, but this does not work. 

Could you please check and let me know how we can send RMDR in such cases. 

Thanks, Bert",hi exof event activated fact secu isin known far know rmdr sent automatically event activated hence tried send rmdr manually via send reminderfunctionality work could please check let know send rmdr case thanks bert,0.02611270546913147,0.0,166,0.06287122660066728,0.24347182363271713,5.****************,0.2,0.13,0.06,45.02%
Incident,OEKB-6538,Major,M12 PROD - EXOF0000059736 - MT564 RMDR NACKed in SWIFT Alliance,Bertram Schon,2024-12-04 15:04:00,2024-12-04 15:15:00,NO,"EXOF0000059736 
Niki noticed that MT564 RMDR from today was NACKed in SWIFT Alliance. 
Nack Error: 
!image-2024-12-04-16-00-04-057.png! 

When checking Error Codes, I found out that this is because of the missing  subsequence A1 (Linkages) which should be retrieved from the migrated M10 event, isn't it? 

Could you please check! 
Thanks, Bert",exof niki noticed mt rmdr today nacked swift alliance nack error imagepng checking error code found missing subsequence linkage retrieved migrated event isnt could please check thanks bert,-0.5618415996432304,0.0,166,0.06287122660066728,0.3904603999108076,5.****************,0.2,0.13,0.15,80.57%
Incident,OEKB-6537,Major,"M12 PROD - CHAN0003210099 - MT564 NACKed in SWIFT Alliance / Status ""Sent with Error"" = NO incorrect",Bertram Schon,2024-12-04 10:28:00,2024-12-04 10:31:00,NO,"CHAN0003210099 
Today we found out (after a client's request) that some messages have been {color:#de350b}*NOT ACKNOWLEDGED*{color} by SWIFT Alliance and therefore not have been sent to clients => 

!image-2024-12-04-11-20-34-411.png! 

!image-2024-12-04-11-21-28-965.png! 

When checking the MT564 OUT, I noticed that :70E::NAME is populated twice, I suppose, this is the reason for the NACK => 

!image-2024-12-04-11-22-15-028.png! 

SWIFT IN only includes one instance of :70E::NAME 

Moreover, I thought that M12 is receiving ACK/NACK from Alliance, is this the case? 
When checking ""List Notifications"" in Global View, the ""Sent with Error""-code ""NO"" is definitively wrong => 

!image-2024-12-04-11-25-53-022.png! 


Could you please check! 
Thanks, Bert",chan today found client request message colordebnot acknowledgedcolor swift alliance therefore sent client imagepng imagepng checking mt noticed ename populated twice suppose reason nack imagepng swift includes one instance ename moreover thought receiving acknack alliance case checking list notification global view sent errorcode definitively wrong imagepng could please check thanks bert,-0.1003841832280159,0.0,167,0.061832056628506604,0.275096045807004,5.****************,0.2,0.13,0.15,63.26%
Incident,OEKB-6536,Major,M12-I2: Validate Conflicting Updates - Redemption Rate,Dalibor VIDIC,2024-12-03 12:09:00,2024-12-03 12:15:00,NO,"Hello,  

There is an update for the two ISINs listed below. 

AT0000A2KUY3 

AT0000A20FY2 

Unfortunately, it is not clear to us what this update means and where it comes from because  

the redemption rate for both ISIN is 100%.  

  

Please check! 

  

Thank you and BR, Dalibor",hello update two isins listed atakuy atafy unfortunately clear u update mean come redemption rate isin please check thank br dalibor,-0.013969479128718376,0.0,167,0.061832056628506604,0.2534923697821796,3.***************,0.*****************,0.13,0.15,45.79%
Incident,OEKB-6535,Medium,"M12 PROD - To Do List - Corp.Act. Manipulation - ""Events with Incoming Comments"" should not include Closed Events",Bertram Schon,2024-12-03 10:55:00,2024-12-03 10:58:00,NO,"We noticed that events which are already closed, are listed in the To Do List under Corp.Action Manipulation ""Events With Incoming Comments"" => 

!image-2024-12-03-11-14-52-926.png! 

Could you please exclude closed events from this list? 
Thanks, Bert",noticed event already closed listed list corpaction manipulation event incoming comment imagepng could please exclude closed event list thanks bert,-0.1038370905444026,0.0,167,0.061832056628506604,0.27595927263610065,5.****************,0.2,0.1,0.15,58.89%
Incident,OEKB-6532,Minor,M12 PROD Alert Event Code : CAManagement-0045,Nikolay TSONKOV,2024-12-02 10:14:00,2024-12-02 10:27:00,NO,"Hello,  

we recieved following alert from PROD 
can you pls advice if we have an issue 

Event Code : CAManagement-0045 
Description : A cancellation message has been received on event ""\{0}"" with cancellation reason code ""\{1}"" and cancellation reason ""\{2}"". 
A cancellation message has been received on event ""DVCA0003210736"" with cancellation reason code ""N/A"" and cancellation reason ""N/A"". 

  

 ",hello recieved following alert prod pls advice issue event code camanagement description cancellation message received event cancellation reason code cancellation reason cancellation message received event dvca cancellation reason code na cancellation reason na,-0.7821969985961914,0.0,169,0.05980496069700671,0.44554924964904785,2.***************,0.*****************,0.07,0.15,63.89%
Incident,OEKB-6531,Major,"M12 PROD - ""Activate CA"" not possible",Dalibor VIDIC,2024-12-02 09:56:00,2024-12-02 09:59:00,NO,"Hello,  

The three MCAL events listed below cannot be activated. 

MCAL0003211000 

MCAL0003211001 

MCAL0003211002 

A validation step is required, that we cannot find. 

  

Please check! 

  

Thank you and BR, Dalibor",hello three mcal event listed activated mcal mcal mcal validation step required find please check thank br dalibor,0.010856235399842262,0.0,169,0.05980496069700671,0.24728594115003943,3.***************,0.*****************,0.13,0.15,44.86%
Incident,OEKB-6529,Medium,Dashboard alert TAX SLAs with Validity Date Less Than One Month,Wallmann GEROLD,2024-11-29 14:18:00,2024-11-29 14:35:00,NO,"For the Dashboard alert ""TAX SLAs with Validity Date Less Than One Month"" under ""Static Data"" the IGNORE button is missing. 

Please add the IGNORE Button 

  

Thank you 

  

GEROLD 

 ",dashboard alert tax slas validity date less one month static data ignore button missing please add ignore button thank gerold,-0.2346998229622841,0.0,171,0.057844320874838456,0.308674955740571,1.****************,0.*****************,0.1,0.15,43.0%
Information Request,OEKB-6528,Medium,Following up on ticket OEKB-6358 -- Extending Logs Information,Nikolay TSONKOV,2024-11-29 12:08:00,,NO,"following up on ticket OEKB-6358 , where on 30.09 MegaCor was not reachable 

to extend Logs information",following ticket oekb megacor reachable extend log information,0.04380249232053757,0.0,171,0.057844320874838456,0.2390493769198656,2.***************,0.*****************,0.1,0.06,23.91%
Incident,OEKB-6527,Medium,"M12 PROD - MRGR0003206939 - CA Status ""PartiallyConfirmed"" incorrect",Bertram Schon,2024-11-29 08:24:00,2024-11-29 08:32:00,NO,"There have been 4 MRGR-events with Pay Date 28/11. 
3 of them were confirmed today (29/11) - MT566 sent to clients accordingly. 

However, the CA Status of MRGR0003206939 is now ""PartiallyConfirmed"" which is not understandable/incorrect => 

!image-2024-11-29-09-18-15-420.png! 

Could you please check? 
Thanks, Bert",mrgrevents pay date confirmed today mt sent client accordingly however ca status mrgr partiallyconfirmed understandableincorrect imagepng could please check thanks bert,0.0029121562838554382,0.0,172,0.056888238346101516,0.24927196092903614,5.****************,0.2,0.1,0.15,54.89%
Incident,OEKB-6526,Medium,Job Client Notification Generation still executing,Nikolay TSONKOV,2024-11-28 11:56:00,2024-11-28 12:01:00,NO,"HI Emna, JOb Client Notification Generation is still executing since today ( 28.11.2024) 09:40 

can we pls discuss what to to with it , as it seems other jobs are not yet executted beause this one is not finished 

  

BR,  

Niki",hi emna job client notification generation still executing since today pls discus seems job yet executted beause one finished br niki,0.253853153437376,0.0,172,0.056888238346101516,0.186536711640656,2.***************,0.*****************,0.1,0.15,29.54%
Incident,OEKB-6525,Major,M12 PROD - AT0000A0BUX8 INTR Event on 15.12.2024,Dalibor VIDIC,2024-11-28 09:56:00,2024-11-28 10:00:00,YES,"Hello, 

An INTR event (INTR00158806) has been correctly created from the referentials for the ISIN mentioned above. 

According to the terms of the bond, no interest will be paid. Therefore, the event was manually cancelled and closed (27.11.2024). 

Today we see that there is a new INTR event (INTR0003210958) for this ISIN with the same key data. This event was created yesterday from the referentials at 20:00. 

  

Please check! 

  

Thank you and BR, Dalibor 

 ",hello intr event intr correctly created referentials isin mentioned according term bond interest paid therefore event manually cancelled closed today see new intr event intr isin key data event created yesterday referentials please check thank br dalibor,-0.16588800959289074,0.0,173,0.055947958471591154,0.2914720023982227,3.***************,0.*****************,0.13,0.15,51.48%
Information Request,OEKB-6524,Major,Alerts in PROD,Nikolay TSONKOV,2024-11-28 09:32:00,2024-12-09 14:46:00,NO,"Hello Emna,  

can we please check which alerts are currently enabled in MegaCor and MegaBroker PROD 

the idea is  if there is a msg which could not be send out from MB or can not be processed to make sure we will be informed 

as i do not know a good overview can we please check alerts also for other crucial jobs together 

  

BR, 

Niki ",hello emna please check alert currently enabled megacor megabroker prod idea msg could send mb processed make sure informed know good overview please check alert also crucial job together br niki,0.045273447409272194,11.0,173,0.055947958471591154,0.23868163814768195,2.***************,0.*****************,0.13,0.06,28.36%
Incident,OEKB-6523,Medium,M12 PROD - MEET0003210939 - saving of update failed,Ana Maria Galic,2024-11-28 08:59:00,2024-11-28 09:01:00,NO,"MEET0003210939 

same issue as in OEKB-6506 
First REPL received at 4:04 in the morning, second REPL received at 4:20 in the morning - we rejected the first REPL, the second REPL did not reach M12 and now the event cannot be updated.  

Can you please check and unblock this event? The two REPL FlowIn's are attached. 

Thanks 
BR Ana  

[^202411280304093_\{54F03858-DCE0-45D1-BBAE-BC91A13D9615}.txt] [^202411280304093_\{54F03858-DCE0-45D1-BBAE-BC91A13D9615}.txt] 

^^[^202411280320120_\{CCD4618F-4FB9-439F-BA6F-38875193D2B0}.txt]^^",meet issue oekb first repl received morning second repl received morning rejected first repl second repl reach event updated please check unblock event two repl flowins attached thanks br ana fdcedbbaebcadtxt fdcedbbaebcadtxt ccdffbfbafdbtxt,-0.01992928422987461,0.0,173,0.055947958471591154,0.25498232105746865,1.****************,0.037820429797300145,0.1,0.15,31.42%
Information Request,OEKB-6522,Medium,Question Related to Files ConfirmedEventTOCCSYS*,Nikolay TSONKOV,2024-11-28 08:03:00,2024-11-28 08:17:00,NO,"Dear Emna,  

there is a file generated from M12 with name ConfirmedEventTOCCSYS* 

it seems before this file was generated from M10 with , symbol at the end of each line, now in M12 this , is missing. can you pls check if this was overseen or if it is meant to be like this? 

  

for us it will be better if this file is generated with , at the end of each line 

can you pls check the script generating those files and let me know  

  

BR,  

Niki",dear emna file generated name confirmedeventtoccsys seems file generated symbol end line missing pls check overseen meant like u better file generated end line pls check script generating file let know br niki,-0.027971701696515083,0.0,173,0.055947958471591154,0.25699292542412877,2.***************,0.*****************,0.1,0.06,26.6%
Incident,OEKB-6521,Medium,M12 PROD - Ignore Button - Function unclear,Dalibor VIDIC,2024-11-27 13:08:00,2024-11-27 13:13:00,NO,"Hello,  

The function of the 'Ignore' button (List Events) is not clear to us or we suspect that it has no function at all. 

Can you please check? 

  

Thank you and BR, Dalibor",hello function ignore button list event clear u suspect function please check thank br dalibor,-0.04593157768249512,0.0,173,0.055947958471591154,0.2614828944206238,3.***************,0.*****************,0.1,0.15,42.49%
Incident,OEKB-6520,Major,M12 PROD - EXOF0003210610 - MT564 REPL sent for unknown reasons,Bertram Schon,2024-11-27 08:58:00,2024-11-27 09:57:00,NO,"EXOF0003210610 
was set up manually on 20/11, MT564 NEWM sent accordingly. 

Today, I noticed, that on 21/11, 20:15, REPL were sent to all clients for unknown reasons without any change in data as no feed was received or update has been made. 

Could you please check why these REPL messages were sent and skip the sending of these unneccessary messages as some clients are yet complaining about it. 

(looks like the same issue as already detected in OEKB-6355) 

Thanks, Bert",exof set manually mt newm sent accordingly today noticed repl sent client unknown reason without change data feed received update made could please check repl message sent skip sending unneccessary message client yet complaining look like issue already detected oekb thanks bert,-0.2322714924812317,0.0,174,0.05502322005640723,0.3080678731203079,5.****************,0.2,0.13,0.15,68.21%
Incident,OEKB-6519,Major,M12 PROD - EXOF0003210610 - MT564 REPL sent for unknown reasons,Bertram Schon,2024-11-27 08:57:00,2024-11-27 09:02:00,NO,"SPLR0003208177 
was set up manually yesterday, MT564 NEWM sent accordingly. 

Today at 07.15, MT564 REPL messages have been sent to all clients without any change in data as no feed was received or update has been made => 

!image-2024-09-27-09-00-38-468.png! 

Could you please check why these REPL messages were sent and skip the sending of these unneccessary messages as some clients are yet complaining about it. 

Same behavior was detected for event LIQU0003206314 (REPL sent for unknown reasons on 23/09, 20:15), so I guess this happens for several events... 

Please check! 
Thanks, Bert",splr set manually yesterday mt newm sent accordingly today mt repl message sent client without change data feed received update made imagepng could please check repl message sent skip sending unneccessary message client yet complaining behavior detected event liqu repl sent unknown reason guess happens several event please check thanks bert,-0.30101724341511726,0.0,174,0.05502322005640723,0.3252543108537793,5.****************,0.2,0.13,0.15,70.79%
Incident,OEKB-6518,Major,"M12 PROD - PROX - seev.004 must be impacted successfully if <AcctId> includes Prefix ""OCSD""",Bertram Schon,2024-11-25 13:29:00,2024-11-25 13:38:00,NO,"When testing import of seev.004 including 
<AcctId>{*}OCSD{*}227300</AcctId> 
it turned out, that the messages failed with following error => 

!image-2024-11-25-14-26-58-338.png! 

When resending the instruction including  
<AcctId>227300</AcctId> (no prefix), the message was impacted successfully. 


However, we request our participants to instruct including the AcctId-format 
<AcctId>{*}OCSD123400{*}</AcctId>, so, a seev004 containing the Prefix must be impacted! 

Could you please check! 

Thanks, Bert",testing import seev including acctidocsdacctid turned message failed following error imagepng resending instruction including acctidacctid prefix message impacted successfully however request participant instruct including acctidformat acctidocsdacctid seev containing prefix must impacted could please check thanks bert,-0.759265772998333,0.0,175,0.05411376622282161,0.43981644324958324,5.****************,0.2,0.13,0.15,87.97%
Information Request,OEKB-6517,Medium,M12 PROD - PROX Events / Mapping discrepancies seev.001 IN / OUT,Bertram Schon,2024-11-25 13:24:00,2024-11-25 13:37:00,NO,"Could you please check and add the following fields in seev.001 OUT which are missing although available in seev.001 IN test messages received from Clearstream (message attached): 

<SctiesBlckgPrdEndDt> 
<AdrTp>ADDR</AdrTp> 

Thanks, Bert",could please check add following field seev missing although available seev test message received clearstream message attached sctiesblckgprdenddt adrtpaddradrtp thanks bert,0.029292363673448563,0.0,175,0.05411376622282161,0.24267690908163786,5.****************,0.2,0.1,0.06,40.4%
Incident,OEKB-6516,Medium,M12 - URGENT - EXOF0003210750 - saving of update vanished/ no update possible anymore,Ana Maria Galic,2024-11-25 11:51:00,2024-11-25 12:04:00,NO,"EXOF0003210750 
I updated and saved this event around 12:05 today, and it was moved to ""Validate/ Reject Manuel Creation/ Updates"". Some minutes after I noticed that the event vanished from there although nobody had validated it - when trying to update the event again, nothing happens, the saving of the update is not possible.  

Same issues were originally fixed in OEKB-6349 

Could you please urgently check this? Event has to be sent out today. 

Attached the flowIn. 

[^FlowIn_120433925 (1).ID_414d512062697a74616c6b2e71756575a15e386725cf1202] 

  

BR Ana",exof updated saved event around today moved validate reject manuel creation update minute noticed event vanished although nobody validated trying update event nothing happens saving update possible issue originally fixed oekb could please urgently check event sent today attached flowin flowin iddacbeaecf br ana,-0.061797911301255226,0.0,175,0.05411376622282161,0.2654494778253138,1.****************,0.037820429797300145,0.1,0.15,32.99%
Incident,OEKB-6515,Medium,Dashbaord alert failed received WM - display ISIN instead of entitled ISIN / ID,Wallmann GEROLD,2024-11-22 07:09:00,2024-11-22 07:23:00,NO,"For the Dashboard alert ""Failed received WM"" under ""Failed Messages"": 

Please display the ISIN - without ""0"" in the ending - instead of ""entitled ISIN""/ID"". ",dashboard alert failed received wm failed message please display isin without ending instead entitled isinid,-0.04584633186459541,0.0,179,0.050623806287817624,0.26146158296614885,1.****************,0.*****************,0.1,0.15,35.92%
Incident,OEKB-6514,Medium,M12 PROD - MT564 RMDR not available in M12,Ana Maria Galic,2024-11-21 14:54:00,2024-11-21 14:56:00,NO,"Attached MT564 RMDR was received around 15:25 but did not reach M12. The associated MT568 did reach M12 though. 

Can you please check what's the problem here? 

Thank you 
BR Ana 

[^FlowIn_120427372.txt]",attached mt rmdr received around reach associated mt reach though please check whats problem thank br ana flowintxt,0.07171669043600559,0.0,179,0.050623806287817624,0.2320708273909986,1.****************,0.037820429797300145,0.1,0.15,27.98%
Incident,OEKB-6513,Major,M12 PROD - Conflicting updates - REDM Rate not displayed - URGENT,Dalibor VIDIC,2024-11-21 12:30:00,2024-11-21 12:44:00,NO,"REDM0003208123 - AT0000A313K8 

Hello,  

for some REDM Events, the redemption rates were announced and delivered to M12. 

The events are in the corresponding status (Blocked Critical =YES). 

But if you look at the update, you won't find the redemption rate. 

If you accept the update, the Redemption Rate will also be updated in the event. 

Please check urgently because we always need to know and to see what we are updating. 

  

Thank you and BR, Dalibor",redm atak hello redm event redemption rate announced delivered event corresponding status blocked critical yes look update wont find redemption rate accept update redemption rate also updated event please check urgently always need know see updating thank br dalibor,-0.016654809936881065,0.0,179,0.050623806287817624,0.25416370248422027,3.***************,0.*****************,0.13,0.15,45.89%
Incident,OEKB-6512,Medium,INTR / REDM loan calc error - IGNORE does not work,Wallmann GEROLD,2024-11-21 11:50:00,2024-11-21 11:52:00,NO,"For the dashboard alerts ""LOAN calc error"" and ""REDM calc error"" (under ""STATIC DATA"") the button ""IGNORE"" does not work - please repair.  

Please remove the button ""deactivate"". It should not be available here.  

This was discussed onside with Omar. 

See attachment",dashboard alert loan calc error redm calc error static data button ignore work please repair please remove button deactivate available discussed onside omar see attachment,-0.34222582541406155,0.0,179,0.050623806287817624,0.3355564563535154,1.****************,0.*****************,0.1,0.15,47.03%
Incident,OEKB-6511,Major,PCAL event created by error - same issue as Ticket 6461,Wallmann GEROLD,2024-11-21 08:26:00,2024-11-21 08:31:00,NO,"The problem of Ticket OEKB-6461 still exists in M12 PROD environment 

For the following ISIN there is an early redemption. A MCAL event was created, which is correct. 

But there is also a PCAL event created:  

PCAL0003210622 / AT0000A38163 

  

There should be no PCAL event created - can you check please the reason for this. 

The issue still is not solved.  

Regards, 

  

Gerold",problem ticket oekb still exists prod environment following isin early redemption mcal event created correct also pcal event created pcal ata pcal event created check please reason issue still solved regard gerold,-0.4194868765771389,0.0,180,0.049787068367863944,0.3548717191442847,1.****************,0.*****************,0.13,0.15,54.43%
Information Request,OEKB-6510,Medium,Dashbort Alerts ( Failed Bookings),Nikolay TSONKOV,2024-11-20 14:33:00,2024-11-20 14:59:00,NO,"Hello, we have Dashbort alerts under Failed Bookings , which we would like to be investigated and adviced what is the impact and if they can be ignored 

  

you can find in the email attached the prelimenary analyses 

it concerns  

Failed Recieved sese.024 

Failed Recieved sese.025 

Failed CASH Acknowledgement 

  

 ",hello dashbort alert failed booking would like investigated adviced impact ignored find email attached prelimenary analysis concern failed recieved sese failed recieved sese failed cash acknowledgement,-0.7477784156799316,0.0,180,0.049787068367863944,0.4369446039199829,2.***************,0.*****************,0.1,0.06,53.6%
Incident,OEKB-6509,Critical,M12 PROD - INTR0003206709 - Update not possible - URGENT,Dalibor VIDIC,2024-11-20 12:06:00,2024-11-20 12:54:00,NO,"Hello,  

The update is not possible in the above INTR Event as the change is not saved. 

  

Please check! 

  

Thank you and BR, Dalibor",hello update possible intr event change saved please check thank br dalibor,-0.20130807533860207,0.0,180,0.049787068367863944,0.3003270188346505,3.***************,0.*****************,0.15,0.15,55.81%
Incident,OEKB-6508,Medium,M12 PROD - Validate Conflicting Updates - View,Dalibor VIDIC,2024-11-20 11:00:00,2024-11-20 11:10:00,YES,"Hello, 

If you look at an ISIN in the menu item 'Validate Conflicting Updates' using 'View', there is no option to return back using the arrow or otherwise. 



Please check! 

  

Thank you and BR, Dalibor",hello look isin menu item validate conflicting update using view option return back using arrow otherwise please check thank br dalibor,0.016755254939198494,0.0,180,0.049787068367863944,0.24581118626520038,3.***************,0.*****************,0.1,0.15,40.14%
Incident,OEKB-6507,Major,M12 PROD - AT0000A1GTQ1 - INTR and PCAL Events not created,Dalibor VIDIC,2024-11-20 10:36:00,2024-11-20 10:46:00,NO,"Hello, 

There is an INTR and PCAL event in the above ISIN on 17 December 2024. 

Unfortunately, these events were not created in M12. 

  

Please check! 

  

Thank you and BR, Dalibor",hello intr pcal event isin december unfortunately event created please check thank br dalibor,-0.015362661331892014,0.0,181,0.04896416050925942,0.253840665332973,3.***************,0.*****************,0.13,0.15,45.84%
Information Request,OEKB-6506,Critical,URGENT - M12 PROD - XMET0003210411 - Saving of update failed,Bertram Schon,2024-11-19 15:16:00,2024-11-19 15:19:00,YES,"XMET0003210411 
We received 3 messages for this event (NEWM, REPL 1, REPL 2). 

!image-2024-11-19-16-10-32-125.png! 

We wanted to validate both REPL feeds one after another. 
However, after validating, we noticed that obviosuly both feeds are validated at the same time. 
Then we updated event data and tried to save. 
However, this action failed (no message popped up). 

We also checked the group in MegaBroker => 

!image-2024-11-19-16-14-58-500.png! 

Obviously, one feed still has to be validated. 
Could you please check and provide us with a workaround? 

Thanks, Bert",xmet received message event newm repl repl imagepng wanted validate repl feed one another however validating noticed obviosuly feed validated time updated event data tried save however action failed message popped also checked group megabroker imagepng obviously one feed still validated could please check provide u workaround thanks bert,0.044891513884067535,0.0,181,0.04896416050925942,0.23877712152898312,5.****************,0.2,0.15,0.06,47.32%
Incident,OEKB-6505,Major,M12 PROD - INTR00164334 - Update not possible - URGENT,Dalibor VIDIC,2024-11-19 14:37:00,2024-11-19 14:40:00,YES,"Hello,  

I have entered and saved the tax rate (16%) in the above Event but the update is not possible as the update is apparently not saved. 

No message is displayed that the changes have been saved. 

  

Please check! 

  

Thank you and BR, Dalibor 

 ",hello entered saved tax rate event update possible update apparently saved message displayed change saved please check thank br dalibor,0.0028882883489131927,0.0,181,0.04896416050925942,0.2492779279127717,3.***************,0.*****************,0.13,0.15,45.16%
Incident,OEKB-6504,Medium,M12 PROD - Same WM feed received every hour ?!,Bertram Schon,2024-11-19 13:49:00,2024-11-19 14:08:00,NO,"We notice that we receive the same feed from WM several times (obviously every hour) => 

!image-2024-11-19-14-47-18-457.png! 


Has there been something changed? 
Could you please check? 

Thanks, Bert 

  

  

  

  

 ",notice receive feed wm several time obviously every hour imagepng something changed could please check thanks bert,0.030465206131339073,0.0,181,0.04896416050925942,0.24238369846716523,5.****************,0.2,0.1,0.15,53.86%
Incident,OEKB-6503,Medium,4 Dashboard alerts under Market entitlement - there should be no DELETE button,Wallmann GEROLD,2024-11-19 11:58:00,2024-11-19 12:37:00,NO,"For the following 4 Dashboard under alert there should be NO ""DELETE"" button: 

Not Reconciled Received Cash REPE (see attachment as example). 

Not Reconciled Received Security REPE 

Reconciliation Mismatch with Received Cash REPE 

Reconciliation Mismatch with Security Cash REPE 

  

Please remove the ""DELETE"" button - as it is risky. 

The User should only use the ""IGNORE"" button if he wants to delete the dashboard alert from the list. 

This was discussed onside with Omar.  

Can you please check. 

 ",following dashboard alert delete button reconciled received cash repe see attachment example reconciled received security repe reconciliation mismatch received cash repe reconciliation mismatch security cash repe please remove delete button risky user use ignore button want delete dashboard alert list discussed onside omar please check,-0.2119806855916977,0.0,181,0.04896416050925942,0.3029951713979244,1.****************,0.*****************,0.1,0.15,42.15%
Incident,OEKB-6502,Major,M12 PROD - PROX0003210473 - seev.001 OUT - <Vote> section - Mapping wrong,Bertram Schon,2024-11-19 11:47:00,2024-11-19 11:57:00,NO,"PROX0003210473  
was set up according to incoming seev.001 (attached). 

In the seev.001, there is no <Vote> section available. 

However, in the seev.001 OUT (attached), this section is populated with incorrect ""default"" (?) mapping as follows => 

!image-2024-11-19-12-46-07-673.png! 


This is wrong information and must be skipped if not received in seev.001 IN. 

Please check! 
Thanks, Bert",prox set according incoming seev attached seev vote section available however seev attached section populated incorrect default mapping follows imagepng wrong information must skipped received seev please check thanks bert,-0.11228615790605545,0.0,181,0.04896416050925942,0.27807153947651386,5.****************,0.2,0.13,0.15,63.71%
Incident,OEKB-6501,Medium,"Dashboard alert - Validate conflicting updates - After ""Validate"" or ""Reject"" the alert is still there",Wallmann GEROLD,2024-11-19 11:40:00,2024-11-19 11:44:00,YES,"This is valid for the Dashbaord alerts ""Validate Conflicting Updates"" under ""Event Validation"". 

If I ""reject"" or ""validate"" an Dashboard alert from the list - for example REDM0003202500 (see attachment) - after validating or rejecting the alert  is still visible in this list and does not disappear from the list.  

Only if I press refresh or open again from the menupoint the Dashboard category ""Validate Conflicting Updates""  the just validated or rejected dashboard alert is no longer visible in the list. 

  

Can you please check and repair. 

The issue was discussed before with Omar onside. 

  

Thank you. 

  

Gerold 

 ",valid dashbaord alert validate conflicting update event validation reject validate dashboard alert list example redm see attachment validating rejecting alert still visible list disappear list press refresh open menupoint dashboard category validate conflicting update validated rejected dashboard alert longer visible list please check repair issue discussed omar onside thank gerold,-0.04248564876616001,0.0,181,0.04896416050925942,0.26062141219154,1.****************,0.*****************,0.1,0.15,35.79%
Incident,OEKB-6500,Major,URGENT/ M12 PROD - PROX0003210473 - event status incorrect (not crosschecked/ activated),Ana Maria Galic,2024-11-19 09:53:00,2024-11-19 09:57:00,NO,"PROX0003210473  

Event was updated and validated. However, the status remained ""preliminary"", we crosschecked the event with the button ""set as reliable"" but the event was still not activated after that. 
When trying to activate manually this error appears: 

!image-2024-11-19-10-52-02-668.png! 

  

Please check this issue urgently, the notifications have to be sent out asap. 

Thanks 
BR, Ana ",prox event updated validated however status remained preliminary crosschecked event button set reliable event still activated trying activate manually error appears imagepng please check issue urgently notification sent asap thanks br ana,-0.1658141016960144,0.0,182,0.0481548541211964,0.2914535254240036,1.****************,0.037820429797300145,0.13,0.15,41.39%
Information Request,OEKB-6499,Critical,M12 PROD - 4 MT564 NEWM from today not available in MegaCor/MegaBroker,Bertram Schon,2024-11-18 15:50:00,2024-11-18 15:56:00,NO,"We were checking MT564 IN drop folder and found 4 messages (attached) which were received from SWIFT Alliance today, but cannot be found in MegaBroker/MegaCor. 

Could you please check why these message are not available and make them available? 

Thanks, Bert",checking mt drop folder found message attached received swift alliance today found megabrokermegacor could please check message available make available thanks bert,0.04719861410558224,0.0,182,0.0481548541211964,0.23820034647360444,5.****************,0.2,0.15,0.06,47.23%
Information Request,OEKB-6498,Medium,M12 PROD - Dashboard Alert - Failed Received WM,Dalibor VIDIC,2024-11-18 14:54:00,2024-11-18 15:05:00,NO,"Hello,  

we have found the ISIN 'AT0000A2LC97' under Dashboard Alert 'Failed Received WM' , but we do not know why it is there and what this update means. 

  

Please check! 

  

Thank you and BR, Dalibor 

  

 ",hello found isin atalc dashboard alert failed received wm know update mean please check thank br dalibor,-0.0037783756852149963,0.0,182,0.0481548541211964,0.25094459392130375,3.***************,0.*****************,0.1,0.06,27.41%
Incident,OEKB-6497,Major,M12 PROD - Event with Incoming Comments - Ignore Button,Dalibor VIDIC,2024-11-18 12:05:00,2024-11-18 12:08:00,NO,"Hello,  

If you ignore the incoming message (Dividend Event Manipulation => Event with Incoming Comments) using the ‘Ignore’ function, the message with ""Incoming Comment"" will be created again.  

Please check! 

  

Thank you and BR, Dalibor",hello ignore incoming message dividend event manipulation event incoming comment using ignore function message incoming comment created please check thank br dalibor,-0.06214942783117294,0.0,182,0.0481548541211964,0.26553735695779324,3.***************,0.*****************,0.13,0.15,47.59%
Incident,OEKB-6496,Medium,MegaBroker FlowOut generation Failed,Nikolay TSONKOV,2024-11-15 13:32:00,2024-11-15 13:37:00,NO,"Hello, we found out that in MB for Flow id 120322657 related to DVCA0003210112 failed to be generated in MegaBroker 

  

attached is the error msg 

  

we came to this finding as our BUS colleagues informed us that they are missing messages in Issuer Platfrom 

  

what is the reason why the message failed to be generated in MB?We should not be sending them again  

can we identify other similar ? why no allert came  

 ",hello found mb flow id related dvca failed generated megabroker attached error msg came finding bus colleague informed u missing message issuer platfrom reason message failed generated mbwe sending identify similar allert came,-0.6062732189893723,0.0,185,0.04580631417262147,0.40156830474734306,2.***************,0.*****************,0.1,0.15,61.79%
Information Request,OEKB-6495,Medium,how can i check and confirm if mt564 msg sent out of MB reached 3i,Nikolay TSONKOV,2024-11-15 13:14:00,2024-11-15 13:31:00,NO,"We are trying to investigate if a msg from MegaCor/MegaBroker reached 3i 

the reason is that those events are not to be found in 3 

DVCA0003210113 

e.g ",trying investigate msg megacormegabroker reached reason event found dvca eg,-0.2055911310017109,0.0,185,0.04580631417262147,0.3013977827504277,2.***************,0.*****************,0.1,0.06,33.26%
Incident,OEKB-6493,Major,M12 PROD - OTHR0003210330 (Super Event MAND) - ADTX truncated,Bertram Schon,2024-11-15 09:52:00,2024-11-15 09:57:00,NO,"OTHR0003210330 
was set up via Super Event MAND including the following outgoing comment => 

!image-2024-11-15-10-51-11-545.png! 

However, in the outgoing MT564, we see that the text was truncated => 

!image-2024-11-15-10-51-58-045.png! 

Could you please check this behavior! 
Thanks, Bert",othr set via super event mand including following outgoing comment imagepng however outgoing mt see text truncated imagepng could please check behavior thanks bert,-0.023678047582507133,0.0,186,0.0450492023935578,0.2559195118956268,5.****************,0.2,0.13,0.15,60.39%
Incident,OEKB-6492,Medium,Dashboard alert - Validating conflicting updates - warning message: more then 10099 results,Wallmann GEROLD,2024-11-14 09:34:00,2024-11-14 09:44:00,NO,"This problem is for the Dashboard alert - ""Validating Conflicting Updates"" under ""Event Validation"": 

When I try to compare the conflicting update there is the warning message that the search has returned 10099 results - see attachment. 

This message could be closed - but it should not be here. 

  

This problem exists since 12.11.2024. 

Can you please check ",problem dashboard alert validating conflicting update event validation try compare conflicting update warning message search returned result see attachment message could closed problem exists since please check,-0.7715122774243355,0.0,187,0.044304604571496584,0.44287806935608387,1.****************,0.*****************,0.1,0.15,63.13%
Incident,OEKB-6491,Major,M12 PROD - INTR0003206478 - Update not possible - URGENT,Dalibor VIDIC,2024-11-14 08:27:00,2024-11-14 08:29:00,NO,"Hello,  

the update is not possible in the above-mentioned INTR Event, as the update is not saved. 

There is no message that the changes have been saved and the event is not in Status "" Blocked critical"". 

  

Please check! 

  

Thank you and BR, Dalibor 

 ",hello update possible abovementioned intr event update saved message change saved event status blocked critical please check thank br dalibor,-0.05987408570945263,0.0,187,0.044304604571496584,0.26496852142736316,3.***************,0.*****************,0.13,0.15,47.51%
Incident,OEKB-6489,Major,M12 PROD - DVCA0003209209 - Mismatch between Client Entitlement and Client Payment - URGENT,Dalibor VIDIC,2024-11-13 10:24:00,2024-11-13 10:51:00,NO,"Hello 

the entitlements were calculated correctly in the above DVCA event but the 'Client Payments' were not executed correctly.  

I have now cancelled the client payments. Cancellation of the payment with the status ""CashBookingRejected' is not possible. 

It looks as if the problem is the same as in the ticket ""6320"" but please check urgently. 

  

Thank you and BR, Dalibor",hello entitlement calculated correctly dvca event client payment executed correctly cancelled client payment cancellation payment status cashbookingrejected possible look problem ticket please check urgently thank br dalibor,-0.5315123908221722,0.0,188,0.04357231386892163,0.38287809770554304,3.***************,0.*****************,0.13,0.15,65.2%
Incident,OEKB-6484,Medium,"M12 PROD - SPLR0003209810 - ""confirm alert"" after clicking on ""compare draft""",Ana Maria Galic,2024-11-12 14:12:00,2024-11-12 14:23:00,NO,"Today I noticed a new ""confirm alert"", when we receive an update from a custodian for an event and want to reject or validate it.  

When clicking on ""compare draft"" this alert pops up: 

!image-2024-11-12-15-07-02-183.png! 

This happened for example in event SPLR0003209810 and several other events today. 

Can you please check what this alert is about and remove it as this was never displayed before when wanting to reject or validate an update received from a custodian? 


Thank you 
BR, 
Ana 

 ",today noticed new confirm alert receive update custodian event want reject validate clicking compare draft alert pop imagepng happened example event splr several event today please check alert remove never displayed wanting reject validate update received custodian thank br ana,0.002642359584569931,0.0,188,0.04357231386892163,0.24933941010385752,1.****************,0.037820429797300145,0.1,0.15,30.57%
Incident,OEKB-6480,Major,M12 PROD - seev.001 for AT-ISINs to be sent to WMDSDEFFXXX / SLA missing,Bertram Schon,2024-11-12 09:56:00,2024-11-15 15:55:00,NO,"When testing seev-messages with WM, it turned out that a configuration/SLA is missing in M12 (which was availble in M10) to send seev.001 to WMDSDEFFXXX. 

Could you please assist us (OeKB IT-S) to set up this SLA in M12? 

Here's an example of a messages sent to WM in M10 => 

!image-2024-11-12-10-55-04-611.png! 

Thanks, Bert",testing seevmessages wm turned configurationsla missing availble send seev wmdsdeffxxx could please assist u oekb set sla here example message sent wm imagepng thanks bert,0.09715109318494797,3.0,189,0.*****************,0.225712226703763,5.****************,0.2,0.13,0.15,55.86%
Incident,OEKB-6479,Medium,For Client 23200 no e-mail is sent for SLA = OTHER,Wallmann GEROLD,2024-11-12 09:37:00,2024-11-12 10:29:00,NO,"For client 232000 and e-mail Adress for Purpose = OTHER = [<EMAIL>|mailto:<EMAIL>] 

The client informed us, that he did not receive the e-mail. (This is the case since 21.10.2024 - on 21.10.2024 they received the e-mail) 

Can you please check as in my opinion SLA and Adress for client are created correctly. 

The e-mails with the Purpose ""Current"" and ""Official"" worked. 

Can you please check if there could be an problem on Vermeg side? 

  

thank you 

  

Gerold 

 ",client email adress purpose accountinfooekbatmailtoaccountinfooekbat client informed u receive email case since received email please check opinion sla adress client created correctly email purpose current official worked please check could problem vermeg side thank gerold,-0.*****************,0.0,189,0.*****************,0.****************,1.****************,0.*****************,0.1,0.15,34.36%
Incident,OEKB-6478,Medium,Dashboard alert - Entitlement Calculated for Untaxed Events - Ignore does not work,Wallmann GEROLD,2024-11-12 08:09:00,2024-11-12 08:39:00,NO,"For the Dashboard alert ""Entitlements calculated for unTaxed Events"" under ""Market Entitlement"" the function ""IGNORE"" does not work. 

If I process IGNORE the dashboard alert is still here and does not disappear. 

Please repair. 

Please remove also the function ""Generate Market Payment"" - it is not needed in this place and should not be clicked in this place. 

 ",dashboard alert entitlement calculated untaxed event market entitlement function ignore work process ignore dashboard alert still disappear please repair please remove also function generate market payment needed place clicked place,-0.42715034633874893,0.0,189,0.*****************,0.35678758658468723,1.****************,0.*****************,0.1,0.15,50.22%
Incident,OEKB-6474,Major,M12 PROD - MT564 REPL not available in M12,Bertram Schon,2024-11-11 10:57:00,2024-11-11 11:11:00,NO,"Today, we received MT564 NEWM for XMET event around 08.45 (attached). 
Event was created, updated manually and subject to validation. 

Before validation, a MT564 REPL was received around 10.46 (attached). 
FlowIn seems to be processed accordingly => 

!image-2024-11-11-11-53-33-380.png! 

When trying to open Flow Out (*********), the file contains only this text =>  

""Golden record sent via REST webservice to MegaCor."" 


Therefore (?), MT564 REPL is not available in M12!? 

Could you please urgently check? 

Thanks, Bert",today received mt newm xmet event around attached event created updated manually subject validation validation mt repl received around attached flowin seems processed accordingly imagepng trying open flow file contains text golden record sent via rest webservice megacor therefore mt repl available could please urgently check thanks bert,0.03408368490636349,0.0,189,0.*****************,0.24147907877340913,5.****************,0.2,0.13,0.15,58.22%
Incident,OEKB-6473,Major,M12 PROD - MT564 NEWM assigned to old event,Bertram Schon,2024-11-11 10:32:00,2024-11-11 10:39:00,NO,"Today, attached MT564 NEWM was received for XMET (CORP//1908933/15684, Record Date 22/11/2024). 

However, the message was received with error description  
""Critical update waiting validation step for CA with mainReference ""XMET00039202"" : A validation step has to be processed from a Accept/Reject Blocked for Critical Update screen."" 

which is a bug as the mentioned XMET is an old event with different CORP (1894224/15684) and different dates (Record Date 13/09/2024).  

We rejected the update in order to keep the old event as it is. 

Could you please check and possibly reprocess the NEWM so that the new XMET event is made available? 

Thanks, Bert",today attached mt newm received xmet corp record date however message received error description critical update waiting validation step ca mainreference xmet validation step processed acceptreject blocked critical update screen bug mentioned xmet old event different corp different date record date rejected update order keep old event could please check possibly reprocess newm new xmet event made available thanks bert,-0.05663817375898361,0.0,190,0.042143843509276406,0.2641595434397459,5.****************,0.2,0.13,0.15,61.62%
Incident,OEKB-6472,Major,"M12 PROD - MT568 failed with error ""MegaCor-0065: No Linked CA Found for the received notification""",Bertram Schon,2024-11-11 08:57:00,2024-11-11 09:05:00,NO,"We received MT568 which failed with the following error: 

!image-2024-11-11-09-52-03-044.png! 


Could you please check as all these 568 are linked to 564 (all 564 + 568 attached) and should be available under ""Incoming Comments"" in the Global View. 

Moreover, we noticed that only one of the three failing 568 is listed in the To-Do-List under ""Failed MT568"" which is also not understandable => 

!image-2024-11-11-09-53-44-942.png! 


BR Bert",received mt failed following error imagepng could please check linked attached available incoming comment global view moreover noticed one three failing listed todolist failed mt also understandable imagepng br bert,-0.20406028628349304,0.0,190,0.042143843509276406,0.30101507157087326,5.****************,0.2,0.13,0.15,67.15%
Incident,OEKB-6471,Medium,M12 PROD - SPLR0000003306 - MT566 wrong after ad-hoc booking workaround,Ana Maria Galic,2024-11-08 12:15:00,2024-11-08 12:18:00,NO,"After re-testing OEKB-6377 with SPLR0000003306 in QAS we noticed some mistakes in the outgoing MT566. Relevant MT566 is attached. 

Please adapt as following:  

:92B::EXCH//EUR/CHF/1, 
:90B::OFFR//ACTU/EUR1,3  
-> these two fields should be removed from the MT566. 

:19B::CINL//EUR0,65 -> this field should be filled with CHF0,65, not EUR. 
:90B::CINL//ACTU/EUR1,3 -> this field should be filled with CHF1,3, not EUR. 



Please note that these changes affect only the MT566 after ad-hoc booking workaround (as described in ticket OEKB-6377)  

Thanks 
BR, 
Ana 

[^flowOut_23390049.txt] 

 ",retesting oekb splr qas noticed mistake outgoing mt relevant mt attached please adapt following bexcheurchf boffractueur two field removed mt bcinleur field filled chf eur bcinlactueur field filled chf eur please note change affect mt adhoc booking workaround described ticket oekb thanks br ana flowouttxt,-0.015504725277423859,0.0,192,0.04076220397836621,0.25387618131935596,1.****************,0.037820429797300145,0.1,0.15,31.25%
Incident,OEKB-6470,Medium,M12 PROD - Cross Check CA - Move to Reliable CA,Dalibor VIDIC,2024-11-07 11:33:00,2024-11-07 11:47:00,NO,"Hello, 

If you try to cross check several ISINs at once, you will receive a message that this was successful, but in reality nothing has happened and the events are not cross checked. 

  

Please check! 

  

Thank you and BR, Dalibor",hello try cross check several isins receive message successful reality nothing happened event cross checked please check thank br dalibor,0.04607252776622772,0.0,193,0.04008846400757714,0.23848186805844307,3.***************,0.*****************,0.1,0.15,39.04%
Information Request,OEKB-6469,Major,URGENT - M12 PROD - SHDS0003209904 - no elig. positions as record date is 22/05/2024 (before migration),Ana Maria Galic,2024-11-05 09:18:00,2024-11-05 09:45:00,NO,"SHDS0003209904  

We received a disclosure request with record date before migration (22.05.2024) and found out that there are no eligible positions available for this event.  

Could you please check if there is a possibility to make these positions available for this event? Or is the only option to manual impact the positions? 

Please advise urgently as we have to send out notifications today. 

Thanks, regards 
Ana  

 ",shds received disclosure request record date migration found eligible position available event could please check possibility make position available event option manual impact position please advise urgently send notification today thanks regard ana,0.009314697235822678,0.0,196,0.038133326547045196,0.24767132569104433,1.****************,0.037820429797300145,0.13,0.06,21.32%
Incident,OEKB-6468,Major,M12 PROD - DFLT00095047 - Saving of update failed,Bertram Schon,2024-11-04 17:49:00,2024-11-04 17:54:00,NO,"DFLT00095047  
is a migrated event => 

!image-2024-11-04-17-47-44-928.png! 

Today, we received MT564 REPL (attached) and tried to update the outgoing comment. 
However, this action failed => 

!image-2024-11-04-17-46-10-195.png! 


Could you please check? 
Thanks, Bert",dflt migrated event imagepng today received mt repl attached tried update outgoing comment however action failed imagepng could please check thanks bert,-0.06536342576146126,0.0,196,0.038133326547045196,0.2663408564403653,5.****************,0.2,0.13,0.15,61.95%
Incident,OEKB-6467,Major,URGENT M12 PROD - XMET0003209792 MT564 not received in M12 (while system was down) - Manual Update failed,Bertram Schon,2024-11-04 17:43:00,2024-11-04 17:56:00,NO,"XMET0003209792   
We received MT564 REPL (attached) today at 13:15, while system was down. 
The message did not reach M12 until now. 
When trying to update the event manually, the saving failed. 

Could you please check? 

In addition, please make sure that no message is lost (in M12) while the system is down. 

Thanks, Bert",xmet received mt repl attached today system message reach trying update event manually saving failed could please check addition please make sure message lost system thanks bert,0.019436631351709366,0.0,196,0.038133326547045196,0.24514084216207266,5.****************,0.2,0.13,0.15,58.77%
Incident,OEKB-6466,Major,URGENT - No swift MT566 is sent on 04.11.2024 / 12:15,Wallmann GEROLD,2024-11-04 12:43:00,2024-11-04 12:53:00,NO,"The events with payment date today are still in the CA status ""PAID"" and not ""CONFIRMED"". 

Please check the reason for this. 

Normally the confirmation job runs at 12:15 

Please check if jobs are running 

 ",event payment date today still ca status paid confirmed please check reason normally confirmation job run please check job running,-0.21735187061131,0.0,196,0.038133326547045196,0.3043379676528275,1.****************,0.*****************,0.13,0.15,46.85%
Incident,OEKB-6465,Major,M12 PROD - DVOP0003209813 - Default Option mapped incorrectly,Bertram Schon,2024-11-04 11:47:00,2024-11-04 11:57:00,NO,"DVOP0003209813  
was set up after creating Custodian SLA for the entitled (rights) ISIN and reprocessing initially failed received feed (attached). 

When updating event data, we noticed that the default option flag was incorrectly set to TRUE for all three CASH options => 

!image-2024-11-04-11-43-30-446.png! 

As a workaround, we updated the flags to FALSE for Option 002 and 003, and immediately saved the update without pressing the return-button (as this action will reset all Default flags to TRUE). 


It seems that there is a similarity to ticket OEKB-6463?! 

Could you please check? 

BR Bert",dvop set creating custodian sla entitled right isin reprocessing initially failed received feed attached updating event data noticed default option flag incorrectly set true three cash option imagepng workaround updated flag false option immediately saved update without pressing returnbutton action reset default flag true seems similarity ticket oekb could please check br bert,-0.43775444105267525,0.0,196,0.038133326547045196,0.3594386102631688,5.****************,0.2,0.13,0.15,75.92%
Incident,OEKB-6464,Medium,"M12 PROD - To Do List - TNDP, WRTC events should not be listed under Corporate Action Events",Bertram Schon,2024-11-04 09:32:00,2024-11-04 09:34:00,NO,"We noticed that the event types TNDP and WRTC are available under ""CORPORATE ACTION MANIPULATION"" in the To Do List, here under ""Cross Check"" => 

!image-2024-11-04-09-30-55-343.png! 


Could you please move this events to ""DIVIDEND EVENT MANIPULATION""? 

Thanks, Bert",noticed event type tndp wrtc available corporate action manipulation list cross check imagepng could please move event dividend event manipulation thanks bert,-0.01735403947532177,0.0,197,0.03750303809827702,0.25433850986883044,5.****************,0.2,0.1,0.15,55.65%
Incident,OEKB-6463,Medium,M12 PROD - XMET Data Feed from WDBO - Impact failed due wrong mapping of Default Option Flag,Bertram Schon,2024-10-31 13:46:00,2024-10-31 13:49:00,NO,"Today, we received below data feed: 
[^FlowIn_120346828.ID_414d512062697a74616c6b2e7175657562210a672a82901c] 

for XMET event (ISIN AT0000777842) from WDBOATXXXXXX. 
However, impact failed due to below error => 

!image-2024-10-31-13-42-06-367.png! 

Obviously, both options have been mapped incorrectly as default option => 

!image-2024-10-31-13-44-37-004.png! 

Could you please check? 

As a workaround, I edited PROX option by setting Default Option to FALSE and reprocessed the feed succesfully. 

BR Bert",today received data feed flowiniddacbeaac xmet event isin wdboatxxxxxx however impact failed due error imagepng obviously option mapped incorrectly default option imagepng could please check workaround edited prox option setting default option false reprocessed feed succesfully br bert,-0.5105337016284466,0.0,200,0.035673993347252395,0.37763342540711164,5.****************,0.2,0.1,0.15,74.15%
Incident,OEKB-6462,Major,URGENT - M12 PROD - BIDS0003209136 - No MT564 REPL generated/sent,Bertram Schon,2024-10-30 16:13:00,2024-10-30 16:18:00,NO,"BIDS0003209136 
We updated this event today (by adding the cash price and editing the outgoing comment) and tried to generate REPL notifications manually at around 15.45. 
However, this action failed => 

!image-2024-10-30-16-11-03-945.png! 

Also the job at 15.55 did not work, at least for the notification generation for this event. 
Could you please check! 

Thanks, Bert",bid updated event today adding cash price editing outgoing comment tried generate repl notification manually around however action failed imagepng also job work least notification generation event could please check thanks bert,-0.22524775564670563,0.0,201,0.035084354100845025,0.3063119389116764,5.****************,0.2,0.13,0.15,67.95%
Incident,OEKB-6461,Major,PCAL event created by error,Wallmann GEROLD,2024-10-29 12:30:00,2024-10-29 12:32:00,NO,"For the following ISINs there is an early redemption. A MCAL event was created, which is correct. 

But there is also a PCAL event created:  
|Main Reference|Entitled ISIN|Reference Date|Payment Date|Update Date| 
|PCAL0003202358|AT0000341904|14.11.2024|15.11.2024|28.10.2024 09:27:44,144| 
|PCAL0003209394|AT0000A377K3|30.10.2024|31.10.2024|29.10.2024 08:46:25,889| 
|PCAL0003209514|AT0000A2TKR9|29.10.2024|30.10.2024|29.10.2024 08:57:53,860| 
|PCAL0003209546|AT0000A38767|31.10.2024|01.11.2024|29.10.2024 08:46:22,867| 

  

There should be no PCAL event created - can you check please the reason for this. 

I noticed this behaviour the first time this week. 

Regards, 

  

Gerold 

 ",following isins early redemption mcal event created correct also pcal event created main referenceentitled isinreference datepayment dateupdate date pcalat pcalatak pcalatatkr pcalata pcal event created check please reason noticed behaviour first time week regard gerold,-0.016266949474811554,0.0,202,0.034504460733950484,0.2540667373687029,1.****************,0.*****************,0.13,0.15,39.31%
Incident,OEKB-6460,Medium,M12 PROD - EXOF0003208292 - MT566 - RATE/PRPP should not be populated within own fees debit confirmation,Bertram Schon,2024-10-29 09:46:00,2024-10-29 09:49:00,NO,"EXOF0003208292 
When checking MT566 (attached) for SECU IN, CASH IN and CASH OUT (own fees) confirmation, we noticed that RATE/PRPP are populated with the CASH OUT debit confirmation of own fees => 

!image-2024-10-29-09-42-16-841.png! 

Could you please check and skip those fields from own fees CASH DEBT? 

Thanks, Bert",exof checking mt attached secu cash cash fee confirmation noticed rateprpp populated cash debit confirmation fee imagepng could please check skip field fee cash debt thanks bert,0.02356264740228653,0.0,203,0.0339341521613492,0.24410933814942837,5.****************,0.2,0.1,0.15,54.12%
Incident,OEKB-6458,Critical,URGENT ! - wrong Payment date for some ISINs on 01.11.2024 - URGENT,Wallmann GEROLD,2024-10-28 11:20:00,2024-10-28 11:26:00,YES,"For some ISINs which have target ""No"" the Payment Date in M12 is 01.11.2024 (=public holiday in Austria) instead on 04.11.2024 

Please check urgently the reason for this wrong payment date! 

  

As a work around we tried to change the value date manually: 

It worked for:  

AT0000491238 - we changed the value date successfully to 04.11.2024 - it can also be seen in the Search Result of LIST events - see attachment 

  

For these to ISINs there is a problem with the Payment date: 

AT0000A1ZH48   

AT0000A2K2R0  

We changed the Payment Date for these 2 ISINS to 4.11.2024 and had the following alert:  

CA saved successfully without Data Cleansing - see attachment for this alert 

  

If you go into the view screen you can see that the ""Payment Date"" = 04.11.2024 - see attachment  

But in the search result of the LIST Events screen the payment date is still 01.11.2024 - see attachment 

please check urgently ",isins target payment date public holiday austria instead please check urgently reason wrong payment date work around tried change value date manually worked changed value date successfully also seen search result list event see attachment isins problem payment date atazh atakr changed payment date isins following alert ca saved successfully without data cleansing see attachment alert go view screen see payment date see attachment search result list event screen payment date still see attachment please check urgently,-0.060768336057662964,0.0,203,0.0339341521613492,0.26519208401441574,1.****************,0.*****************,0.15,0.15,43.98%
Requirement,OEKB-6457,Medium,Change of LDAP Users,Nikolay TSONKOV,2024-10-25 15:29:00,2024-11-04 16:22:00,NO,"Hello Emna,  

as already in some emails discussed we need to change the technical LDAP User 

we already have new created LDAP users for each System ( PROD, QAS and TEST) 

IT colleagues just informed me we need to finish this before EOM 

can we please put a PRIO on this reques? 

  

BR,  

Niki 

 ",hello emna already email discussed need change technical ldap user already new created ldap user system prod qas test colleague informed need finish eom please put prio reques br niki,0.002847440540790558,10.0,206,0.032279164031359865,0.24928813986480236,2.***************,0.*****************,0.1,0.09,29.95%
Information Request,OEKB-6456,Medium,M12 PROD - Repair Received Feed - Error Description,Dalibor VIDIC,2024-10-25 11:34:00,2024-10-25 11:42:00,NO,"Hello, 

The menu item '{*}Repair Received Feed{*}' currently contains 1922 data records. 

The following BICs are listed as senders: 

OCSDATWWAXXX 

ISPOATXXXXXX 

WDBOATXXXXXX 

NECINL2AXXX 

It looks as if most of the data sets are updated on an hourly basis. 

  

In M10 we had the dashboard alert ""Failed Received WM"" (is also available in M12) with Data Feed WDBO and error description ""{*}No Positions and no Eligible custodians found for the financial instrument ISIN, Configure one CustodianSLA instance or check security positions.{*}  

This issue has been discussed and it has been decided that this dashboard warning with the above mentioned error message is no longer needed. (Ticket 4545). 

The question for us now is whether this has also been implemented in M12 as we have a large number of data records in M12 with sender 'WDBOATXXXXXX' and a similar error description ({*}ELIGIBLE CUSTODIANS MISSING FOR THE TRADABLE ASSET ...{*}) 

  

We also have many events with sender 'OCSDATWWAXXX' and with the same error description ({*}ELIGIBLE CUSTODIANS MISSING FOR THE TRADABLE ASSET ...){*} I assume that we can ignore them all because there is no holdings. 

If our assumption is correct, it would be possible to ignore all these events at once, because otherwise we would have to look at each individual data record and ignore them one by one? 

  

The Excel file with the data records is in the attachment. 

  

Please check! 

  

Thank you and BR, Dalibor 

  

  

  

  

  

  

  

  

  

  

  

  

  

 ",hello menu item repair received feed currently contains data record following bics listed sender ocsdatwwaxxx ispoatxxxxxx wdboatxxxxxx necinlaxxx look data set updated hourly basis dashboard alert failed received wm also available data feed wdbo error description position eligible custodian found financial instrument isin configure one custodiansla instance check security position issue discussed decided dashboard warning mentioned error message longer needed ticket question u whether also implemented large number data record sender wdboatxxxxxx similar error description eligible custodian missing tradable asset also many event sender ocsdatwwaxxx error description eligible custodian missing tradable asset assume ignore holding assumption correct would possible ignore event otherwise would look individual data record ignore one one excel file data record attachment please check thank br dalibor,-0.12137901596724987,0.0,206,0.032279164031359865,0.28034475399181247,3.***************,0.*****************,0.1,0.06,31.82%
Incident,OEKB-6455,Major,BPUT Events not created in M12 PROD,Wallmann GEROLD,2024-10-25 10:58:00,2024-10-25 11:00:00,NO,"Under „Repair Received Feeds“ there are the following alerts fort BPUT Events – the BPUT events where not created in M12. 

The ISINs are: 
|AT0000A073W3| 

AT0000494984 

AT0000494976 

  

The mapping was already configured in M12 QAS.  

The re-load of the flows were done in M12 QAS. 

BPUT events were created successfully in M12 QAS. 

  

Please fix it also in PROD and reprocess the old failed events in M12 PROD. 

See e-mail conversation attached. 

  

Thank you 

  

Gerold 

  

  

 ",repair received feed following alert fort bput event bput event created isins ataw mapping already configured qas reload flow done qas bput event created successfully qas please fix also prod reprocess old failed event prod see email conversation attached thank gerold,-0.08920089527964592,0.0,206,0.032279164031359865,0.2723002238199115,1.****************,0.*****************,0.13,0.15,42.05%
Incident,OEKB-6454,Critical,URGENT - M12 PROD - BIDS0003209295 - No MT564 sent / Generate/Send Notification Job not processed? / Manual generation failed,Bertram Schon,2024-10-24 15:32:00,2024-10-24 15:36:00,NO,"BIDS0003209295 
was set up today (validated at 24/10/2024 14:46:02:553) and needs to be notified to clients asap. 

Unfortunately, I noticed that no notifications have been generated at 14:55, when trying to generate manually, an error popped up => 

!image-2024-10-24-15-32-07-537.png! 


Could you please urgently check! 
Thanks, Bert",bid set today validated need notified client asap unfortunately noticed notification generated trying generate manually error popped imagepng could please urgently check thanks bert,-0.6433953419327736,0.0,207,0.03174563637806794,0.4108488354831934,5.****************,0.2,0.15,0.15,86.63%
Incident,OEKB-6453,Medium,Field NWFC is missing in swift MT566 for PRED events,Wallmann GEROLD,2024-10-24 12:08:00,2024-10-24 12:13:00,YES,"Hi, 

  

In the swift MT566 from M12 for PRED events the field NWFC is missing. 

In the swift MT566 from M10 for PRED events this was available.  

  

Please see swift MT566 from M10 and M12 attached. ",hi swift mt pred event field nwfc missing swift mt pred event available please see swift mt attached,-0.00523335300385952,0.0,207,0.03174563637806794,0.2513083382509649,1.****************,0.*****************,0.1,0.15,34.4%
Information Request,OEKB-6452,Medium,M12 PROD - Validate/Reject Manual Creation/Updates - Buttons unclear/to be skipped,Bertram Schon,2024-10-24 11:34:00,2024-10-24 13:10:00,NO,"Today I noticed the button ""Edit Tolerated Keys"" when/before validating a manual update. 
Could you please explain the functionality behind and if/when this can/should be used? 

Also, there is an ""Ignore""-button, which is also not understandable. 
!image-2024-10-24-11-33-30-268.png! 

  

If there is no functionality, could you please skip these buttons? 

Thanks for checking, 
BR Bert 

  

 ",today noticed button edit tolerated key whenbefore validating manual update could please explain functionality behind ifwhen canshould used also ignorebutton also understandable imagepng functionality could please skip button thanks checking br bert,-0.00044004619121551514,0.0,207,0.03174563637806794,0.2501100115478039,5.****************,0.2,0.1,0.06,41.52%
Incident,OEKB-6451,Medium,M12 PROD - To Do List - Client Entitlement Not Calculated - Ignore does not work,Bertram Schon,2024-10-24 09:51:00,2024-10-24 09:59:00,NO,"There is still an issue with the To Do List, when trying to ignore under ""Client Entitlement Not Calculated"", the following error popped up =>  

!image-2024-10-24-09-50-57-101.png! 




Could you please check? 
Thanks, Bert 

  

  

  

 ",still issue list trying ignore client entitlement calculated following error popped imagepng could please check thanks bert,-0.26643130369484425,0.0,208,0.031220927161230903,0.31660782592371106,5.****************,0.2,0.1,0.15,64.99%
Incident,OEKB-6450,Major,M12 PROD - Internal Comment,Dalibor VIDIC,2024-10-24 09:44:00,2024-10-24 10:01:00,NO,"Hello,  

we have noticed that the 'Internal comment' is no longer visible after an update. 

The comment can still be found in the menu item 'Internal Comment by Event'. 

  

Please check! 

  

Thank you and BR, Dalibor",hello noticed internal comment longer visible update comment still found menu item internal comment event please check thank br dalibor,-0.07146510854363441,0.0,208,0.031220927161230903,0.2678662771359086,3.***************,0.*****************,0.13,0.15,47.94%
Incident,OEKB-6440,Major,M12 PROD - AT000B110085 - INTR Event created twice,Dalibor VIDIC,2024-10-23 10:51:00,2024-10-23 11:02:00,NO,"Hello, 

The 'INTR' event per 31 October 2024 for the above ISIN was created twice from the referential. 

Please check! 

  

Thank you and BR, Dalibor 

  

 ",hello intr event per october isin created twice referential please check thank br dalibor,-0.008374657481908798,0.0,208,0.031220927161230903,0.2520936643704772,3.***************,0.*****************,0.13,0.15,45.58%
Incident,OEKB-6439,Major,M12 PROD - MT564 IN CANC - Message cannot be viewed/downloaded,Bertram Schon,2024-10-23 08:44:00,2024-10-23 08:52:00,NO,"MT564 CANC was received yesterday => 

!image-2024-10-23-08-44-09-884.png! 

When trying to view and download the message found under MT564 IN CANC, this action failed => 

!image-2024-10-23-08-41-50-551.png! 

Could you please check? 
Thanks, Bert",mt canc received yesterday imagepng trying view download message found mt canc action failed imagepng could please check thanks bert,-0.007284305989742279,0.0,209,0.030704890624914584,0.25182107649743557,5.****************,0.2,0.13,0.15,59.77%
Information Request,OEKB-6438,Major,M12 PROD some jobs with Failed stats,Nikolay TSONKOV,2024-10-23 08:16:00,2024-10-23 10:01:00,NO,"Good morning dears,  

today i saw some Alert from Megara PROD which does not say to me much attached 

then i checked if any jobs with failed status attached also  

can you pls have a look and advice if we have an issue in PROD or not",good morning dear today saw alert megara prod say much attached checked job failed status attached also pls look advice issue prod,-0.4331054575741291,0.0,209,0.030704890624914584,0.3582763643935323,2.***************,0.*****************,0.13,0.06,46.3%
Information Request,OEKB-6436,Medium,Megara Alerts M12 - AT0000306691,Dalibor VIDIC,2024-10-22 15:13:00,2024-10-22 16:37:00,NO,"Hello, 

We have received last two days an e-mail Alert regarding 'AT0000306691' but we do not know what the cause is. 

Can you please check? 

  

Thank you and BR, Dalibor",hello received last two day email alert regarding know cause please check thank br dalibor,0.012798281386494637,0.0,209,0.030704890624914584,0.24680042965337634,3.***************,0.*****************,0.1,0.06,26.78%
Incident,OEKB-6435,Major,M12 PROD - AT0000A0ZCR7 - Error in the Generated Schedule,Dalibor VIDIC,2024-10-22 14:19:00,2024-10-22 16:59:00,NO,"Hello,  

The generated schedule created for AT0000A0ZCR7 is not correct and therefore the wrong INTR events are created. 

Coupon Frequency for above ISIN is Monthly and the coupon date is the 30th of the month. 

  

Please check! 

  

Thank you and BR, Dalibor 

  

 ",hello generated schedule created atazcr correct therefore wrong intr event created coupon frequency isin monthly coupon date th month please check thank br dalibor,-0.22351123206317425,0.0,209,0.030704890624914584,0.30587780801579356,3.***************,0.*****************,0.13,0.15,53.65%
Incident,OEKB-6433,Medium,M12 PROD - Repair Received Feed - IgnoreDataCA failed for 2 seev.001 messages,Bertram Schon,2024-10-22 07:41:00,2024-10-22 09:53:00,NO,"Could you please check why it is not possible to ignored these 2 feeds => 

!image-2024-10-22-07-40-53-862.png! 

Although I received the below message => 

!image-2024-10-22-07-40-28-213.png! 

the feeds are still available under ""RepairReceivedFeed""-search result. 

Thanks, Bert",could please check possible ignored feed imagepng although received message imagepng feed still available repairreceivedfeedsearch result thanks bert,0.041739802807569504,0.0,210,0.0301973834223185,0.23956504929810762,5.****************,0.2,0.1,0.15,53.43%
Incident,OEKB-6432,Medium,M12 PROD - BIDS0003209136 - Certification Breakdown Type (CERT) not populated in MT564 OUT,Bertram Schon,2024-10-21 17:11:00,2024-10-21 17:14:00,NO,"BIDS0003209136 
was set up via ""Super Event VOLU"" including the ""Certification Breakdown Type"" with value FULL => 


!image-2024-10-21-17-07-57-493.png! 

However, when checking outgoing MT564 (message attached), I noticed that this tag is missing => 

!image-2024-10-21-17-10-19-800.png! 




Could you please check? 
Thanks, Bert 

  

 ",bid set via super event volu including certification breakdown type value full imagepng however checking outgoing mt message attached noticed tag missing imagepng could please check thanks bert,0.012118728831410408,0.0,210,0.0301973834223185,0.2469703177921474,5.****************,0.2,0.1,0.15,54.55%
Incident,OEKB-6431,Medium,M12 PROD -Close INTR Event with INTP = 0,Dalibor VIDIC,2024-10-21 08:12:00,2024-10-21 08:26:00,NO,"Hello,  

We have two INTR events with an interest rate of 0 on 21 October 2024. 

I cannot find these events in the dashboard under 'Close INTR Event with INTP = 0'.  

  

Please check! 

  

Thank you and BR, Dalibor",hello two intr event interest rate october find event dashboard close intr event intp please check thank br dalibor,-0.058527953922748566,0.0,211,0.02969826457595639,0.26463198848068714,3.***************,0.*****************,0.1,0.15,42.96%
Requirement,OEKB-6430,Medium,M12 PROD - :22F:://CHAN - CHAN Type Indicator to be handled as repetitive field,Bertram Schon,2024-10-18 16:43:00,,NO,"Hi, 
as discussed within OEKB-6341, the field CHAN Type Indicator (:22F:://CHAN) is currently not handled as a repetitive field (M12 only can map/populate it once). 

As this field is potentially used by custodians more than once, please check feasibility to handle this field as a repetitive one and provide us with a cost estimation. 

Many thanks, Bert",hi discussed within oekb field chan type indicator fchan currently handled repetitive field mappopulate field potentially used custodian please check feasibility handle field repetitive one provide u cost estimation many thanks bert,0.017675666138529778,0.0,213,0.028724639654239433,0.24558108346536756,5.****************,0.2,0.1,0.09,45.34%
Incident,OEKB-6429,Major,M12 PROD - DVCA0003208778 - RMDR sent for unknown reasons,Bertram Schon,2024-10-18 09:40:00,2024-10-18 09:42:00,NO,"DVCA0003208778 (Exercise of Dividend Rights)  
was set up as subsequent event to RHDI0003208573 (Issue of Dividend Rights). 
PAYD of RHDI is on 04/11/2024. 

Today I coincidentally noticed that already RMDR messages have been sent to clients =>  

!image-2024-10-18-09-39-44-480.png! 


 although the RMDR jobs are scheduled as follows => 


!image-2024-10-18-09-35-18-561.png! 


Could you please check this behavior and avoid sending further useless RMDR messages? 

Thanks, Bert",dvca exercise dividend right set subsequent event rhdi issue dividend right payd rhdi today coincidentally noticed already rmdr message sent client imagepng although rmdr job scheduled follows imagepng could please check behavior avoid sending useless rmdr message thanks bert,0.001318998634815216,0.0,214,0.028249863121256928,0.2496702503412962,5.****************,0.2,0.13,0.15,59.45%
Incident,OEKB-6428,Major,M12 PROD -DVCA0003208380 MT566 OUT Generation Failed - URGENT,Dalibor VIDIC,2024-10-17 11:13:00,2024-10-17 11:48:00,NO,"Hello,  

the 'DVCA0003208380 ' dividend was processed with value date 15.10.2024 and confirmed by MT566 message. However, it looks as if no messages have been sent to the clients. MT564 messages have been successfully sent. 

The second problem is that we do not receive any information about it because there is no e-mail alert about it. A client brought this to our attention. 

Please check! 

  

Thank you and BR, Dalibor",hello dvca dividend processed value date confirmed mt message however look message sent client mt message successfully sent second problem receive information email alert client brought attention please check thank br dalibor,0.009646158665418625,0.0,214,0.028249863121256928,0.24758846033364534,3.***************,0.*****************,0.13,0.15,44.9%
Incident,OEKB-6427,Medium,M12 PROD - EXOF0003208572 - Market Instruction AckedCreated although MT567 IN failed?!,Bertram Schon,2024-10-17 10:40:00,2024-10-17 10:57:00,NO,"EXOF0003208572  
Yesterday, we instructed for SLLE 3,8 rights, Market Instruction moved to status AckedCreated => 

!image-2024-10-17-10-34-56-471.png! 

  

However, when checking To Do List / Failed Received MT567, I noticed that the corresponding MT567 (attached) is in status ""Impact Failed"" with error => 

PALM-42008: Error when running lifecycle? 

Could you please check? 

BR Bert 

  

  

 ",exof yesterday instructed slle right market instruction moved status ackedcreated imagepng however checking list failed received mt noticed corresponding mt attached status impact failed error palm error running lifecycle could please check br bert,-0.27151443623006344,0.0,214,0.028249863121256928,0.31787860905751586,5.****************,0.2,0.1,0.15,65.18%
Incident,OEKB-6426,Major,M12 PROD CERT0003206587 - Client Instruction rejected in error,Bertram Schon,2024-10-16 18:06:00,2024-10-16 18:16:00,NO,"CERT0003206587 
Could you please urgently check the following: 
Client 222100 has eligible balance of 80.000 => 

!image-2024-10-16-18-04-18-348.png! 

There have been 2 instructions (30.000 and 50.000), both were cancelled today. 
When trying to create a new instruction for 50.000, the instruction was autorejected with error ""Instruction Rejected Due to Insufficient Position"" which is wrong. 

So I'm not able to forward this instruction to the custodian. 

Could you please repair this behavior asap, so that I can send the instruction tomorrow? 

Thanks, Bert",cert could please urgently check following client eligible balance imagepng instruction cancelled today trying create new instruction instruction autorejected error instruction rejected due insufficient position wrong im able forward instruction custodian could please repair behavior asap send instruction tomorrow thanks bert,-0.44835712388157845,0.0,215,0.02778293395412425,0.3620892809703946,5.****************,0.2,0.13,0.15,76.31%
Incident,OEKB-6425,Medium,M12 PROD - XMET0003208939 - Status not understandable,Bertram Schon,2024-10-16 17:42:00,2024-10-16 17:45:00,NO,"XMET0003208939 
We received NEWM and REPL within 15 minutes (both messages attached). 
I validated the REPL to be able to update the newest version of event data. 
After validating, I noticed that the event is subject to ""Accept/Reject Manual Update"" although I did no manual update => 

!image-2024-10-16-17-41-14-312.png! 

Could you please check this behavior, for me this does not make any sense. 
Thanks, Bert",xmet received newm repl within minute message attached validated repl able update newest version event data validating noticed event subject acceptreject manual update although manual update imagepng could please check behavior make sense thanks bert,0.047012876719236374,0.0,215,0.02778293395412425,0.2382467808201909,5.****************,0.2,0.1,0.15,53.24%
Incident,OEKB-6424,Major,M12 PROD - EXOF0003208572 - Client Instruction NOT autorejected in error / MIEX/MILT check failed,Bertram Schon,2024-10-16 15:41:00,2024-10-16 15:57:00,NO,"EXOF0003208572  
SECU option was set up with MIEX, MILT = 1 

Instruction was received for 2.986,8 but failed due to error ""NO_ELIGIBLE_PMVT"" (separate ticket already created). 
When creating CI manually, I would have expected that the instruction will be autorejected immediately which did not happen. 
Instruction moved to status ""WaitingFreezeConfirmation"" which is incorrect. 
As a workaround, I rejected the instruction manually (before Freeze confirmation was received in M12) and did the unfreeze manually in CCSYS. 

!image-2024-10-16-15-47-44-069.png! 


Could you please check? 
Thanks, Bert",exof secu option set miex milt instruction received failed due error noeligiblepmvt separate ticket already created creating ci manually would expected instruction autorejected immediately happen instruction moved status waitingfreezeconfirmation incorrect workaround rejected instruction manually freeze confirmation received unfreeze manually ccsys imagepng could please check thanks bert,-0.07091889344155788,0.0,215,0.02778293395412425,0.26772972336038947,5.****************,0.2,0.13,0.15,62.16%
Incident,OEKB-6423,Major,M12 PROD - EXOF0003208291/EXOF0003208292 - Consolidation failed,Bertram Schon,2024-10-15 16:40:00,2024-10-15 16:43:00,NO,"EXOF0003208291/EXOF0003208292 
We received a huge amount of Client Instructions which should be consolidated today to receive one Market Entitlement (process one Market Payment). 
When trying to do so via ""CONSOLIDATE ALL"", I noticed that this action failed, now there are several Market Entilements (but not the same amount as Client Entitlements) => 

!image-2024-10-15-16-37-49-390.png! 


As no instruction had to be sent, I edited one of the Market Entitlements SECU OUT with the sum of Client Entitlements SECU OUT, to process the Payment via Issuer Agent, for both events. 

But please check this behavior as soon as possible. 
Thanks, Bert",exofexof received huge amount client instruction consolidated today receive one market entitlement process one market payment trying via consolidate noticed action failed several market entilements amount client entitlement imagepng instruction sent edited one market entitlement secu sum client entitlement secu process payment via issuer agent event please check behavior soon possible thanks bert,0.056176548823714256,0.0,216,0.02732372244729256,0.23595586279407144,5.****************,0.2,0.13,0.15,57.39%
Incident,OEKB-6422,Major,M12 PROD - MT565 sent with existing SEME should be autorejected,Bertram Schon,2024-10-15 10:26:00,2024-10-15 10:52:00,NO,"Today, a client sent two instructions with the same SEME, MT565 failed => 

!image-2024-10-15-10-24-49-105.png! 

!image-2024-10-15-10-25-13-174.png! 

In such a case (as it was in M10), the instruction must be autorejected and a MT567 REJT must be sent to the client to inform about the status. 

Could you please check? 
Thanks, Bert 

  

  

 ",today client sent two instruction seme mt failed imagepng imagepng case instruction must autorejected mt rejt must sent client inform status could please check thanks bert,-0.0012919865548610687,0.0,217,0.0268721010********,0.*****************,5.****************,0.2,0.13,0.15,59.55%
Incident,OEKB-6421,Medium,M12 PROD - To Do List - Failed Bookings (seev.024/025) - MainRef/ISIN should be available,Bertram Schon,2024-10-15 08:49:00,2024-10-15 09:39:00,YES,"Could you please check the following regarding the ""Failed Bookings""-alert. 
Currently, there are several entries under ""Received failed sese-024"" => 

!image-2024-10-15-08-42-37-523.png! 

For better clarity, is it possible to provide MainRef., ISIN, Safekeeping Account in this list, as it was in M10 => 



!image-2024-10-15-08-47-21-497.png! 


In addition, could you please confirm if the a.m. search results are under the correct alert? We guess that in M10 those have been under ""Failed Received sese.025"". 

Thanks, Bert",could please check following regarding failed bookingsalert currently several entry received failed sese imagepng better clarity possible provide mainref isin safekeeping account list imagepng addition could please confirm search result correct alert guess failed received sese thanks bert,-0.****************,0.0,217,0.0268721010********,0.*****************,5.****************,0.2,0.1,0.15,56.75%
Incident,OEKB-6420,Major,URGENT - M12 PROD - EXOF0003208572 - reject conflicting update not possible,Bertram Schon,2024-10-14 12:36:00,2024-10-14 13:16:00,NO,"EXOF0003208572 
Same issue as reported in OEKB-6407, 
Flow In ID *********, message attached. 
When trying to compare draft, the following error popped up => 

!image-2024-10-14-12-37-32-273.png! 

Please check! 

Thanks, Bert ",exof issue reported oekb flow id message attached trying compare draft following error popped imagepng please check thanks bert,-0.*****************,0.0,217,0.0268721010********,0.****************,5.****************,0.2,0.13,0.15,61.72%
Incident,OEKB-6419,Major,URGENT - M12 PROD - CERT0003206587 - MT564 OUT for New Position missing,Bertram Schon,2024-10-14 09:48:00,2024-10-14 10:05:00,NO,"CERT0003206587 
There are two new client positions in XS2886118236 => 

!image-2024-10-14-09-47-49-148.png! 

However, no MT564 NEWM messages have been sent yet. 
As deadline is today, messages should be sent asap to these clients. 

Could you please urgently let me know if the job for sending MT564 for new positions did not run today and, if so, there is a way to execute this job manually (""GenerateByCA"" did not work)? 

Thanks, Bert",cert two new client position x imagepng however mt newm message sent yet deadline today message sent asap client could please urgently let know job sending mt new position run today way execute job manually generatebyca work thanks bert,0.03892875462770462,0.0,218,0.026427944276122963,0.24026781134307384,5.****************,0.2,0.13,0.15,58.04%
Incident,OEKB-6418,Major,M12 PROD - CERT0003206587 - 2nd RMDR Job not executed yet,Bertram Schon,2024-10-14 09:39:00,2024-10-14 09:43:00,NO,"I coincidentally found out that no 2nd RMDR messages have been sent yet for event CERT0003206587 - the job was scheduled for today, 04.00, and is still available under ""Related Jobs"" => 

!image-2024-10-14-09-37-16-540.png! 

Could you please check? 
Thanks, Bert",coincidentally found nd rmdr message sent yet event cert job scheduled today still available related job imagepng could please check thanks bert,-0.10166740790009499,0.0,218,0.026427944276122963,0.27541685197502375,5.****************,0.2,0.13,0.15,63.31%
Incident,OEKB-6417,Medium,"M12 PROD - MRGR Events - Client SECU payments ""BlockedForCutOffTime""",Bertram Schon,2024-10-14 09:09:00,2024-10-14 09:30:00,NO,"We had to process 12 MRGR events on Friday, the Market Payments settled on 11/10 at around 21.26 => 

!image-2024-10-14-09-07-29-133.png! 

However, Client Payments are still in status """"BlockedForCutOffTime"" => 

!image-2024-10-14-09-08-53-092.png! 


Could you please check? 
Thanks, Bert",process mrgr event friday market payment settled around imagepng however client payment still status blockedforcutofftime imagepng could please check thanks bert,0.002550888806581497,0.0,218,0.026427944276122963,0.24936227779835463,5.****************,0.2,0.1,0.15,54.9%
Incident,OEKB-6416,Critical,"Events are in CA status ""executed"" instead of ""waiting payment"" / URGENT",Wallmann GEROLD,2024-10-14 08:28:00,2024-10-14 08:32:00,NO,"The events with payment day = today = 14.10.2024 are all in the CA status ""executed"" instead of ""waiting paymnt"". 

Please also check why there are events in the status ""notified""! 

Please correct as soon as possible.  

Regards, 

GEROLD",event payment day today ca status executed instead waiting paymnt please also check event status notified please correct soon possible regard gerold,-0.030993839725852013,0.0,218,0.026427944276122963,0.257748459931463,1.****************,0.*****************,0.15,0.15,42.86%
Incident,OEKB-6415,Major,M12 PROD - DVCA0003206452 - duplicate not possible,Dalibor VIDIC,2024-10-11 14:35:00,2024-10-11 14:44:00,NO,"DVCA0003206452  

Hello, 

Unfortunately, it was not possible to duplicate the above-mentioned event. An error message was displayed. 

  

Please check! 

  

Thank you and BR, Dalibor",dvca hello unfortunately possible duplicate abovementioned event error message displayed please check thank br dalibor,-0.33169469237327576,0.0,220,0.025561533206507402,0.33292367309331894,3.***************,0.*****************,0.13,0.15,57.7%
Incident,OEKB-6414,Major,M12 PROD - Repair Received Feed,Dalibor VIDIC,2024-10-11 14:24:00,2024-10-11 15:03:00,NO,"US7181721090 

Hello, 

The above ISIN (Philip Morris) contains a tax-free dividend and a taxable dividend. 

This means that there are two DVCA events in this ISIN with the same basic data (Record Date, Ex Date, Payment Date) but with different Dividend rate.  

In M10 If we receive a second message for the same ISIN with the same key data, a dashboard alert is created ant it was possible to view the message and create new event. 

Failed Messages => Failed Received MT564 => More than one feed related to the same corporate action. 

This is solved differently in M12. There is no dashboard alert for this purpose. 

The note for this ISIN can be found under 'Repair Received Feed'. 

My question is whether it is possible to receive a dashboard alert as before. 

The second issue is that we can't view and download the received Swift message in M12, we have to search for it in the Megabroker which is not usable solution to us because we don't normally work with Megabroker. 

We really need them in MEGACOR where we decide what to do with them. 

  

Please check! 

  

Thank you and BR, Dalibor 

 ",u hello isin philip morris contains taxfree dividend taxable dividend mean two dvca event isin basic data record date ex date payment date different dividend rate receive second message isin key data dashboard alert created ant possible view message create new event failed message failed received mt one feed related corporate action solved differently dashboard alert purpose note isin found repair received feed question whether possible receive dashboard alert second issue cant view download received swift message search megabroker usable solution u dont normally work megabroker really need megacor decide please check thank br dalibor,-0.040384288877248764,0.0,220,0.025561533206507402,0.2600960722193122,3.***************,0.*****************,0.13,0.15,46.78%
Incident,OEKB-6413,Major,M12 PROD - XMET0003208466 - Option Number must be editable,Bertram Schon,2024-10-11 10:51:00,2024-10-11 13:38:00,NO,"XMET0003208466 
We received MT564 REPL from our custodian where some option numbers have been changed. 
When trying to update, we noticed that we are not able to edit the option numbers => 

!image-2024-10-11-10-51-00-754.png! 

Could you please check and made this field editable? 

Thanks, Bert",xmet received mt repl custodian option number changed trying update noticed able edit option number imagepng could please check made field editable thanks bert,-0.049987953156232834,0.0,220,0.025561533206507402,0.2624969882890582,5.****************,0.2,0.13,0.15,61.37%
Information Request,OEKB-6412,Medium,Pending Trades - Performance Issue?,Valdes ELIZABEHT,2024-10-11 10:15:00,2024-10-11 10:20:00,NO,"Dear All,  

we received the first pending trades Batch in PROD at 09:21 and it is still processing.  

Can you please check or advise.  

The interface received is attached so you can check the file in the meantime.  

Till now no alert is received.  

KR,  

Eli",dear received first pending trade batch prod still processing please check advise interface received attached check file meantime till alert received kr eli,-0.05662756413221359,0.0,221,0.02513903822451339,0.2641568910330534,0.*****************,0.010483112502500294,0.1,0.06,15.2%
Incident,OEKB-6411,Medium,US TAX Reporting - No Ca Code selection is possible,Wallmann GEROLD,2024-10-11 09:03:00,2024-11-23 18:45:00,NO,"menu = Events / US Tax Reporting 

  

If I go to the field Ca Code I cannot select a Ca Code (like INTR, DVCA). 

This was possible in M10. 

See screen shots attached. 

Please repair. 

 ",menu event u tax reporting go field ca code select ca code like intr dvca possible see screen shot attached please repair,0.013867603614926338,43.0,221,0.02513903822451339,0.24653309909626842,1.****************,0.*****************,0.1,0.15,33.68%
Incident,OEKB-6410,Major,M12 PROD - Instructed quantity exeeds the available quantity,Dalibor VIDIC,2024-10-11 08:34:00,2024-10-11 09:28:00,NO,"INTR00157109 

Hello, 

In the above event we have a Breakdown Instruction (236500, Instructed Quantity 81.615.000). 

However, the client holding is only 81,583,000 and the instruction is correctly ignored. 

What is not correct, is that the client entitlement (for 236500) and market entitlement have been calculated here. 

As was usual in M10, there must be a warning in the form of an {*}e-mail alert{*}: 

*Client Entitlement Calculation Failed: Instructed quantity exeed the available quantity* 

and a *dashboard alert* with error Description: 

*Client Entitlement Calculation Failed - Instructed quantity exeeds the available quantity* 

  

Therefore, the client entitlement for this client and subsequently also the market entitlement cannot be calculated and the Event may not be in Status ""Waiting Payment"". 

  

Please check! 

  

Thank you and BR, Dalibor 

  

  

  

  

  

  

 ",intr hello event breakdown instruction instructed quantity however client holding instruction correctly ignored correct client entitlement market entitlement calculated usual must warning form email alert client entitlement calculation failed instructed quantity exeed available quantity dashboard alert error description client entitlement calculation failed instructed quantity exeeds available quantity therefore client entitlement client subsequently also market entitlement calculated event may status waiting payment please check thank br dalibor,-0.7985807526856661,0.0,221,0.02513903822451339,0.4496451881714165,3.***************,0.*****************,0.13,0.15,75.21%
Incident,OEKB-6409,Major,"Wrong currency in field ""90B::OFFR in swift MT566 / URGENT",Wallmann GEROLD,2024-10-10 15:38:00,2024-10-10 15:43:00,NO,"For payments in foreign currency with FX in Euro  (for example: DKK, RUB etc). the field  

:90B::OFFR//ACTU/ in swift MT566 does not display the correct currency. The correct currency should be the original one (DKK, RUB etc.), but currently EUR is dispalyed.  

See example swift with wrong currency attached.  

In swift MT564 NEWM and swift MT564 REPE the correct currency is displayed.  

 ",payment foreign currency fx euro example dkk rub etc field boffractu swift mt display correct currency correct currency original one dkk rub etc currently eur dispalyed see example swift wrong currency attached swift mt newm swift mt repe correct currency displayed,-0.004868127405643463,0.0,221,0.02513903822451339,0.25121703185141087,1.****************,0.*****************,0.13,0.15,38.88%
Incident,OEKB-6407,Major,M12 PROD - EXOF0003208572 - reject conflicting update not possible,Bertram Schon,2024-10-10 12:13:00,2024-10-10 12:22:00,NO,"EXOF0003208572 
Same issue as yesterday (see OEKB-6402), 
Flow In ID 120280659, message attached. 

Please check! 

Thanks, Bert ",exof issue yesterday see oekb flow id message attached please check thanks bert,0.035858625546097755,0.0,221,0.02513903822451339,0.24103534361347556,5.****************,0.2,0.13,0.15,58.16%
Incident,OEKB-6406,Medium,M12 PROD - To-Do-List - Consolidate Instruction - Ignore functionality missing,Bertram Schon,2024-10-10 11:07:00,2024-10-10 11:11:00,NO,"Could you please provide us with the IGNORE-button in the To-Do-List/""Consolidate Instruction""-screen? 

!image-2024-10-10-10-41-41-472.png! 

Thanks, Bert",could please provide u ignorebutton todolistconsolidate instructionscreen imagepng thanks bert,0.05780380871146917,0.0,221,0.02513903822451339,0.2355490478221327,5.****************,0.2,0.1,0.15,52.83%
Incident,OEKB-6405,Major,M12 PROD - XMET0003208466 - MT564 not available in M12,Bertram Schon,2024-10-10 09:44:00,2024-10-10 09:47:00,NO,"XMET0003208466 

We checked our internal SWIFT IN drop folder and found the attached MT564 from today which is not available in M12. 

FlowOut: 
!image-2024-10-10-09-40-59-492.png! 

Could you please check? 
Thanks, Bert",xmet checked internal swift drop folder found attached mt today available flowout imagepng could please check thanks bert,-0.002864137291908264,0.0,222,0.024723526470339388,0.25071603432297707,5.****************,0.2,0.13,0.15,59.61%
Incident,OEKB-6404,Major,Pending Trades Batch #1 from 10.10.2024 not successfully uploaded in the M12 PROD,Nikolay TSONKOV,2024-10-10 09:28:00,2024-10-10 09:45:00,NO,"Good Morning dears,  

the pending trades batch #1 for 10.10.2024 is not processed.  

  

pls for support 

  

BR,  

Niki",good morning dear pending trade batch processed pls support br niki,0.6130134873092175,0.0,222,0.024723526470339388,0.09674662817269564,2.***************,0.*****************,0.13,0.15,20.57%
Incident,OEKB-6403,Medium,M12 PROD - EXOF0003208572 - Wrong Instruction Status,Bertram Schon,2024-10-09 16:20:00,2024-10-09 16:24:00,NO,"EXOF0003208572  
We received several MT565 instructions (example attached), including contact info in the field 
70E::PACO, which moved to status ""InvalidData"", which is a wrong behavior. 

If the narrative only includes PACO, instructions should not move to ""InvalidData"" but processed accordingly (same behavior as in M10) - in this case status should move to ""WaitingFreezeConfirmation"" immediately. 

Could you please check? 
Thanks, Bert ",exof received several mt instruction example attached including contact info field epaco moved status invaliddata wrong behavior narrative includes paco instruction move invaliddata processed accordingly behavior case status move waitingfreezeconfirmation immediately could please check thanks bert,-0.11240126378834248,0.0,222,0.024723526470339388,0.2781003159470856,5.****************,0.2,0.1,0.15,59.22%
Incident,OEKB-6402,Medium,M12 PROD - EXOF0003208572 - reject conflicting update not possible,Ana Maria Galic,2024-10-09 12:26:00,2024-10-09 12:32:00,NO,"EXOF0003208572 

There is an conflicting update (RMDR) which I wanted to reject, but when pressing the ""compare draft"" button an ""internal error"" pops up: 

  

!image-2024-10-09-12-27-32-944.png! 

  

Could you please check and do the workaround? 

Attached the MT564 RMDR received that caused this conflicting update: 

[^FlowIn_120277793 (1).ID_414d512062697a74616c6b2e71756575d2dfe9662e947f35] 


Thanks 
Regards, 
Ana ",exof conflicting update rmdr wanted reject pressing compare draft button internal error pop imagepng could please check workaround attached mt rmdr received caused conflicting update flowin iddacbeddfeef thanks regard ana,-0.4009582791477442,0.0,222,0.024723526470339388,0.35023956978693604,1.****************,0.037820429797300145,0.1,0.15,45.71%
Information Request,OEKB-6401,Medium,Linux Patch Q3 2024,Nikolay TSONKOV,2024-10-09 10:00:00,2024-11-04 09:38:00,NO,"Good Morning dear colleagues 

There is  upcoming Linux patch which is scheduled as following: on some of the days we will be needing your support 
* Tuesday, 2024.10.15—during the day—Test Systems from MegaCor and Issuer Platform with be patched  Patch from v 19.23 ro  19.24—just fyi the expectation hier ist that no activities needed from  Vermeg 
* Tuesday, 2024.10.22-during the day-- QAS Systems will be patched from v  19.23 to  19.24- - can you pls share contacts from your teams, to whom may we refer in case of support needed? 
* Thursday 2024.11.07 starting after  16:00--  3i Produktion und MegaCor Produktion Systeme + Oracle QAS Patch from  19.23 to  19.24 will be pachted .  we will need your support hier as this is concerning PROD system, can you pls confirm your availability and who will be supporting us from your teams on that day 

  

BR,  

Niki",good morning dear colleague upcoming linux patch scheduled following day needing support tuesday daytest system megacor issuer platform patched patch v ro fyi expectation hier ist activity needed vermeg tuesday day qas system patched v pls share contact team may refer case support needed thursday starting produktion und megacor produktion systeme oracle qas patch pachted need support hier concerning prod system pls confirm availability supporting u team day br niki,0.18225351721048355,25.0,223,0.02431488252138192,0.2044366206973791,2.***************,0.*****************,0.1,0.06,18.72%
Incident,OEKB-6400,Major,M12 PROD - PROX0003206511 - <Vote> - Wrong Mapping,Bertram Schon,2024-10-09 09:57:00,2024-10-09 10:01:00,NO,"PROX0003206511  
was set up according to received seev.001 (attached) from Provider Captrace. 
seev.001 does not include any ""Meeting Votes""-information =>  

!image-2024-10-09-09-54-36-580.png! 

However, outgoing seev.001 messages include a ""Vote""-sequence with incorrect ""default values"" (false) => 

 <Vote> 
                <PrtlVoteAllwd>false</PrtlVoteAllwd> 
                <SpltVoteAllwd>false</SpltVoteAllwd> 
                <BnfclOwnrDsclsr>false</BnfclOwnrDsclsr> 
</Vote> 

Could you please check! 
Thanks, Bert",prox set according received seev attached provider captrace seev include meeting votesinformation imagepng however outgoing seev message include votesequence incorrect default value false vote prtlvoteallwdfalseprtlvoteallwd spltvoteallwdfalsespltvoteallwd bnfclownrdsclsrfalsebnfclownrdsclsr vote could please check thanks bert,-0.12787608243525028,0.0,223,0.02431488252138192,0.28196902060881257,5.****************,0.2,0.13,0.15,64.3%
Information Request,OEKB-6399,Major,URGENT Pending trade batches for 08102024 not processed,Nikolay TSONKOV,2024-10-08 18:06:00,2024-10-08 19:06:00,NO,"Dear all, as i was checking now i saw no batch from Processing trades was today (08102024) processed  

pls for urgent call 

BR,  

niki",dear checking saw batch processing trade today processed pls urgent call br niki,0.029843099415302277,0.0,223,0.02431488252138192,0.24253922514617443,2.***************,0.*****************,0.13,0.06,28.94%
Incident,OEKB-6398,Medium,M12 PROD - EXOF0003208572 - SecuIn Client Payments not correct,Ana Maria Galic,2024-10-08 14:27:00,2024-10-08 14:30:00,NO,"EXOF0003208572 

For this event we received two MT566 (SecuIn and SecuOut booking), the entitlements were correct and the market payments were also correct:  

!image-2024-10-08-14-15-23-759.png! 

!image-2024-10-08-14-15-47-947.png! 

But when I checked the client payments, I noticed that two SecuIn payments were created on the client side and that the resulting quantity consists of decimals: 

!image-2024-10-08-14-18-08-408.png! 

I already checked with Oussema, we cancelled these wrong SecuIn payments and created the correct SecuIn payments manually, so workaround was already done. 

But please check why this problem occured with the client payments. 

Attached you will find the two MT564 received for this event and the two MT566 for these payments:  

[^FlowIn_120195408 (1).ID_414d512062697a74616c6b2e71756575d2dfe96626e14f63] 

[^FlowIn_120261331 (1).ID_414d512062697a74616c6b2e71756575d2dfe9662b6cb95e] 

[^FlowIn_120276111.ID_414d512062697a74616c6b2e71756575d2dfe9662e11dd3e] 

[^FlowIn_120276121.ID_414d512062697a74616c6b2e71756575d2dfe9662e127335] 

  

Thank you 

Best regards,  
Ana",exof event received two mt secuin secuout booking entitlement correct market payment also correct imagepng imagepng checked client payment noticed two secuin payment created client side resulting quantity consists decimal imagepng already checked oussema cancelled wrong secuin payment created correct secuin payment manually workaround already done please check problem occured client payment attached find two mt received event two mt payment flowin iddacbeddfeef flowin iddacbeddfebcbe flowiniddacbeddfeedde flowiniddacbeddfee thank best regard ana,-0.05973606929183006,0.0,223,0.02431488252138192,0.2649340173229575,1.****************,0.037820429797300145,0.1,0.15,32.91%
Incident,OEKB-6397,Medium,Urgent/ M12 PROD - EXOF0003208572 - failed received MT565,Ana Maria Galic,2024-10-08 12:33:00,2024-10-08 13:22:00,NO,"EXOF0003208572 

Can you please urgently check, why these two MT565 are failed? What does this error description mean? NO_ELIGIBLE_PMVT 

!image-2024-10-08-12-32-07-650.png! 

  

Thank you 
Best regards, 
Ana 

 ",exof please urgently check two mt failed error description mean noeligiblepmvt imagepng thank best regard ana,-0.02353176847100258,0.0,223,0.02431488252138192,0.25588294211775064,1.****************,0.037820429797300145,0.1,0.15,31.56%
Information Request,OEKB-6396,Medium,MT564 REPL with an interest rate of 0% is rejected in 3i PROD,Valdes ELIZABEHT,2024-10-07 16:47:00,2024-10-07 17:10:00,NO,"Dear All,  

3i is expecting a 92A or a 92F with a value of 0 in the CASHMOVE, but instead it got a "":92K::INTP//NILP"". This leads t this Error!  

Can you confirm this actual behaviour in M12 altough we received 92A or a 92F in M10? 

  

Thanks & KR,  

Eli 

 ",dear expecting f value cashmove instead got kintpnilp lead error confirm actual behaviour altough received f thanks kr eli,-0.635505449026823,0.0,224,0.023912992862805314,0.40887636225670576,0.*****************,0.010483112502500294,0.1,0.06,36.9%
Incident,OEKB-6395,Medium,M12 PROD - EXOF0003208572 - eligible positions not correct in MT564 FlowOut,Ana Maria Galic,2024-10-07 16:36:00,2024-10-07 16:53:00,NO,"EXOF0003208572 

When checking the MT564 FlowOuts, I noticed that the eligible positions are not correct. 
Attached you will find one FlowOut as an example, the eligible position is stated with 129: 

[^flowOut_120242029 (1).txt] 


The actual eligible position is 1.470,6 - see CCSYS and M12: 

!image-2024-10-07-16-32-37-687.png! 

!image-2024-10-07-16-33-08-224.png! 

  

The eligible positions are stated wrong for all clients. (in the MT564 notifications) 
As far as I noticed, M12 took the eligible positions from the linked CA MRGR0003208130. 

Can you please urgently check this? 

Thanks 

Best Regards, 
Ana  

  

  

  

  

  

 ",exof checking mt flowouts noticed eligible position correct attached find one flowout example eligible position stated flowout txt actual eligible position see ccsys imagepng imagepng eligible position stated wrong client mt notification far noticed took eligible position linked ca mrgr please urgently check thanks best regard ana,0.02574940212070942,0.0,224,0.023912992862805314,0.24356264946982265,1.****************,0.037820429797300145,0.1,0.15,29.71%
Information Request,OEKB-6394,Major,M12 PROD - INTR Events - Missing or wrong interest rates,Dalibor VIDIC,2024-10-07 14:49:00,2024-10-07 14:52:00,YES,"Hello, 

For some INTR events, the 'Price' is not calculated although the annual interest rate is available. 

In addition, the annual interest rate is wrong for most of them. 

  

AT000B077953 

AT0000A1A398 

AT0000A26812 

AT0000A28Z42 

AT0000A2B485 

AT0000A360Q6 

AT000B093562 

AT000B093570 

AT000B120704 

AT0000A296C2  

AT000B067095 

AT0000A1JPY7 

  

The annual interest rate is available for the ISIN below, although it is a post-fixed coupon. 

AT000B015615 

  

We have now manually updated all events, but please check why they were wrong. 

  

Thank you and BR, Dalibor 

 ",hello intr event price calculated although annual interest rate available addition annual interest rate wrong atb ataa ata ataz atab ataq atb atb atb atac atb atajpy annual interest rate available isin although postfixed coupon atb manually updated event please check wrong thank br dalibor,-0.06168342009186745,0.0,224,0.023912992862805314,0.26542085502296686,3.***************,0.*****************,0.13,0.06,34.08%
Incident,OEKB-6393,Major,M12 PROD - AT0000A377X6- Error in the Generated Schedule,Dalibor VIDIC,2024-10-07 12:21:00,2024-10-07 12:27:00,YES,"INTR0003206348 

Hello,  

The interest rate calculated for the above event was correct, but now an update has been received from Referentials and the interest rate is incorrect.  

Coupon Frequency is montly and if you look under 'List financial Instruments' => Generated Schedule, you will find that the calculated interest rates are incorrect. 

  

Please check! 

  

Thank you and BR, Dalibor",intr hello interest rate calculated event correct update received referentials interest rate incorrect coupon frequency montly look list financial instrument generated schedule find calculated interest rate incorrect please check thank br dalibor,-0.33492427691817284,0.0,224,0.023912992862805314,0.3337310692295432,3.***************,0.*****************,0.13,0.15,57.82%
Information Request,OEKB-6392,Medium,M12 PROD - DSCL0003208414 - event blocked?,Ana Maria Galic,2024-10-07 11:47:00,2024-10-07 11:49:00,NO,"DSCL0003208414 

There was an conflicting update which i validated in order to update this event. But after validating the conflicting update, the event was sent to ""validate/ reject manual creation/ updates"" ?! 
We rejected this ""update"", as there was not really an update, but the event is still blocked in this screen: 

!image-2024-10-07-11-42-20-435.png! 

  

And there is no possibility to validate or reject this ""update"" anymore. 

  

!image-2024-10-07-11-43-21-715.png! 

  

Can you please check and unblock this event? I need to do a real update in this event. 

Thanks, regards 
Ana 

 ",dscl conflicting update validated order update event validating conflicting update event sent validate reject manual creation update rejected update really update event still blocked screen imagepng possibility validate reject update anymore imagepng please check unblock event need real update event thanks regard ana,-0.15188580378890038,0.0,224,0.023912992862805314,0.2879714509472251,1.****************,0.037820429797300145,0.1,0.06,22.87%
Incident,OEKB-6391,Medium,M12 PROD - RHDI0003208573 - ADTX not whole in FlowOut,Ana Maria Galic,2024-10-04 15:05:00,2024-10-04 19:26:00,NO,"RHDI0003208573 

I updated the event with following ADTX today:  

!image-2024-10-04-14-59-25-844.png! 

  

But when checking the FlowOuts I noticed that the last sentence was cut off, it stops with ""whereby  the capital"". 

Please find attached one of the FlowOuts.  

Can you please check this? 

Thank you, regards 
Ana  

  

[^flowOut_120227866.txt] 

  

  

  

  

 ",rhdi updated event following adtx today imagepng checking flowouts noticed last sentence cut stop whereby capital please find attached one flowouts please check thank regard ana flowouttxt,-0.0372011698782444,0.0,227,0.022746742438975988,0.2593002924695611,1.****************,0.037820429797300145,0.1,0.15,32.07%
Incident,OEKB-6390,Medium,M12 PROD - EXOF0003208572 - validation of update not possible,Ana Maria Galic,2024-10-04 14:55:00,2024-10-04 15:18:00,NO,"EXOF0003208572 

I updated the event and saved it afterwards and it was visible under ""validate/ reject manual creation/ updates"": 

!image-2024-10-04-14-52-50-108.png! 


I asked Dalibor to validate the update, but when he clicks on ""compare draft"" an internal error pops up: 

!image-2024-10-04-14-53-41-676.png! 

  

Can you please urgently check and fix this problem? This event should be sent out today. 

Thank you, regards 
Ana 

 ",exof updated event saved afterwards visible validate reject manual creation update imagepng asked dalibor validate update click compare draft internal error pop imagepng please urgently check fix problem event sent today thank regard ana,-0.09447471797466278,0.0,227,0.022746742438975988,0.2736186794936657,1.****************,0.037820429797300145,0.1,0.15,34.22%
Incident,OEKB-6384,Major,04102024 Pending trades Batch not succesfully uploaded,Nikolay TSONKOV,2024-10-04 09:39:00,2024-10-04 09:56:00,NO,"Good Morning today ( 04102024)  the pending trades batch #1 was not succesfully uplaoded 

kindly ask for support to upload it together 

BR,  

Niki",good morning today pending trade batch succesfully uplaoded kindly ask support upload together br niki,0.339752497151494,0.0,228,0.0223707718561656,0.1650618757121265,2.***************,0.*****************,0.13,0.15,30.81%
Information Request,OEKB-6378,Medium,03102024 Login in M12 MC and MB was not possible after entering usernam and password,Nikolay TSONKOV,2024-10-03 08:44:00,2024-10-03 08:48:00,NO,"Good Morning dears,  

for your info , today it was not possible user authentication in both systems MB and MC M12 due to locked user LDAP in Active Directory 

user was unlocked at 08:24 

both systems were available at 08:39",good morning dear info today possible user authentication system mb mc due locked user ldap active directory user unlocked system available,0.024026667699217796,0.0,229,0.****************,0.24399333307519555,2.***************,0.*****************,0.1,0.06,24.65%
Incident,OEKB-6377,Medium,M12 PROD - SPLR0003208177 - wrong behaviour after ad-hoc booking,Ana Maria Galic,2024-10-02 16:15:00,2024-10-02 16:34:00,NO,"SPLR0003208177 

I processed a new workaround with ad-hoc booking today (as described from Oussema last week), as the fraction currency in the event was set up in EUR but we needed to make a fraction payment also in CHF. However, after validating the ad-hoc booking, the market payment cash booking was rejected.  
I had a call with Oussema and we noticed that the creditor account in the booking was not correct, it adressed the EUR account: 

  

!image-2024-10-02-16-08-38-958.png! 


It should adress the CHF account:  

!image-2024-10-02-16-09-57-457.png! 

However, we did a manual workaround with Oussema and the payment was fixed and successful afterwards.  
But can you please check and fix this issue as this is a wrong behaviour from M12. 

Thanks! 

Best regards, 
Ana 

 ",splr processed new workaround adhoc booking today described oussema last week fraction currency event set eur needed make fraction payment also chf however validating adhoc booking market payment cash booking rejected call oussema noticed creditor account booking correct adressed eur account imagepng adress chf account imagepng however manual workaround oussema payment fixed successful afterwards please check fix issue wrong behaviour thanks best regard ana,-0.*****************,0.0,229,0.****************,0.*****************,1.****************,0.037820429797300145,0.1,0.15,39.06%
Incident,OEKB-6375,Medium,M12 PROD - MRGR0003208130 - reject conflicting update not possible,Ana Maria Galic,2024-10-02 15:46:00,2024-10-02 16:16:00,NO,"MRGR0003208130 

There is a conflicting update which I wanted to reject, but when pressing the ""compare draft"" button an ""internal error"" pops up: 

  

!image-2024-10-02-15-47-36-204.png! 

  

  

Could you please check and do the workaround? 
Thanks 
Regards, 
Ana ",mrgr conflicting update wanted reject pressing compare draft button internal error pop imagepng could please check workaround thanks regard ana,-0.*****************,0.0,229,0.****************,0.*****************,1.****************,0.037820429797300145,0.1,0.15,34.05%
Information Request,OEKB-6374,Major,"SIX SIS MT564 stucked in MegaBroker ""Identification Failed""",Valdes ELIZABEHT,2024-10-02 15:37:00,2024-10-02 15:45:00,NO,"Hi all,  

I've just imported together with OeKB IT missing MT564 from SIX SIS as the incoming routing was not set up correctly.  

Now all 171 flows are receveid in MB but in status ""Identification Failed"" 

See list attached.  

Can you plesae check and advise, as we have to load all this MT564.  

  

Thanks & KR,  

Eli",hi ive imported together oekb missing mt six si incoming routing set correctly flow receveid mb status identification failed see list attached plesae check advise load mt thanks kr eli,-0.*****************,0.0,229,0.****************,0.*****************,0.*****************,0.010483112502500294,0.13,0.06,24.52%
Incident,OEKB-6372,Blocker,M12 MegaCor am 30.09.2024 nicht erreichbar,Automation test,2024-10-02 13:13:00,2024-10-02 13:21:00,NO,"Am 30.09 ( Montag ) um 08:25 wurde festgestellt dass M12 MegaCor App Prod nicht erreichbar ist. 

Die Störung wurde beim Consol als ticket 376605 ** gemeldet und beim Vermeg als Jira ticket OEKB-6358 gemeldet 

  

Laut Consol Analyse im Ticket  376605 am 28.09.2024 Samstag wurde ein Service gehängt  

  

Sep 28 11:00:57 lnxpweb01.oekb.co.at MegaCor-standalone.sh[1367835]: [thread 2764760 also had an error] 
Sep 28 11:00:57 lnxpweb01.oekb.co.at MegaCor-standalone.sh[1367835]: [409839.746s][warning][os,thread] Failed to start thread ""Unknown thread"" - pthread_create failed (EAGAIN) for attributes: stacksize: 1024k,> 
Sep 28 11:00:57 lnxpweb01.oekb.co.at MegaCor-standalone.sh[1367835]: [409839.747s][warning][os,thread] Failed to start the native thread for java.lang.Thread ""Thread-22012"" 

nach dem neustart wurde die Störung soeben gehoben 

  


[This ticket was automatically created]",montag um wurde festgestellt das megacor app prod nicht erreichbar ist die strung wurde beim consol al ticket gemeldet und beim vermeg al jira ticket oekb gemeldet laut consol analyse im ticket samstag wurde ein service gehngt sep lnxpweboekbcoat megacorstandalonesh thread also error sep lnxpweboekbcoat megacorstandalonesh swarningosthread failed start thread unknown thread pthreadcreate failed eagain attribute stacksize k sep lnxpweboekbcoat megacorstandalonesh swarningosthread failed start native thread javalangthread thread nach dem neustart wurde die strung soeben gehoben ticket automatically created,-0.03646635264158249,0.0,229,0.****************,0.2591165881603956,0.*****************,0.0012921485771927904,0.14,0.15,32.56%
Incident,OEKB-6371,Major,MB: MT564_Parsing and mapping Issue,Valdes ELIZABEHT,2024-10-02 11:09:00,2024-10-02 11:45:00,NO,"Hi Oussema,  

today again a flow in is stukced in MB: Flow ID: ********* 

*****FlowIn Identification: 
1) SenderReceiver with identifier (MEGACOR ) found related to the device ( MC.TO.MB ) 
2) 19 instances of FlowInConfiguration are found for the sender ( MEGACOR ). 
3) Cannot find a FlowInConfiguration related to the device ( MC.TO.MB ). 
4) FlowIn Configuration with identifier ( COR_DATACA_FLOW_IN ) found for the Sender with identifier ( MEGACOR ) and the InputDevice with identifier ( MC.TO.MB ) using the preparsing grammar with identifier ( Notification_XML ). 
5) No Domain Configuration found related to the flowIn (*********) 
*****Single Flow Record Type Config Identification: 
1) The configuration ( COR_DATACA_FLOW_IN ) has one RecordTypeConfiguration ( 160016 ) 

*****Single Flow Parsing and Mapping: 
Operation fails due to the following exception:PALM-15053: The content -3,567 of the value uninstructedBalance of DataCA.accountDetails.AccountDetails.0 is not valid. It should verify this pattern ^\{0,1}((((\d\{1,2},)\{0,1})((\d\{3}(,)\{0,1})*)(\d\{3}))|(\d\{1,2}))(.\d\{0,20})\{0,1}$. 

  

[^FlowIn_*********.xml] 

^Plese check and advise.^  

^Thanks & KR,^  

^Eli^",hi oussema today flow stukced mb flow id flowin identification senderreceiver identifier megacor found related device mctomb instance flowinconfiguration found sender megacor find flowinconfiguration related device mctomb flowin configuration identifier cordatacaflowin found sender identifier megacor inputdevice identifier mctomb using preparsing grammar identifier notificationxml domain configuration found related flowin single flow record type config identification configuration cordatacaflowin one recordtypeconfiguration single flow parsing mapping operation fails due following exceptionpalm content value uninstructedbalance datacaaccountdetailsaccountdetails valid verify pattern ddddd flowinxml plese check advise thanks kr eli,-0.****************,0.0,229,0.****************,0.*****************,0.*****************,0.010483112502500294,0.13,0.15,56.5%
Incident,OEKB-6370,Medium,M12 PROD - OMET0003208290 - reject conflicting update not possible,Ana Maria Galic,2024-10-01 16:01:00,2024-10-01 16:50:00,YES,"OMET0003208290 

There is a conflicting update which I wanted to reject, but when pressing the ""compare draft"" button nothing happens. 

  

!image-2024-10-01-16-01-34-390.png! 

  

Could you please check? 
Thanks 
Regards, 
Ana ",omet conflicting update wanted reject pressing compare draft button nothing happens imagepng could please check thanks regard ana,-0.*****************,0.0,230,0.*****************,0.****************,1.****************,0.037820429797300145,0.1,0.15,31.98%
Information Request,OEKB-6369,Critical,Pending trades Batch #2 from 01.10.2024 is not in the system,Nikolay TSONKOV,2024-10-01 11:25:00,2024-10-01 11:27:00,NO,"Hello colleagues,  

we noticed Batch #2 from 01.10.2024 is not successfully uploaded 

  

can i please ask for support to upload this together 

  

BR,  

Niki",hello colleague noticed batch successfully uploaded please ask support upload together br niki,0.11515631154179573,0.0,230,0.*****************,0.22121092211455107,2.***************,0.*****************,0.15,0.06,28.74%
Incident,OEKB-6368,Major,Flow Outs - 56 flows Generation Failed MT568,Valdes ELIZABEHT,2024-10-01 11:17:00,2024-10-01 11:20:00,NO,"Dear All,  

I've found some flows in status : Generation Failed with following Error:  

  

*****Message Generation Failed: 
PALM-15037: The content +++ Additional information +++ 
Offer by UBM Development AG to the 
holders of 3,125pct UBM-Anleihe 
2021–2026 (AT0000A2QS11) and 
2,750pct UBM-Anleihe 2019-2025 
(see also separate EXOF under ISIN 
AT0000A2AX04) to exchange their 
bonds into new 7,00pct UBM Green 
Bond 2024-2029 (ISIN AT0000A3FFK1) 
subject to the terms and of the value Narrative[block4.SeqC.F70E.ADTX.Narrative] is not valid. It should verify this pattern (?:(?:((?:(?:[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]\{1,35})(?:(?:\r\n)|(?:\n))?)\{1,10}+))). The length of this value should be between 1 and 368. 
*****Message Generation Failed: 
PALM-15037: The content +++ Additional information +++ 
Offer by UBM Development AG to the 
holders of 3,125pct UBM-Anleihe 
2021–2026 (AT0000A2QS11) and 
2,750pct UBM-Anleihe 2019-2025 
(see also separate EXOF under ISIN 
AT0000A2AX04) to exchange their 
bonds into new 7,00pct UBM Green 
Bond 2024-2029 (ISIN AT0000A3FFK1) 
subject to the terms and of the value Narrative[block4.SeqC.F70E.ADTX.Narrative] is not valid. It should verify this pattern (?:(?:((?:(?:[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]\{1,35})(?:(?:\r\n)|(?:\n))?)\{1,10}+))). The length of this value should be between 1 and 368. 

  

The related flow in is attached as well. Can you please check and advise.  

Thanks & KR,  

Eli",dear ive found flow status generation failed following error message generation failed palm content additional information offer ubm development ag holder pct ubmanleihe ataqs pct ubmanleihe see also separate exof isin ataax exchange bond new pct ubm green bond isin ataffk subject term value narrativeblockseqcfeadtxnarrative valid verify pattern azaz rnn length value message generation failed palm content additional information offer ubm development ag holder pct ubmanleihe ataqs pct ubmanleihe see also separate exof isin ataax exchange bond new pct ubm green bond isin ataffk subject term value narrativeblockseqcfeadtxnarrative valid verify pattern azaz rnn length value related flow attached well please check advise thanks kr eli,-0.08093230426311493,0.0,230,0.*****************,0.27023307606577873,0.*****************,0.010483112502500294,0.13,0.15,34.11%
Information Request,OEKB-6367,Blocker,Login Into M12 Megacor and MegaBroker not possible,Nikolay TSONKOV,2024-10-01 08:11:00,2024-10-01 08:21:00,NO,"Good Morning dear all,  

currently it is not possible to login into M12 MegaBroker and MegaCor  

  

attached the error msg 

  

i will attached asap and the logs 

  

at the same time issue is also addressed to Consol under ticket 376625  

  

BR,  

Niki",good morning dear currently possible login megabroker megacor attached error msg attached asap log time issue also addressed consol ticket br niki,-0.02486215904355049,0.0,231,0.02127973643837717,0.2562155397608876,2.***************,0.*****************,0.14,0.06,32.49%
Information Request,OEKB-6366,Medium,M12 PROD - EXOF0003208292/ EXOF0003208291 - MT568 not sent,Ana Maria Galic,2024-09-30 17:37:00,2024-09-30 17:41:00,NO,"EXOF0003208292 and EXOF0003208291 

I set up those events today, MT564 were sent accordingly but the linked MT568 were not sent. 

!image-2024-09-30-17-33-19-011.png! 

  

!image-2024-09-30-17-33-30-960.png! 

  

  

Could you please urgently check why the MT568 were not sent, it is really important. 

Thanks, regards 
Ana 

 ",exof exof set event today mt sent accordingly linked mt sent imagepng imagepng could please urgently check mt sent really important thanks regard ana,0.1436035819351673,0.0,231,0.02127973643837717,0.21409910451620817,1.****************,0.037820429797300145,0.1,0.06,11.79%
Information Request,OEKB-6365,Major,M12 PROD -DVCA00098507 MT566 OUT technically rejected,Dalibor VIDIC,2024-09-30 17:15:00,2024-09-30 17:21:00,NO,"Hello,  

the 'AT0000A2SGH0' dividend was processed today and confirmed by MT566 message. However, the messages are technically rejected in megabroker. 

  

Please check! 

  

Thank you and BR, Dalibor",hello atasgh dividend processed today confirmed mt message however message technically rejected megabroker please check thank br dalibor,-0.09264291822910309,0.0,231,0.02127973643837717,0.27316072955727577,3.***************,0.*****************,0.13,0.06,35.24%
Incident,OEKB-6364,Major,MB: MT564_SWIFT_DC_FLOW_IN - Errors,Valdes ELIZABEHT,2024-09-30 12:05:00,2024-09-30 12:11:00,NO,"Dear All,  

as we are missing MT564 from Custodians, I started to check MB and came accross following list attached:  
* Determining Receivers Failed For Single Row: 63 Flows 
* Parsing and Mapping Failed: 22 Flows 

  

Please check and revert.  

Thanks & KR,  

Eli 

 ",dear missing mt custodian started check mb came accross following list attached determining receiver failed single row flow parsing mapping failed flow please check revert thanks kr eli,-0.5704411454498768,0.0,231,0.02127973643837717,0.3926102863624692,0.*****************,0.010483112502500294,0.13,0.15,52.46%
Information Request,OEKB-6363,Critical,Client Pamynets - Waiting Settlment Confirmation,Nikolay TSONKOV,2024-09-30 11:49:00,2024-09-30 11:57:00,NO,"Hello attached,  

there is a list with payments wich are not being processed 

  

Can we pls urgently check ? 

  

 ",hello attached list payment wich processed pls urgently check,0.0032391175627708435,0.0,231,0.02127973643837717,0.2491902206093073,2.***************,0.*****************,0.15,0.06,32.93%
Incident,OEKB-6362,Medium,Job Client Notification Generation was executing more than 2 hours to check why,Nikolay TSONKOV,2024-09-30 11:37:00,2024-09-30 12:24:00,NO,"Hello, as today working togheter on ticket 6359 with Oussema, we saw there was Job Client Notification Generation which was in status Executing for more than 2 Hours 

  

Ousseam took care and job was exected manually, attached are the logs to check why initially the job was executing so long 

  

 ",hello today working togheter ticket oussema saw job client notification generation status executing hour ousseam took care job exected manually attached log check initially job executing long,-0.010873161256313324,0.0,231,0.02127973643837717,0.25271829031407833,2.***************,0.*****************,0.1,0.15,39.46%
Incident,OEKB-6361,Medium,MB Flow In with Error - Sender OEKB_ZWP,Valdes ELIZABEHT,2024-09-30 09:52:00,2024-09-30 10:00:00,NO,"Today at 08:16:30 we received our Securities Event Files which lead to an Error on MB side.  

Please check and revert.  

!image-2024-09-30-09-52-07-397.png! 

Thanks & KR,  

Eli",today received security event file lead error mb side please check revert imagepng thanks kr eli,-0.7207362204790115,0.0,232,0.02092801333199164,0.4301840551197529,0.*****************,0.010483112502500294,0.1,0.15,53.6%
Incident,OEKB-6360,Major,Pending trades Batch #1 from 28.09.2024 is not in the system,Nikolay TSONKOV,2024-09-30 08:59:00,2024-09-30 09:10:00,NO,"Dear colleagues,  

Pending trade batch #1 from 28.09.2024 is nto in the system 

  

can we pls have a call to uplaod the file 

BR,  

Niki",dear colleague pending trade batch nto system pls call uplaod file br niki,-0.03218730539083481,0.0,232,0.02092801333199164,0.2580468263477087,2.***************,0.*****************,0.13,0.15,44.76%
Incident,OEKB-6359,Major,M12 PROD - The CA status of the events is not correct - MT564REPE,Dalibor VIDIC,2024-09-30 08:53:00,2024-09-30 09:46:00,NO,"Hello,  

The 'CA Status' is not correct for some events. Some events have the status 'Executed' instead of 'Waiting Payment"". 

And it looks like the MT564REPE was not sent for any event. 

Please check urgently! 

  

Thank you and BR, Dalibor",hello ca status correct event event status executed instead waiting payment look like mtrepe sent event please check urgently thank br dalibor,0.002858094871044159,0.0,232,0.02092801333199164,0.24928547628223896,3.***************,0.*****************,0.13,0.15,45.16%
Incident,OEKB-6358,Blocker,M12 PROD ist not Available,Nikolay TSONKOV,2024-09-30 08:33:00,2024-09-30 09:12:00,NO,"Good Morning , this is for your Info that M12 PROD ist not available 

i have opened a ticket  376605 for Consol  

  

attached you can see the error + the default log is needed",good morning info prod ist available opened ticket consol attached see error default log needed,-0.00285523384809494,0.0,232,0.02092801333199164,0.25071380846202374,2.***************,0.*****************,0.14,0.15,45.16%
Incident,OEKB-6357,Major,"M12 PROD - DVCA00098956 Client Payment ""WaitingInstructionConfirmation""",Dalibor VIDIC,2024-09-27 17:03:00,2024-09-27 17:10:00,NO,"DVCA00098956 - NL00126503600 

Hello,  

As discussed with Oussema, please check why the claim that was automatically created is not being posted and why the instruction cannot be found in CCSYS.  

We have received a claim (MT566) from the custodian. The Client and Market Entitlement has been automatically calculated in M12. 

I have validated the ‘Market Payment ’. 

The instruction was sent to CCSYS but the client payment is still on ‘WaitingInstructionConfirmation” and and instruction cannot be found in CCSYS. 

  

Thank you and BR, Dalibor 

  

  

  

  

  

 ",dvca nl hello discussed oussema please check claim automatically created posted instruction found ccsys received claim mt custodian client market entitlement automatically calculated validated market payment instruction sent ccsys client payment still waitinginstructionconfirmation instruction found ccsys thank br dalibor,-0.01437312737107277,0.0,234,0.02024191144580439,0.2535932818427682,3.***************,0.*****************,0.13,0.15,45.8%
Information Request,OEKB-6356,Medium,M12 PROD - ISIN Request for NL00150028M1 failed?,Ana Maria Galic,2024-09-27 16:15:00,2024-09-27 16:42:00,NO,"Hi,  

we received MT564 with error ""Tradable Asset with alternative code NL00150028M1 not found"" on 25.09.2024. ISIN is available in WM since 26.09. and should have been received by ISIN Request Job.  

However, the status of the message is the same since receiving the message (see also update date) 

!image-2024-09-27-16-14-17-416.png! 

  

Please check if the ISIN request job works accordingly or not. 

Thanks, 
Ana 

 ",hi received mt error tradable asset alternative code nlm found isin available wm since received isin request job however status message since receiving message see also update date imagepng please check isin request job work accordingly thanks ana,-0.029718324542045593,0.0,234,0.02024191144580439,0.2574295811355114,1.****************,0.037820429797300145,0.1,0.06,18.29%
Incident,OEKB-6355,Major,M12 PROD - SPLR0003208177 - MT564 REPL sent for unknown reasons,Bertram Schon,2024-09-27 09:06:00,2024-09-27 09:38:00,NO,"SPLR0003208177 
was set up manually yesterday, MT564 NEWM sent accordingly. 

Today at 07.15, MT564 REPL messages have been sent to all clients without any change in data as no feed was received or update has been made => 

!image-2024-09-27-09-00-38-468.png! 

Could you please check why these REPL messages were sent and skip the sending of these unneccessary messages as some clients are yet complaining about it. 

Same behavior was detected for event LIQU0003206314 (REPL sent for unknown reasons on 23/09, 20:15), so I guess this happens for several events... 

Please check! 
Thanks, Bert",splr set manually yesterday mt newm sent accordingly today mt repl message sent client without change data feed received update made imagepng could please check repl message sent skip sending unneccessary message client yet complaining behavior detected event liqu repl sent unknown reason guess happens several event please check thanks bert,-0.30101724341511726,0.0,235,0.019907342077733686,0.3252543108537793,5.****************,0.2,0.13,0.15,70.79%
Incident,OEKB-6354,Major,"M12 PROD - ""SendConfirmedEventsToCCSYS"" failed",Bertram Schon,2024-09-26 15:42:00,2024-09-26 15:45:00,NO,"Please check why the ""ConfirmedEvents""-csv-file was not sent to CCSYS since 24/09. 

Logs,screenshots etc. will be provided by IT-S colleagues. 

Thanks! 
BR Bert",please check confirmedeventscsvfile sent ccsys since logsscreenshots etc provided colleague thanks br bert,0.08775379229336977,0.0,235,0.019907342077733686,0.22806155192665756,5.****************,0.2,0.13,0.15,56.21%
Incident,OEKB-6353,Medium,M12 PROD - To-Do-List - Failed received seev.046 not understandable,Bertram Schon,2024-09-25 16:20:00,2024-09-25 16:30:00,YES,"On 23/09, we received a seev.046 message which was found under ""List Received Disclosure Request Cancellation"" with Notification Status ""Impacted"" => 

!image-2024-09-25-16-18-10-569.png! 


However, we also see this message in the To-Do-List under ""Failed received seev.046"" which is not understandable for us => 



!image-2024-09-25-16-17-27-096.png! 

Moreover, we miss the IGNORE-functionality here. 

Could you please check? 

Thanks, Bert",received seev message found list received disclosure request cancellation notification status impacted imagepng however also see message todolist failed received seev understandable u imagepng moreover miss ignorefunctionality could please check thanks bert,-0.*****************,0.0,236,0.019578302654913008,0.*****************,5.****************,0.2,0.1,0.15,57.35%
Incident,OEKB-6352,Medium,M12 PROD - REDM00078395 - MP Reversal booked incorrectly,Bertram Schon,2024-09-25 14:25:00,2024-10-07 09:23:00,NO,"REDM00078395 
We had to reverse MP as it was processed twice in error. 
When checking the reversal bookings, we found out that the ""Market Payment Instruction Reverse"" was generated incorrectly, with counterpart 9232100 instead of the agent's account 221700 =>  


!image-2024-09-25-14-02-08-827.png! 


Could you please check? 

As a workaround, we amended the instruction directly in CCSYS and received the ACK manually, MT566 REVR was sent to the Agent. 

BR Bert",redm reverse mp processed twice error checking reversal booking found market payment instruction reverse generated incorrectly counterpart instead agent account imagepng could please check workaround amended instruction directly ccsys received ack manually mt revr sent agent br bert,-0.*****************,11.0,236,0.019578302654913008,0.*****************,5.****************,0.2,0.1,0.15,57.92%
Incident,OEKB-6351,Major,"M12 PROD - Error when trying to ""List Events""",Bertram Schon,2024-09-25 11:32:00,2024-09-25 11:39:00,NO,"Could you please check the following: 

When trying to list events by searching for the relevant ISINs and event code REDM, I found no result, although an event exists. 

After closing the List Event-window and trying to open another one, the following error popped up => 

!image-2024-09-25-11-32-38-899.png! 

BR Bert",could please check following trying list event searching relevant isins event code redm found result although event exists closing list eventwindow trying open another one following error popped imagepng br bert,-0.*****************,0.0,236,0.019578302654913008,0.*****************,5.****************,0.2,0.13,0.15,65.73%
Incident,OEKB-6350,Medium,CA status is wrong,Wallmann GEROLD,2024-09-25 10:39:00,2024-09-25 10:50:00,NO,"Today we had a lot of events which where in CA status ""executed"" instead of ""waiting payment"". 

An automatic job set these events to CA status confirmed - which is not correct. 

  

Please correct as soon as possible. 

  

Thank you 

  

Regards 

  

GEROLD 

 ",today lot event ca status executed instead waiting payment automatic job set event ca status confirmed correct please correct soon possible thank regard gerold,-0.15640880167484283,0.0,236,0.019578302654913008,0.2891022004187107,1.****************,0.*****************,0.1,0.15,40.07%
Incident,OEKB-6349,Medium,M12 PROD - XMET0003207020 - update not shown although validated/ saving of update failed,Ana Maria Galic,2024-09-25 10:00:00,2024-09-25 10:04:00,NO,"XMET0003207020  

Event was updated on friday 20/09/2024 and was successfully saved and validated. Today I noticed that the status is still preliminary and that nothing was updated in the event, although the update was made on friday.  

!image-2024-09-25-09-55-53-177.png! 

  

I tried to update the event again with all the ""lost"" data but saving the update was not possible - when i click on the ""save"" button, nothing happens. 

Could you please urgently check?  

Thanks 

 ",xmet event updated friday successfully saved validated today noticed status still preliminary nothing updated event although update made friday imagepng tried update event lost data saving update possible click save button nothing happens could please urgently check thanks,-0.1838561501353979,0.0,237,0.01925470177538692,0.2959640375338495,1.****************,0.037820429797300145,0.1,0.15,37.57%
Incident,OEKB-6348,Medium,M12 PROD - DRIP00014507/ OMET0003208066 - reject conflicting update not possible,Ana Maria Galic,2024-09-25 09:10:00,2024-09-25 09:40:00,NO,"DRIP00014507 

There is a conflicting update which I wanted to reject, but when pressing the ""compare draft"" button an ""internal error"" pops up: 

  

!image-2024-09-25-09-10-32-659.png! 

  

  

  

For event OMET0003208066 I also wanted to reject an update but when pressing  ""compare draft"" button nothing happens, not even an error. 

  

Could you please check? 
Thanks 
Regards, 
Ana ",drip conflicting update wanted reject pressing compare draft button internal error pop imagepng event omet also wanted reject update pressing compare draft button nothing happens even error could please check thanks regard ana,-0.1044198926538229,0.0,237,0.01925470177538692,0.2761049731634557,1.****************,0.037820429797300145,0.1,0.15,34.59%
Incident,OEKB-6347,Medium,M12 PROD - EXOF0003208094 - Automatic Reprocessing of failed received feed did not work,Bertram Schon,2024-09-24 16:51:00,2024-09-24 18:13:00,NO,"MT564 was received which unknown SECU IN ISIN. 
When checking the ISIN Request process with Oussema, it turned out that the ISIN was requested successfully, however, the reprocessing of the failed message did not take place and had to be processed manually. 

Could you please check the relevant job? 

Thanks, Bert",mt received unknown secu isin checking isin request process oussema turned isin requested successfully however reprocessing failed message take place processed manually could please check relevant job thanks bert,-0.1677897572517395,0.0,237,0.01925470177538692,0.2919474393129349,5.****************,0.2,0.1,0.15,61.29%
Information Request,OEKB-6346,Critical,M12 PROD - DRIP00014202 - Saving of update failed,Bertram Schon,2024-09-24 15:23:00,2024-09-24 18:04:00,NO,"DRIP00014202 
We must update the event by adding the SECU Rate, Fraction Price, Pay Date, comment. 

After pressing the save button, no message popped up and no draft was created, no update saved. 

Please urgently check! 
Thanks, Bert 

  

  

 ",drip must update event adding secu rate fraction price pay date comment pressing save button message popped draft created update saved please urgently check thanks bert,0.011257987469434738,0.0,237,0.01925470177538692,0.24718550313264132,5.****************,0.2,0.15,0.06,48.58%
Incident,OEKB-6345,Major,M12 PROD - DVCA Event - MT564 REPE- Field 92J,Dalibor VIDIC,2024-09-24 14:52:00,2024-09-24 14:54:00,NO,"Hello, 

Please check MT564REPE outgoing messages.  

Field :92J::GRSS//TXBL/USD2,6  - should show the gross dividend rate (0,2 USD) and not the total taxable gross amount (2,6 USD). 

Please check! 

  

Thank you and BR, Dalibor 

  

 ",hello please check mtrepe outgoing message field jgrsstxblusd show gross dividend rate usd total taxable gross amount usd please check thank br dalibor,0.024350032210350037,0.0,237,0.01925470177538692,0.2439124919474125,3.***************,0.*****************,0.13,0.15,44.35%
Information Request,OEKB-6344,Medium,"M12 PROD - ""Repair Received Feed"" - Message not downloadable?",Bertram Schon,2024-09-24 12:07:00,2024-09-24 12:53:00,NO,"When checking  ""Repair Received Feed""-list, we do not find a way to download the message? 

!image-2024-09-24-12-07-05-341.png! 

Could you please check if you can make these messages available in M12? 
It's not very userfriendly to search for those in MegaBroker... 

Thanks, Bert",checking repair received feedlist find way download message imagepng could please check make message available userfriendly search megabroker thanks bert,0.06836326234042645,0.0,237,0.01925470177538692,0.2329091844148934,5.****************,0.2,0.1,0.06,38.94%
Information Request,OEKB-6343,Medium,Can we deinstall Firefox fom the old M10 Env,Nikolay TSONKOV,2024-09-23 15:52:00,2024-09-23 19:21:00,NO,"Hello Team,  

can we deinstall Firefox from the old M10 MegaCor Servers? 

  

do you see any negative impact ? 

the reason is not to constantly install updates on those servers in terms of Firefox 

  

BR,  

Niki",hello team deinstall firefox old megacor server see negative impact reason constantly install update server term firefox br niki,-0.7702222242951393,0.0,238,0.018936449547941457,0.44255555607378483,2.***************,0.*****************,0.1,0.06,54.44%
Incident,OEKB-6342,Medium,M12 PROD - Mass REDM Events - Compare Screen incomplete,Bertram Schon,2024-09-23 14:42:00,2024-09-23 18:31:00,NO,"We received several MT564 REPL from 3i for mass REDM events with new CASE option including SECU OUT SECU IN CASH IN movements. 

When checking the compare screen, we noticed that the movements are not shown => 

!image-2024-09-23-14-41-45-088.png! 


So, we have no option to check the new SECU IN ISIN, parity in/out, cash rate, agent flags etc.!? 

Could you please check? 

Thanks, Bert",received several mt repl mass redm event new case option including secu secu cash movement checking compare screen noticed movement shown imagepng option check new secu isin parity inout cash rate agent flag etc could please check thanks bert,0.047898534685373306,0.0,238,0.018936449547941457,0.23802536632865667,5.****************,0.2,0.1,0.15,53.2%
Incident,OEKB-6341,Medium,M12 PROD - CHAN0003207394 - Event Purpose mapped wrong,Ana Maria Galic,2024-09-23 13:22:00,2024-09-23 13:26:00,YES,"CHAN0003207394 

The field ""event purpose"" under General Information is filled with ""name"", which is wrong. In the MT564 we received, the event purpose is clearly ""TERM"": 

!image-2024-09-23-13-19-03-206.png! 

  

!image-2024-09-23-13-19-17-081.png! 

  

I changed it manually in the event, but can you please check why it's not mapped correctly in M12 from the MT564. 

[^FlowIn_120186323.ID_414d512062697a74616c6b2e71756575d2dfe96626517c04] 

Thanks, regards 
Ana 

  

 ",chan field event purpose general information filled name wrong mt received event purpose clearly term imagepng imagepng changed manually event please check mapped correctly mt flowiniddacbeddfec thanks regard ana,-0.02801378443837166,0.0,238,0.018936449547941457,0.2570034461095929,1.****************,0.037820429797300145,0.1,0.15,31.72%
Information Request,OEKB-6339,Major,M12 PROD - CERT0003206587 - MT565 reject in error,Bertram Schon,2024-09-23 11:54:00,2024-09-23 11:58:00,NO,"CERT0003206587  
MT565 (attached) was received containing narrative :COMP//CERT:'A' and rejected by the system in error. 

Instruction should move in status ""Invalid Data"", the user should decide if it will be validated or rejected. 

Please check! 
Thanks, Bert",cert mt attached received containing narrative compcerta rejected system error instruction move status invalid data user decide validated rejected please check thanks bert,-0.15497657284140587,0.0,238,0.018936449547941457,0.28874414321035147,5.****************,0.2,0.13,0.06,51.81%
Information Request,OEKB-6338,Critical,M12 PROD - EXOF0000063705 - repair invalid market payment failed/ error misleading,Ana Maria Galic,2024-09-23 10:30:00,2024-09-23 11:19:00,NO,"EXOF0000063705 

Market payment received in status invalid data with error ""no pending trade found"". 

!image-2024-09-23-10-26-17-515.png! 

Please check this error message, it is misleading. We also noticed the ""origin 

 market claim"", which is wrong: 

!image-2024-09-23-10-29-47-842.png! 

Moreover, repair failed  

!image-2024-09-23-10-27-59-703.png! 

Please check, thanks. 

Regards, 
Ana 

  

  

  

  

 ",exof market payment received status invalid data error pending trade found imagepng please check error message misleading also noticed origin market claim wrong imagepng moreover repair failed imagepng please check thanks regard ana,-0.852678881958127,0.0,239,0.018623457567133697,0.46316972048953176,1.****************,0.037820429797300145,0.15,0.06,56.65%
Incident,OEKB-6337,Major,M12 PROD - PROX0003206717 - Several seev.001 REPL sent to clients in error,Bertram Schon,2024-09-20 16:43:00,2024-09-20 16:56:00,NO,"PROX0003206717 
We received some client complaints about the fact that we have sent several seev.001 REPL messages for unknown reasons (only 1 REPL was sent correctly, due to a manual update by Ana) => 

!image-2024-09-20-16-40-27-716.png! 

Could you please urgently check and prevent the system from sending useless REPL messages? 

Thanks, Bert 

  

  

 ",prox received client complaint fact sent several seev repl message unknown reason repl sent correctly due manual update ana imagepng could please urgently check prevent system sending useless repl message thanks bert,-0.32610902190208435,0.0,241,0.01801290800557516,0.3315272554755211,5.****************,0.2,0.13,0.15,71.73%
Information Request,OEKB-6336,Critical,M12 PROD - DSCL0000063719 - Saving of update failed,Ana Maria Galic,2024-09-20 12:39:00,2024-09-24 10:19:00,NO,"DSCL0000063719 
Update is not possible again due to blocked event in Megabroker ( ? ) Please check workaround (see ticket OEKB-6268). 

  

Thanks 

Regards,  
Ana 

  

  

  

 ",dscl update possible due blocked event megabroker please check workaround see ticket oekb thanks regard ana,-0.31338005512952805,3.0,241,0.01801290800557516,0.328345013782382,1.****************,0.037820429797300145,0.15,0.06,36.42%
Incident,OEKB-6335,Medium,M12 PROD - Search functionality - ISIN Input criteria (input without last technical digit),Bertram Schon,2024-09-20 10:31:00,2024-09-20 10:51:00,NO,"We noticed that we sometimes have to input an ISIN including the last technical digit (0) to get search results, sometimes not => 

!image-2024-09-20-10-28-15-595.png! 




Could you please check and make sure that the input criteria are consistent, respectitely that only the ""real"" 12-digit-ISIN should be input to receive relevant search results? 

Thanks, Bert 

  

  

 ",noticed sometimes input isin including last technical digit get search result sometimes imagepng could please check make sure input criterion consistent respectitely real digitisin input receive relevant search result thanks bert,0.06582079268991947,0.0,242,0.017715180823798055,0.23354480182752013,5.****************,0.2,0.1,0.15,52.53%
Incident,OEKB-6334,Major,M12 PROD - CHAN0003206948 - reject conflicting update not possible,Ana Maria Galic,2024-09-20 10:20:00,2024-09-20 10:23:00,NO,"CHAN0003206948  

There is a conflicting update which I wanted to reject, but when pressing the ""compare draft"" button nothing happens. 

  

!image-2024-09-20-10-13-34-293.png! 

  

For some other events it worked perfectly today, for example CHAN0003206946 or MRGR0003206939. 

Could you please check? 
Thanks 
Regards, 
Ana ",chan conflicting update wanted reject pressing compare draft button nothing happens imagepng event worked perfectly today example chan mrgr could please check thanks regard ana,0.06630764901638031,0.0,242,0.017715180823798055,0.23342308774590492,1.****************,0.037820429797300145,0.13,0.15,32.69%
Incident,OEKB-6333,Critical,e-mail alert: Event Code : NOTIFICATION_IN_FAILURE,Wallmann GEROLD,2024-09-20 10:00:00,2024-09-20 10:06:00,NO,"Hello, 


We have received the following e-mail alert on 20.09.2024 at 09:22 

  

Event Code : NOTIFICATION_IN_FAILURE 
Description : Failure while treating an incoming message 
IJ031070: Transaction cannot proceed: STATUS_MARKED_ROLLBACK 

  

Can you please check",hello received following email alert event code notificationinfailure description failure treating incoming message ij transaction proceed statusmarkedrollback please check,-0.07614604383707047,0.0,242,0.017715180823798055,0.2690365109592676,1.****************,0.*****************,0.15,0.15,44.56%
Incident,OEKB-6332,Critical,M12 PROD - MT564 REPE failed in MegaBroker => No Alert?,Bertram Schon,2024-09-20 09:53:00,2024-09-20 09:59:00,NO,"I, coincidentelly, found out that a MT564 REPE received from ESES failed in MegaBroker => 

!image-2024-09-20-09-44-36-546.png! 

It looks like the configuration for ESES is still not ok, we had an issue yesterday when receiving MT566 from ESESBEBBXECN (see OEKB-6329), the a.m. failed REPE was received from ESESBEBBXXX. 

Moreover, we did not see this failed REPE in M12 - and also no alert was received!? 

How can we make sure that business user have all incoming messages available in M12 or, at least, receive an alert if a message stucks in MegaBroker? 

Please check! 
Thanks, Bert 




  

  

  

 ",coincidentelly found mt repe received es failed megabroker imagepng look like configuration es still ok issue yesterday receiving mt esesbebbxecn see oekb failed repe received esesbebbxxx moreover see failed repe also alert received make sure business user incoming message available least receive alert message stucks megabroker please check thanks bert,0.03372637555003166,0.0,242,0.017715180823798055,0.24156840611249208,5.****************,0.2,0.15,0.15,61.24%
Incident,OEKB-6331,Major,M12 PROD - CHAN0003206938 - Link in WEBB field incomplete / incorrect,Bertram Schon,2024-09-19 11:43:00,2024-09-19 12:07:00,YES,"CHAN0003206938  
We set up this CHAN / TERM event by providing a web link => 

!image-2024-09-19-11-41-24-084.png! 

The correct link includes an ""equal sign"" ( = ), but this sign was not mapped correctly in the corresponding WEBB field in the MT564 OUT - so, the link is not valid => 

!image-2024-09-19-11-42-56-736.png! 


Could you please check? 

Thanks, Bert 

  

 ",chan set chan term event providing web link imagepng correct link includes equal sign sign mapped correctly corresponding webb field mt link valid imagepng could please check thanks bert,0.028703860938549042,0.0,242,0.017715180823798055,0.24282403476536274,5.****************,0.2,0.13,0.15,58.42%
Incident,OEKB-6330,Medium,"M12 PROD - DRIP00014501 - MP CASH in status ""PartiallyReconciled"" for unknown reasons",Bertram Schon,2024-09-19 11:25:00,2024-09-19 11:34:00,NO,"DRIP00014501 

We received CASH Payment from ESES in the amount of EUR 120,65 (MT566 attached). 

Market Entitlements have been calculated as follows => 

!image-2024-09-19-11-24-28-213.png! 

However, after validating the CASH payment, status is ""ReconciledPartially"" => 

!image-2024-09-19-11-25-28-942.png! 

Could you please check? 

Thanks, Bert",drip received cash payment es amount eur mt attached market entitlement calculated follows imagepng however validating cash payment status reconciledpartially imagepng could please check thanks bert,0.032308103516697884,0.0,242,0.017715180823798055,0.24192297412082553,5.****************,0.2,0.1,0.15,53.79%
Incident,OEKB-6329,Major,"M12 PROD - DRIP00014501 - MT566 from ESES failed with error ""Unknown Sender""",Bertram Schon,2024-09-19 09:53:00,2024-09-19 10:18:00,NO,"DRIP00014501 
We received two MT566 messages from ESES, both failed with error ""Unknown Sender"" => 

!image-2024-09-19-09-50-53-039.png! 

When checking configuration, I found the following under ""Custodian"" => 

!image-2024-09-19-09-52-28-182.png! 

Could you please check? 

Thanks, 

Bert 

  

  

 ",drip received two mt message es failed error unknown sender imagepng checking configuration found following custodian imagepng could please check thanks bert,-0.025337468832731247,0.0,243,0.017422374639493515,0.2563343672081828,5.****************,0.2,0.13,0.15,60.45%
Information Request,OEKB-6328,Critical,M12 PROD - MEET0003206521/ MEET0003206522 - Saving of update failed,Ana Maria Galic,2024-09-19 09:47:00,2024-09-19 10:18:00,NO,"MEET0003206521 and MEET0003206522 
Updates are not possible again due to blocked event in Megabroker. Please check workaround (see ticket OEKB-6268). 

  

Thanks 

Regards,  
Ana 

  

  

  

 ",meet meet update possible due blocked event megabroker please check workaround see ticket oekb thanks regard ana,-0.052852433174848557,0.0,243,0.017422374639493515,0.26321310829371214,1.****************,0.037820429797300145,0.15,0.06,26.66%
Incident,OEKB-6327,Major,M12 PROD - Incorrect status of CA data feed after reject of update for non critical keys for elective events,Bertram Schon,2024-09-18 16:32:00,2024-09-18 17:00:00,NO,"After rejecting an update for non critical keys for elective event XMET00039209, we noticed that the CA data feed stuck with status ""Processed AndWaitingAck"" and the event is then blocked and each new update will stuck in megabroker with status wait and will not be visible in megacor as it is the case for the mentioned event. 

See also Oussema's comment in OEKB-6309. 

Please check! 

Thanks, Bert",rejecting update non critical key elective event xmet noticed ca data feed stuck status processed andwaitingack event blocked new update stuck megabroker status wait visible megacor case mentioned event see also oussemas comment oekb please check thanks bert,-0.11129389144480228,0.0,243,0.017422374639493515,0.27782347286120057,5.****************,0.2,0.13,0.15,63.67%
Incident,OEKB-6326,Medium,M12 PROD - List Received Meeting Notification should not include internal update,Bertram Schon,2024-09-18 16:16:00,2024-10-03 16:45:00,YES,"PROX0003206717 
was set up due to incoming seev.001 and updated/validated afterwards. 
When checking ""List Received Meeting Notification"", I noticed that the ""update feed"" is also listed which is quite confusing => 

!image-2024-09-18-16-07-27-052.png! 

Could you please check and skip showing those ""internal updates"" in this list where only the received seev.001 should be available? 

Thanks, Bert",prox set due incoming seev updatedvalidated afterwards checking list received meeting notification noticed update feed also listed quite confusing imagepng could please check skip showing internal update list received seev available thanks bert,-0.19107689149677753,15.0,243,0.017422374639493515,0.2977692228741944,5.****************,0.2,0.1,0.15,62.17%
Information Request,OEKB-6323,Medium,M12 PROD - MT564 REPL OUT - Please check notification configuration when UNBA = 0,Bertram Schon,2024-09-18 14:48:00,2024-09-18 14:50:00,NO,"I found out that in M10 (CORP event BIDS00009602), no REPL was sent to a client who has already instructed for the total ELIG position (= UNBA was 0). 

Could you please check if this configuration is the same in M12? 

If yes, could be please amend this, because REPL must always be sent to clients, regardless if positions are instructed or not. 

Thanks, Bert",found corp event bid repl sent client already instructed total elig position unba could please check configuration yes could please amend repl must always sent client regardless position instructed thanks bert,0.010962769389152527,0.0,243,0.017422374639493515,0.24725930765271187,5.****************,0.2,0.1,0.06,41.09%
Incident,OEKB-6321,Major,M12 PROD - DVCA00099473 - Client Payment - CashBookingRejected,Dalibor VIDIC,2024-09-17 17:24:00,2024-09-17 17:25:00,NO,"Hello,  

some client payments were rejected in the ISIN mentioned above.  

The attempt to cancel these client payments via the Menu => Payments => Client Payments => Reverse => Reverse Cash Payments was not successful. 

The manual workaround is already done by Oussema. 

BR, Dalibor 

 ",hello client payment rejected isin mentioned attempt cancel client payment via menu payment client payment reverse reverse cash payment successful manual workaround already done oussema br dalibor,0.31670211255550385,0.0,244,0.017134408115727567,0.17082447186112404,3.***************,0.*****************,0.13,0.15,33.39%
Incident,OEKB-6320,Major,M12 PROD - DVCA00099473 - Mismatch between Client Entitlement and Client Payment,Dalibor VIDIC,2024-09-17 17:15:00,2024-09-17 17:20:00,NO,"Hello,  

in the above DVCA Event the entitlements were calculated correctly but the ‘Client Payments’ were not executed correctly and some are also rejected. 

At first glance, it looks as if the fees have been calculated incorrectly. 

  

Please check! 

  

Thank you and BR, Dalibor 

 ",hello dvca event entitlement calculated correctly client payment executed correctly also rejected first glance look fee calculated incorrectly please check thank br dalibor,-0.12807231582701206,0.0,244,0.017134408115727567,0.282018078956753,3.***************,0.*****************,0.13,0.15,50.07%
Information Request,OEKB-6319,Medium,M12 PROD: To Do List - Failed Received MT564 - Ignore via right mouse click does not work,Bertram Schon,2024-09-17 16:40:00,2024-09-17 17:09:00,NO,"I found out that ignore functionality does not work, when trying to do so via right mouse click => 

!image-2024-09-17-16-37-51-403.png! 


After clicking ""Ignore"", this message is shown => 

!image-2024-09-17-16-38-33-641.png! 

However, the feed is still not ignored after that. 

Obviously, ignore only works via Edit => Ignore. 
If so, please delete ""Ignore"" from the pop up list when clicking the right mouse key. 

Thanks, Bert",found ignore functionality work trying via right mouse click imagepng clicking ignore message shown imagepng however feed still ignored obviously ignore work via edit ignore please delete ignore pop list clicking right mouse key thanks bert,-0.19117274321615696,0.0,244,0.017134408115727567,0.29779318580403924,5.****************,0.2,0.1,0.06,48.67%
Incident,OEKB-6318,Medium,M12 PROD - To Do List - Corp.Action Manipulation / Nil Payment Events should not include INTR events,Bertram Schon,2024-09-17 16:15:00,2024-09-17 16:18:00,YES,"When checking To Do List, we found INTR event INTR00156368 under Corp.Action Manipulation / Nil Payment Events => 
!image-2024-09-17-16-13-39-925.png! 

I'm not sure if those INTR events should be alerted somewhere else (maybe under Bond Event Manipulation), but it should not be shown under *Corporate Action* Manipulation. 

Thanks, Bert",checking list found intr event intr corpaction manipulation nil payment event imagepng im sure intr event alerted somewhere else maybe bond event manipulation shown corporate action manipulation thanks bert,0.027889899909496307,0.0,244,0.017134408115727567,0.24302752502262592,5.****************,0.2,0.1,0.15,53.95%
Incident,OEKB-6317,Medium,M12 PROD - CERT0003206384 - CA Status changed after Unfreeze,Ana Maria Galic,2024-09-17 15:11:00,2024-09-17 15:13:00,NO,"CERT0003206384  

Unfreeze for the positions was successful today, but then we noticed that the CA Status changed from ""Instructed"" to ""Receiving Instruction"". 

Could you please check? The CA Status should remain the same. 

Thanks 
Regards,  
Ana",cert unfreeze position successful today noticed ca status changed instructed receiving instruction could please check ca status remain thanks regard ana,0.02183845080435276,0.0,244,0.017134408115727567,0.2445403872989118,1.****************,0.037820429797300145,0.1,0.15,29.85%
Requirement,OEKB-6316,Major,SWIFT Release 2024 (Score/ECMS),Valdes ELIZABEHT,2024-09-17 12:06:00,2024-10-15 15:06:00,YES,"Dear Emna,  

please find below the needed scope to be implemented for the SR2024:  
||Change #||Impact on||O/M||Impacted messages||Costs|| 
|CR 002002|Issuer Platform|Mandatory|MT566|FREE| 
|CR 002015|Issuer Platform/CCSYS|Optional|MT564/MT566|6,5 MD| 
|CR 002057|Issuer Platform|Optional|seev.001.001.11, seev.007.001.10, seev.008.001.09|7,5 MD| 

  

  

Following tickets are also in scope but can be delivered also in a later stage:  
|CR 002038|no Impact|Optional|seev.042|9 MD 

| 
|CR 002026|no Impact| |MT567 
seev.034.001.14, seev.042.001.12|9 MD 
 | 

In QAS the future mode on SWIFT side will be implemented this weekend.  

KR,  

Eli",dear emna please find needed scope implemented sr change impact onomimpacted messagescosts cr issuer platformmandatorymtfree cr issuer platformccsysoptionalmtmt md cr issuer platformoptionalseev seev seev md following ticket also scope delivered also later stage cr impactoptionalseev md cr impact mt seev seev md qas future mode swift side implemented weekend kr eli,0.05717293545603752,28.0,244,0.017134408115727567,0.23570676613599062,0.*****************,0.010483112502500294,0.13,0.09,19.93%
Incident,OEKB-6315,Medium,M12 PROD - To Do List - Failed Received MT564 / Conflicting Updates - Behavior unclear/incorrect?!,Bertram Schon,2024-09-17 08:57:00,2024-09-17 09:17:00,YES,"We see a number of events which are both listed under ""Failed Received MT564"" as well as under ""Validate/Reject Conflicting Updates"" => 

!image-2024-09-17-08-55-26-803.png! 
!image-2024-09-17-08-55-55-815.png! 

Is this not a contradiction? 
Moreover, how should these feeds be processed? Ignored in ""List Failed Received"" or Rejected in ""Conflicting Updates"" or both? 

Please check and revert to us! 
Thanks, Bert",see number event listed failed received mt well validatereject conflicting update imagepng imagepng contradiction moreover feed processed ignored list failed received rejected conflicting update please check revert u thanks bert,-0.****************,0.0,245,0.*****************,0.*****************,5.****************,0.2,0.1,0.15,71.84%
Incident,OEKB-6314,Critical,M12 PROD - LIQU0003206589 - No MT564 sent to 3i (DEF005),Bertram Schon,2024-09-17 08:44:00,2024-09-17 09:20:00,NO,"LIQU0003206589 
was set up yesterday automatically due to incoming MT564 (IFAS-File). 
Pay Date is today. 

However, we noticed that no MT564 were sent to DEF accounts, including DEF005 which should set up the event also in 3i in order to process the payment. 

Could you please urgently check, MT564 must be sent immediately to 3i (and as well to DEF001, DEF003). 

Thanks, Bert",liqu set yesterday automatically due incoming mt ifasfile pay date today however noticed mt sent def account including def set event also order process payment could please urgently check mt must sent immediately well def def thanks bert,-0.*****************,0.0,245,0.*****************,0.****************,5.****************,0.2,0.15,0.15,65.8%
Incident,OEKB-6313,Major,"M12 PROD - ""Repair Received Feed"" - Search failed",Bertram Schon,2024-09-17 08:18:00,2024-09-17 08:29:00,NO,"When trying to search for ""Repair Received Feed""-results, the following error popped up => 



!image-2024-09-17-08-18-11-002.png! 

  

Could you please check? 
Thanks, Bert 

  

  

 ",trying search repair received feedresults following error popped imagepng could please check thanks bert,-0.054740678519010544,0.0,245,0.*****************,0.*****************,5.****************,0.2,0.13,0.15,61.55%
Information Request,OEKB-6312,Medium,M12 PROD - New freeze Configuration for CERT event,Bertram Schon,2024-09-16 17:42:00,2024-09-16 20:45:00,NO,"As discussed with Oussema, please amend the freeze configuration for CERT events. 

There must be NO FREEZE for CERT events if there is no SECU OUT movement and no END OF SECURITIES BLOCKING DATE available, even if an instruction is received before RDTE. 

Thanks, 
Bert",discussed oussema please amend freeze configuration cert event must freeze cert event secu movement end security blocking date available even instruction received rdte thanks bert,-0.09489854238927364,0.0,245,0.*****************,0.2737246355973184,5.****************,0.2,0.1,0.06,45.06%
Incident,OEKB-6311,Major,M12 PROD: CERT0003206384 - Unfreeze failed,Bertram Schon,2024-09-16 17:31:00,2024-09-16 17:37:00,NO,"CERT0003206384  
When trying to unfreeze positions via  

!image-2024-09-16-17-29-10-870.png! 


!image-2024-09-16-17-30-38-836.png! 


I noticed that no unfreeze was processed. 

Could you please check? 
Thanks, Bert",cert trying unfreeze position via imagepng imagepng noticed unfreeze processed could please check thanks bert,0.02137310616672039,0.0,245,0.*****************,0.2446567234583199,5.****************,0.2,0.13,0.15,58.7%
Incident,OEKB-6310,Major,M12 PROD: CERT0003206384 - Freeze of Instructed position incorrect,Bertram Schon,2024-09-16 16:40:00,2024-09-16 16:52:00,NO,"CERT0003206384 
was set up without a SEC movement and without filling the field ""End Of Securities Blocking Period Date"". 

Hence, no freeze should be processed for the instructed positions. 

However, the system was sending semt.013 to T2S, freeze was executed => 

!image-2024-09-16-16-40-09-962.png! 

Could you please check? 
Thanks, Bert 

  

  

 ",cert set without sec movement without filling field end security blocking period date hence freeze processed instructed position however system sending semt t freeze executed imagepng could please check thanks bert,-0.10729876905679703,0.0,245,0.*****************,0.27682469226419926,5.****************,0.2,0.13,0.15,63.52%
Incident,OEKB-6309,Medium,M12 PROD - XMET00039209 - Status incorrect/unclear after Reject of Draft,Bertram Schon,2024-09-16 15:50:00,2024-09-16 15:59:00,NO,"XMET00039209 
Conflicting update was rejected. 
However, if checking data feeds, we notice that the feed is still in status ""ProcessedAndWaitingAck"", decision ""Ignored"" => 

!image-2024-09-16-15-50-34-410.png! 

In my opinion, it should be NACKed (as it was in M10)? 

Please check! 
Thanks, Bert",xmet conflicting update rejected however checking data feed notice feed still status processedandwaitingack decision ignored imagepng opinion nacked please check thanks bert,-0.03084677830338478,0.0,245,0.*****************,0.2577116945758462,5.****************,0.2,0.1,0.15,56.16%
Incident,OEKB-6308,Major,Dashboard alert Failed-Jobs: Financial_Instruments_Automatic_Deactivation,Wallmann GEROLD,2024-09-16 10:53:00,2024-09-16 12:31:00,YES,"There is a dash-board alert - Financial_Instruments_Automatic_Deactivation under ""Failed Jobs"". 

  

Please can you check? 

  

Thank you. 

  

Gerold 

 ",dashboard alert financialinstrumentsautomaticdeactivation failed job please check thank gerold,-0.5213411599397659,0.0,245,0.*****************,0.3803352899849415,1.****************,0.*****************,0.13,0.15,58.25%
Incident,OEKB-6307,Medium,"M12 PROD - MEET0003206521/ MEET0003206522 - ""For Information Only"" mapped wrong",Ana Maria Galic,2024-09-16 10:47:00,2024-09-16 10:51:00,NO,"Today we received two new MEET events, MEET0003206521 and MEET0003206522. 
When updating the events, we noticed that the field ""For Information Only"" under General Information is mapped with ""Yes"", which is not correct. 

Can you please check and fix this problem. 

Thanks 
Regards, 
Ana 

  

!image-2024-09-16-10-43-50-208.png!",today received two new meet event meet meet updating event noticed field information general information mapped yes correct please check fix problem thanks regard ana imagepng,0.06015080213546753,0.0,245,0.*****************,0.23496229946613312,1.****************,0.037820429797300145,0.1,0.15,28.42%
Information Request,OEKB-6306,Medium,"M12 PROD - ""Blocked for non-critical modification"" unclear",Bertram Schon,2024-09-16 10:39:00,,NO,"PROX00063401 
We received +*seev.001*+ (could you please also amend the Search Result name accordingly!) RMDR, for 2 messages, error description is ""GR is blocked for non critical modification under MegaBroker"", for the 2 other two, there is no such error description. 
Can you please explain the difference? 

!image-2024-09-16-10-33-01-508.png! 

Moreover, after rejecting the update, the error description is still available, shouldn't it be removed as the error does not longer occur? => 

!image-2024-09-16-10-39-11-148.png! 


Could you please check? 
Thanks, Bert",prox received seev could please also amend search result name accordingly rmdr message error description gr blocked non critical modification megabroker two error description please explain difference imagepng moreover rejecting update error description still available shouldnt removed error longer occur imagepng could please check thanks bert,-0.07783396355807781,0.0,245,0.*****************,0.26945849088951945,5.****************,0.2,0.1,0.06,44.42%
Incident,OEKB-6305,Major,M12 PROD - DVOP00099447 - Reject Conflicting Update failed,Bertram Schon,2024-09-16 09:55:00,2024-09-16 10:10:00,NO,"DVOP00099447 
When trying to reject the conflicting update, the following error popped up after clicking ""Compare Draft"" => 

!image-2024-09-16-09-53-44-462.png! 

Could you please check? 
Thanks, Bert",dvop trying reject conflicting update following error popped clicking compare draft imagepng could please check thanks bert,-0.5306376777589321,0.0,246,0.016572675401761255,0.38265941943973303,5.****************,0.2,0.13,0.15,79.4%
Incident,OEKB-6304,Medium,"M12 PROD - SPLF00007001 - MP still in status ""BlockedForCutOffTime"" / Job failed?!",Bertram Schon,2024-09-16 09:32:00,2024-09-16 09:37:00,NO,"SPLF00007001 
MT566 was received on Friday around 19.00 - MP moved to status ""BlockedForCutOffTime"" which is correct. 

However, today at 8.30, the status was still the same => 

!image-2024-09-16-09-31-38-914.png! 

I guess, the job which should force the payment failed. Could you please check? 

As a workaround, we forced the MP manually. 

BR Bert ",splf mt received friday around mp moved status blockedforcutofftime correct however today status still imagepng guess job force payment failed could please check workaround forced mp manually br bert,-0.13449584692716599,0.0,246,0.016572675401761255,0.2836239617317915,5.****************,0.2,0.1,0.15,60.04%
Incident,OEKB-6303,Major,For the Dashboard alert Client Subject to QI Reporting the Ignore Function is missing,Wallmann GEROLD,2024-09-13 15:42:00,2024-09-13 15:45:00,YES,"For the Dashboard alert ""Client Subject to QI Reporting"" the Ignore button is missing. 

Please add the ignore Button. 

Please the Delete Button, as the Delete Button cancel the client entitlement and does not delete the Dashboard alert.  

Please add the ""Ignore"" for Dashbaord alert ""Possible MCAL Event"" under ""Static Data"" 

Please add the Ignore Button also for the Dashboard Alert "" => Client Instruction => Consolidate",dashboard alert client subject qi reporting ignore button missing please add ignore button please delete button delete button cancel client entitlement delete dashboard alert please add ignore dashbaord alert possible mcal event static data please add ignore button also dashboard alert client instruction consolidate,-0.15908178500831127,0.0,248,0.016029358476645567,0.2897704462520778,1.****************,0.*****************,0.13,0.15,44.67%
Incident,OEKB-6302,Medium,"M12 PROD - PROX0003206511 - field ""Issuer Deadline For Voting"" not mapped",Ana Maria Galic,2024-09-13 15:00:00,2024-09-13 16:09:00,NO,"Today we received a new PROX event (PROX0003206511) via seev.001. When comparing the fields in M12 with the seev.001 I noticed that in the Participation Method the ""Issuer Deadline For Voting"" was not filled, although there was a date in the seev.001 message (message attached and date highlighted). 

I filled in the field manually but please check why it was not mapped automatically. 

Thanks and regards, 

Ana  

  

!image-2024-09-13-14-56-30-532.png!",today received new prox event prox via seev comparing field seev noticed participation method issuer deadline voting filled although date seev message message attached date highlighted filled field manually please check mapped automatically thanks regard ana imagepng,0.047012776136398315,0.0,248,0.016029358476645567,0.23824680596590042,1.****************,0.037820429797300145,0.1,0.15,28.91%
Incident,OEKB-6301,Medium,M12 PROD - List Market Entitlement - Menu modifications,Dalibor VIDIC,2024-09-13 15:00:00,2024-09-13 16:15:00,NO,"Hello, 

Please adapt the Menu Entitlement => Market Entitlements => List Market Entitlement => Entitled ISIN / ID to Entitled ISIN.  

  

Thank you and BR, Dalibor",hello please adapt menu entitlement market entitlement list market entitlement entitled isin id entitled isin thank br dalibor,-0.005631934851408005,0.0,248,0.016029358476645567,0.251407983712852,3.***************,0.*****************,0.1,0.15,40.98%
Information Request,OEKB-6300,Critical,M12 PROD - BIDS00009302 - Saving of update failed,Ana Maria Galic,2024-09-13 13:34:00,2024-09-13 14:13:00,NO,"BIDS00009302 
Update is not possible due to blocked event in Megabroker. Please check workaround (see ticket OEKB-6268). 

  

Thanks 

Regards,  
Ana 

  

  

  

 ",bid update possible due blocked event megabroker please check workaround see ticket oekb thanks regard ana,-0.3311636671423912,0.0,248,0.016029358476645567,0.3327909167855978,1.****************,0.037820429797300145,0.15,0.06,37.09%
Incident,OEKB-6299,Major,M12 PROD - MT566 sent twice to clients,Dalibor VIDIC,2024-09-13 13:34:00,2024-09-13 13:38:00,NO,"Hi. 

It looks like the MT566 messages have been sent to clients twice again. 

Several clients have already contacted us about this. 

Please check! 

BR, Dalibor",hi look like mt message sent client twice several client already contacted u please check br dalibor,0.007895786315202713,0.0,248,0.016029358476645567,0.*****************,3.***************,0.*****************,0.13,0.15,44.97%
Incident,OEKB-6298,Major,M12 PROD - SPLR0003206431 - SECU IN payment booked with incorrect NostroSecAccount,Bertram Schon,2024-09-13 11:48:00,2024-09-13 12:08:00,NO,"SPLR0003206431  
Nostro Sec Account Entitled ISIN = CBF 9250200 
Nostro Sec Account SECU IN ISIN = CBL 67242 

Hence, we edited the field accordingly => 
!image-2024-09-13-11-45-13-806.png! 


MP looks ok => 

!image-2024-09-13-11-45-53-840.png! 
However, when checking CCSYS (and sese.023 - attached), we found out that the Nostro Sec Account was mapped incorrectly => 

!image-2024-09-13-11-46-44-747.png! 


Please urgently check! 

We reversed the SECU IN MP and wait until it can be reprocessed accordingly. 

Thanks, Bert",splr nostro sec account entitled isin cbf nostro sec account secu isin cbl hence edited field accordingly imagepng mp look ok imagepng however checking ccsys sese attached found nostro sec account mapped incorrectly imagepng please urgently check reversed secu mp wait reprocessed accordingly thanks bert,0.046307072043418884,0.0,248,0.016029358476645567,0.*****************,5.****************,0.2,0.13,0.15,57.76%
Incident,OEKB-6297,Critical,M12 PROD - SPLR0003206431/ SPLF00007001 - no REPE sent,Ana Maria Galic,2024-09-13 10:57:00,2024-09-13 11:08:00,NO,"Please urgently check why no REPE messages have been sent for these event and make sure that these messages are sent immediately. (entitlements have been calculated accordingly for both events) 

Thanks 
Regards,  
Ana ",please urgently check repe message sent event make sure message sent immediately entitlement calculated accordingly event thanks regard ana,0.012719392776489258,0.0,248,0.016029358476645567,0.*****************,1.****************,0.037820429797300145,0.15,0.15,37.7%
Incident,OEKB-6296,Medium,M12 PROD - Menu modifications,Dalibor VIDIC,2024-09-12 15:23:00,2024-09-12 15:26:00,NO,"Hello,  

Please adapt the two dashboard alerts 'Bond Event Manipulation => Cross Check CA' and 'Dividend Event Manipulation => Cross Check CA"" as in M10.  

Thank you and BR, Dalibor",hello please adapt two dashboard alert bond event manipulation cross check ca dividend event manipulation cross check ca thank br dalibor,0.0445810966193676,0.0,249,0.015764416484854486,0.2388547258451581,3.***************,0.*****************,0.1,0.15,39.09%
Incident,OEKB-6295,Major,JOB - Client Notification JOB stucked in Executed Status,Valdes ELIZABEHT,2024-09-12 14:39:00,2024-09-12 14:48:00,NO,"*Update: Issue is on the Notification JOB as this is blocking further to jobs to be executed.*  

See Job execution of this Job below! 

!image-2024-09-16-09-15-39-878.png! 

Today the Client Confirmation Job at 09:XX was still executing at 14:00.  

We stopped the Job and trigered the Job manually.  

=> Additionaly a restart was done to unblock MC.  

KR,  

Eli",update issue notification job blocking job executed see job execution job imagepng today client confirmation job xx still executing stopped job trigered job manually additionaly restart done unblock mc kr eli,-0.9094187775626779,0.0,249,0.015764416484854486,0.47735469439066947,0.*****************,0.010483112502500294,0.13,0.15,65.18%
Incident,OEKB-6294,Critical,M12 PROD - Customer References must be unique,Bertram Schon,2024-09-12 12:47:00,2024-09-12 12:52:00,NO,"We noticed that freeze instructions have been rejected by T2S due to the fact that the Customer Reference was already assigned to a settlement instruction => 

!image-2024-09-12-12-41-50-382.png! 

MVDC017 - A Settlement Instruction that has been settled within a predefined number of days in the past already exists in T2S for the same T2S Party and the same instruction reference. 


!image-2024-09-12-12-43-07-196.png! 


Hence, could you please make sure that M12 is creating unique Customer References, independently if it is a freeze attempt or settlement instruction. 



We tried to process a workaround (by submitting the freeze instruction again), but one freeze could not be processed until now, as M12 is creating references for which settlement instructions already exists. I will further discuss a possible workaround (manually edit of the Customer Reference!?) with Oussema. 

BR Bert ",noticed freeze instruction rejected t due fact customer reference already assigned settlement instruction imagepng mvdc settlement instruction settled within predefined number day past already exists t t party instruction reference imagepng hence could please make sure creating unique customer reference independently freeze attempt settlement instruction tried process workaround submitting freeze instruction one freeze could processed creating reference settlement instruction already exists discus possible workaround manually edit customer reference oussema br bert,-0.0035414323210716248,0.0,249,0.015764416484854486,0.2508853580802679,5.****************,0.2,0.15,0.15,62.63%
Incident,OEKB-6293,Medium,Currency HKD can not be input,Wallmann GEROLD,2024-09-12 12:21:00,2024-09-12 12:31:00,NO,"For ISIN CNE1000003X6 we input the currency HKD. 

But as in financial instruments the currency is CNY, we cannot enter HKD - there is the following alert: 

Cannot invoke ""com.palmyra.arch.presentation.viewformat.iviewformat.IFieldViewFormat.isReadOnly()"" because ""simpleVF"" is null 

  

A word around was done by Oussema. 

  

Can you please check. 

  

Thank you. 

  

Regards, 

  

GEROLD",isin cnex input currency hkd financial instrument currency cny enter hkd following alert invoke compalmyraarchpresentationviewformativiewformatifieldviewformatisreadonly simplevf null word around done oussema please check thank regard gerold,0.001170385628938675,0.0,249,0.015764416484854486,0.24970740359276533,1.****************,0.*****************,0.1,0.15,34.16%
Incident,OEKB-6292,Medium,"M12 PROD - To Do List - Corp.Act. Manipulation - ""Events with a Client Deadline Reached"" - Events still listed although ignored",Bertram Schon,2024-09-12 12:13:00,2024-09-12 12:50:00,NO,"We ignored some events found in the ""Events with a Client Deadline Reached"", however, after doing so, these events are still available in this list (even after refreshing) => 

!image-2024-09-12-12-13-12-237.png! 

Could you please check? 
Thanks, Bert",ignored event found event client deadline reached however event still available list even refreshing imagepng could please check thanks bert,-0.03207777068018913,0.0,249,0.015764416484854486,0.2580194426700473,5.****************,0.2,0.1,0.15,56.2%
Information Request,OEKB-6291,Critical,M12 PROD - XMET00039212 - Saving of update failed,Ana Maria Galic,2024-09-12 11:53:00,2024-09-12 11:58:00,NO,"XMET00039212 
Update is not possible due to blocked event in Megabroker. Please check workaround (see ticket OEKB-6268). 

  

Thanks 

Regards,  
Ana 

  

  

  

 ",xmet update possible due blocked event megabroker please check workaround see ticket oekb thanks regard ana,-0.24315385147929192,0.0,249,0.015764416484854486,0.310788462869823,1.****************,0.037820429797300145,0.15,0.06,33.79%
Information Request,OEKB-6290,Medium,M12 PROD - To Do List - Meeting/CA Manipulations - Cross Check CA - Result List to be renamed,Bertram Schon,2024-09-12 11:46:00,2024-09-16 12:29:00,NO,"Please amend the name of the search result lists from 

!image-2024-09-12-11-44-28-335.png! 

  

!image-2024-09-12-12-01-09-622.png! 

  

to ""Events"" (like for other Search Result lists). 

Thanks, Bert",please amend name search result list imagepng imagepng event like search result list thanks bert,0.0030660927295684814,4.0,249,0.015764416484854486,0.24923347681760788,5.****************,0.2,0.1,0.06,41.39%
Incident,OEKB-6289,Medium,Dashboard alert: necessary adaptions,Wallmann GEROLD,2024-09-12 11:15:00,2024-09-12 12:02:00,NO,"For the following Dashboard alert we need an adaption: 

1) Market Payment Report failed to tax data Pool 

--> the market payments shown in this alert are already in the US-TAX data pool - so please adapt the setting 

2) Failed Received sese 024 

There are more then 3.000 alerts - please also correct 

  

Thank you. 

  

Gerold 

  

 ",following dashboard alert need adaption market payment report failed tax data pool market payment shown alert already ustax data pool please adapt setting failed received sese alert please also correct thank gerold,-0.27952205017209053,0.0,249,0.015764416484854486,0.31988051254302263,1.****************,0.*****************,0.1,0.15,44.68%
Incident,OEKB-6288,Medium,MB12 Flow: Status: Flow Rejected - ISO20022 ACK,Valdes ELIZABEHT,2024-09-12 10:34:00,2024-09-12 10:39:00,YES,"Hi All,  

today we have again a rejected flow: Message ID: ********* 

  

*****FlowIn Identification: 
1) Cannot find any SenderReceiver related to the device (Biz.mx.finplus.to.mb.ack ) in the database. 
2) Cannot find any FlowInConfiguration related to the device ( {color:#de350b}*Biz.mx.finplus.to.mb.ack*{color} ). 
{color:#de350b}*3) No Domain Configuration found related to the flowIn (*********)*{color} 

  

{color:#de350b}*See flow attached - it's an ACK of SWIFT to CS00000000000039.*{color} 

  

Can you please check and advise.  

Thanks & KR,  

Eli",hi today rejected flow message id flowin identification find senderreceiver related device bizmxfinplustomback database find flowinconfiguration related device colordebbizmxfinplustombackcolor colordeb domain configuration found related flowin color colordebsee flow attached ack swift cscolor please check advise thanks kr eli,0.01540357805788517,0.0,250,0.015503853599009314,0.2461491054855287,0.*****************,0.010483112502500294,0.1,0.15,25.99%
Information Request,OEKB-6287,Major,"M12 PROD - PROX Instruction Management - No Auto-Reject if instructions are received on or before ""Entitlement Fixing Date""",Bertram Schon,2024-09-12 10:08:00,2025-01-15 10:24:00,NO,"Currently, in M12, Client Instructions for PROX events are auto-rejected if an instruction is received before or on the ""Entitlement Fixing Date"". 

Please amend this behavior as there could be meetings with Deadline Date <= Entitlement Fixing Date. 

In case such instructions are received, these should move to status INVALID DATA and the user should decide if the instruction should be accepted or rejected. 

Please check feasibility! 
Thanks, Bert",currently client instruction prox event autorejected instruction received entitlement fixing date please amend behavior could meeting deadline date entitlement fixing date case instruction received move status invalid data user decide instruction accepted rejected please check feasibility thanks bert,-0.07041027583181858,125.0,250,0.015503853599009314,0.26760256895795465,5.****************,0.2,0.13,0.06,48.64%
Incident,OEKB-6286,Major,MB Flow: Record Identification Failed,Valdes ELIZABEHT,2024-09-12 09:59:00,2024-09-12 10:21:00,NO,"Dear All,  

when chekcing MB we found following {color:#ff0000}*Status: Record Identification Failed*{color} 

{color:#172b4d}with following comment: {color}{color:#172b4d}*****FlowIn Identification:{color} 

{color:#172b4d} 
1) SenderReceiver with identifier (SWIFT ) found related to the device ( BIZ.FIN.TO.CAS ) 
2) 9 instances of FlowInConfiguration are found for the sender ( SWIFT ). 
3) Cannot find any FlowInConfiguration related to the device ( BIZ.FIN.TO.CAS ). 
4) FlowIn Configuration with identifier ( MT564_SWIFT_DC_FLOW_IN ) found for the Sender with identifier ( SWIFT ) and the InputDevice with identifier ( BIZ.FIN.TO.CAS ) using the preparsing grammar with identifier ( Swift_Header ). 
5) No Domain Configuration found related to the flowIn (120022896) 
*****Single Flow Record Type Config Identification: 
1) *There is no matching element of type RecordTypeConfiguration with the pre-parsing result \{block4.SeqA.F23G.Function=ADDB}*{color} 

  

{color:#172b4d}*Sender BIC:* {color} 

CEDELULLDXXX 

  

{color:#172b4d}*Message ID:* {color} 

{color:#172b4d}*120022896*{color} 

{color:#172b4d}*120022894*  see flows attached{color} 

  

Can you please check and advise.  

Thanks & KR,  

Eli",dear chekcing mb found following colorffstatus record identification failedcolor colorbdwith following comment colorcolorbdflowin identificationcolor colorbd senderreceiver identifier swift found related device bizfintocas instance flowinconfiguration found sender swift find flowinconfiguration related device bizfintocas flowin configuration identifier mtswiftdcflowin found sender identifier swift inputdevice identifier bizfintocas using preparsing grammar identifier swiftheader domain configuration found related flowin single flow record type config identification matching element type recordtypeconfiguration preparsing result blockseqafgfunctionaddbcolor colorbdsender bic color cedelulldxxx colorbdmessage id color colorbdcolor colorbd see flow attachedcolor please check advise thanks kr eli,0.0036785509437322617,0.0,250,0.015503853599009314,0.24908036226406693,0.*****************,0.010483112502500294,0.13,0.15,30.93%
Incident,OEKB-6285,Medium,"M12 PROD - List Received MT564 should not include seev.045 (046, 001, 002) messages",Bertram Schon,2024-09-11 17:39:00,2024-09-11 17:52:00,NO,"I noticed that SHDS (seev.045) messages are listed in the ""List Received MT564/Seev.031/Seev0.35/Seev.039"" search results => 

!image-2024-09-11-17-39-12-892.png! 

Could you please remove both SHDS and PROX from this list as those messages should be separated and found under the relevant SHDS and PROX search results. 

Thanks, 
Bert",noticed shds seev message listed list received mtseevseevseev search result imagepng could please remove shds prox list message separated found relevant shds prox search result thanks bert,-0.038204342126846313,0.0,250,0.015503853599009314,0.2595510855317116,5.****************,0.2,0.1,0.15,56.43%
Incident,OEKB-6284,Major,M12 PROD - List Financial Instruments - Error in the Generated Schedule,Dalibor VIDIC,2024-09-11 17:16:00,2024-09-11 17:53:00,YES,"Hello,  

When checking the future interest events, we noticed that there are always cases in which interest run is not correct.  
The problem is that the calculated 'Security Loan Schedule' contains the wrong last coupon date (before migration) and therefore the interest run is not correct.  

We have manually updated the INTR events, but are afraid that we will miss something in the future and then calculate an incorrect interest rate. 

  

Please check! 

  

Thank you and BR, Dalibor",hello checking future interest event noticed always case interest run correct problem calculated security loan schedule contains wrong last coupon date migration therefore interest run correct manually updated intr event afraid miss something future calculate incorrect interest rate please check thank br dalibor,-0.7562118899077177,0.0,250,0.015503853599009314,0.4390529724769294,3.***************,0.*****************,0.13,0.15,73.62%
Incident,OEKB-6283,Medium,"M12 PROD - ""Remove Invalid Disclosure Response"" - Search did not work",Bertram Schon,2024-09-11 16:52:00,2024-09-11 17:00:00,NO,"When trying to find and remove the invalid Disclosure Response for event SHDS00063747, 
the search failed => 

!image-2024-09-11-16-52-13-083.png! 

Could you please check? 
Thanks, Bert",trying find remove invalid disclosure response event shds search failed imagepng could please check thanks bert,-0.1430589184165001,0.0,250,0.015503853599009314,0.285764729604125,5.****************,0.2,0.1,0.15,60.36%
Incident,OEKB-6282,Medium,"M12 PROD - DRIP00014303 - SECU Payments in status ""WaitingValueDate""",Bertram Schon,2024-09-11 15:08:00,2024-09-11 15:39:00,NO,"DRIP00014303  
As discussed with Oussema: 
MT566 were received for SECU OUT and SECU IN yesterday (10/09) with Pay Date 11/09.  
Payments moved to status ""WaitingValueDate"" which is correct. 
However, the status did not change today, so, we had to force the payments manually. 

Please check if there is an issue with the relevant job which should force the payments from ""WaitingValueDate"" to ""WaitingBookingConfirmation"". 

Thanks, Bert",drip discussed oussema mt received secu secu yesterday pay date payment moved status waitingvaluedate correct however status change today force payment manually please check issue relevant job force payment waitingvaluedate waitingbookingconfirmation thanks bert,0.04320850409567356,0.0,250,0.015503853599009314,0.2391978739760816,5.****************,0.2,0.1,0.15,53.38%
Incident,OEKB-6281,Major,M12 PROD - E-Mail Corporate Action Confirmation File Format,Dalibor VIDIC,2024-09-11 13:03:00,2024-09-11 13:11:00,NO,"Hello,  

clients who receive a Corporate Action Confirmation from us by E-Mail complain that the file format is no longer common PDF File and cannot be opened immediately. 

I have attached the E-Mail confirmation from the past (M10) and now (M12). 

  

Please check! 

  

Thank you and BR, Dalibor",hello client receive corporate action confirmation u email complain file format longer common pdf file opened immediately attached email confirmation past please check thank br dalibor,0.02108977735042572,0.0,250,0.015503853599009314,0.24472755566239357,3.***************,0.*****************,0.13,0.15,44.47%
Incident,OEKB-6280,Medium,"Error alert: PALM-68092: A problem occurs, please contact the support team.",Wallmann GEROLD,2024-09-11 12:11:00,2024-09-11 12:16:00,NO,"I have deleted Dash board alerts. 

Sometimes there is the following alert:  

PALM-68092: A problem occurs, please contact the support team. 

  

The only way to continue is to logg out and log in in the system again. 

  

Please could you check and repair. 

  

Thank you. 

  

Gerold 

 ",deleted dash board alert sometimes following alert palm problem occurs please contact support team way continue logg log system please could check repair thank gerold,-0.16389980539679527,0.0,250,0.015503853599009314,0.2909749513491988,1.****************,0.*****************,0.1,0.15,40.35%
Incident,OEKB-6279,Major,Ignore function does not work in Dash board alerts,Wallmann GEROLD,2024-09-11 11:46:00,2024-09-11 11:53:00,YES,"For the Dashboard alert under ""Client Entitlement"":  

Entitlements Calculated without Tax Due to Missing SLAs   

The Ignore Button does not work. 

  

Please also check for the other Dashboard alerts if there is the same issue. 

 ",dashboard alert client entitlement entitlement calculated without tax due missing slas ignore button work please also check dashboard alert issue,-0.06614813394844532,0.0,250,0.015503853599009314,0.26653703348711133,1.****************,0.*****************,0.13,0.15,41.18%
Incident,OEKB-6278,Major,For NL0009690239 the market payment is not reconciled,Wallmann GEROLD,2024-09-11 10:50:00,2024-09-11 12:24:00,NO,"For ISIN NL0009690239 / DVCA00099981 the market payment is created, but it is not reconciled. 

Can you please check.",isin nl dvca market payment created reconciled please check,0.005384894087910652,0.0,250,0.015503853599009314,0.*****************,1.****************,0.*****************,0.13,0.15,38.5%
Incident,OEKB-6276,Major,Update Routing for seev.047/048/004/005 to FINPLUS Queue,Valdes ELIZABEHT,2024-09-10 18:21:00,2024-09-10 18:28:00,NO,"Dear All,  

we have noticed that the actual configuration was pointing to a wrong queue - ""CUG Queue"" - please amend this to FINPLUS Queue: *Mb.mx.finplus.to.biz.*  

*In PROD it was already changed remotely!*  

KR,  

El{*}i{*}",dear noticed actual configuration pointing wrong queue cug queue please amend finplus queue mbmxfinplustobiz prod already changed remotely kr eli,-0.*****************,0.0,251,0.015247597438855207,0.****************,0.*****************,0.010483112502500294,0.13,0.15,46.09%
Incident,OEKB-6274,Major,M12 PROD - SHDS0003206313 - seev.047 sent to Captrace NACKed in Alliance,Bertram Schon,2024-09-10 17:08:00,2024-09-10 17:42:00,NO,"SHDS0003206313 
was set up manually due to the issue with incoming seev.045. 

When checking outgoing seev.047 (attached) in SWIFT Alliance, we noticed that the message was NACKed => 

!image-2024-09-10-17-07-09-624.png! 

Could you please check? 
Thanks, Bert",shds set manually due issue incoming seev checking outgoing seev attached swift alliance noticed message nacked imagepng could please check thanks bert,-0.10820422880351543,0.0,251,0.015247597438855207,0.27705105720087886,5.****************,0.2,0.13,0.15,63.56%
Incident,OEKB-6273,Critical,M12 PROD - MT566 to 3i with wrong Quantity Type Code,Dalibor VIDIC,2024-09-10 16:26:00,2024-09-10 16:39:00,YES,"Hello,  

The MT566 message that has been sent to 3i has in the Line :36B::PSTA//{color:#de350b}*UNIT*{color}/6141000, 

although the security is quoted in nominal value. 

:35B:ISIN AT0000A296Q2 
:93B::CONB//{*}{color:#de350b}FAMT{color}{*}/6141000, 

The messages sent to clients are correct. 

  

Please check. 

  

Thank you and BR, Dalibor 

  

This problem exists for: 

REDM Events 

PCAL Events 

MCAL Events 

  

 ",hello mt message sent line bpstacolordebunitcolor although security quoted nominal value bisin ataq bconbcolordebfamtcolor message sent client correct please check thank br dalibor problem exists redm event pcal event mcal event,-0.07400186732411385,0.0,251,0.015247597438855207,0.26850046683102846,3.***************,0.*****************,0.15,0.15,51.04%
Incident,OEKB-6272,Major,M12 PROD - AT0000A00X79 - Error in the Generated Schedule,Dalibor VIDIC,2024-09-10 15:00:00,2024-09-10 15:33:00,NO,"Hello,  

The generated schedule created for AT0000A00X79 is not correct and therefore the wrong INTR event is created. 

Coupon Frequency are ThreeMonths and that is correct but the ‘Loan Schedule’ was created incorrectly. 

The calculated interval here is two months and then one month and so on instead of three months. 

  

!image-2024-09-10-14-51-43-117.png! 

Please check! 

  

Thank you and BR, Dalibor 

  

  

 ",hello generated schedule created atax correct therefore wrong intr event created coupon frequency threemonths correct loan schedule created incorrectly calculated interval two month one month instead three month imagepng please check thank br dalibor,-0.47515041939914227,0.0,251,0.015247597438855207,0.36878760484978557,3.***************,0.*****************,0.13,0.15,63.08%
Incident,OEKB-6271,Medium,MCAL events must be correctec,Wallmann GEROLD,2024-09-10 12:05:00,2024-09-10 12:33:00,NO,"For all MCAL there is an error regarding the tax. 

For MCAL payment date <= 10.09.2024 there was done a manual work around with Oussema. 

For MCAL events payment date > 10.09.2024  we need a script to repair it.  

  

For MCAL payment date <= 10.09.2024 there was done a manual with Oussema. 

  

 ",mcal error regarding tax mcal payment date done manual work around oussema mcal event payment date need script repair mcal payment date done manual oussema,-0.6603358164429665,0.0,251,0.015247597438855207,0.4150839541107416,1.****************,0.*****************,0.1,0.15,58.96%
Incident,OEKB-6270,Medium,"M12 PROD - To-Do List ""Events with a Client Deadline Reached"" - ""ignore"" button missing",Ana Maria Galic,2024-09-10 10:19:00,2024-09-10 10:28:00,NO,"Hi,  

when checking the To-Do List ""Events with a Client Deadline Reached"", we noticed that no ""ignore"" functionality is available. Please add the ""ignore"" button. 

Thanks,  
Regards 
Ana ",hi checking todo list event client deadline reached noticed ignore functionality available please add ignore button thanks regard ana,-0.07408247701823711,0.0,252,0.014995************,0.2685206192545593,1.****************,0.037820429797300145,0.1,0.15,33.45%
Incident,OEKB-6269,Medium,Error when trying to calculate market entitlements out of search screen,Wallmann GEROLD,2024-09-10 09:55:00,2024-09-10 09:59:00,NO,"There is an error alert when I try to execute the following search option: 

Entitlements/Market Entitlements/Calculate/Calculate MAND Events/ 

If I input a date in the field ""reference date"" and press search there is an error alert. 

See attachment 

 ",error alert try execute following search option entitlementsmarket entitlementscalculatecalculate mand event input date field reference date press search error alert see attachment,-0.2244091322645545,0.0,252,0.014995************,0.3061022830661386,1.****************,0.*****************,0.1,0.15,42.62%
Information Request,OEKB-6268,Critical,M12 PROD - DRIP00014303 - Saving of update failed,Bertram Schon,2024-09-10 09:39:00,2024-09-10 10:06:00,NO,"DRIP00014303 
We must update the event by adding the SECU Rate, Fraction Price, comment => 

!image-2024-09-10-09-37-39-849.png! 
!image-2024-09-10-09-37-05-033.png! 

  

After pressing the save button, no message popped up and no draft was created, no update saved. 

Please urgently check! 
Thanks, Bert 

  

  

 ",drip must update event adding secu rate fraction price comment imagepng imagepng pressing save button message popped draft created update saved please urgently check thanks bert,0.002140454947948456,0.0,252,0.014995************,0.24946488626301289,5.****************,0.2,0.15,0.06,48.92%
Information Request,OEKB-6267,Medium,M12 Position Full Load not processed - Entitlement not calculated,Valdes ELIZABEHT,2024-09-10 08:23:00,2024-09-10 08:29:00,NO,"Dear All,  

the position interface was not received yesterday evening - therefore I have tried to upload it via file device.  

At 08:12 I put the file in the device: /opt/jboss/Megara/Devices/MegaCor/position_interface_in 

An Alert raised at 08:12 bit nothing is visible in the application - please help.  

We can't start our daily work!!! 

Thanks & KR,  

Eli",dear position interface received yesterday evening therefore tried upload via file device put file device optjbossmegaradevicesmegacorpositioninterfacein alert raised bit nothing visible application please help cant start daily work thanks kr eli,0.10715828649699688,0.0,252,0.014995************,0.22321042837575078,0.*****************,0.010483112502500294,0.1,0.06,9.05%
Incident,OEKB-6266,Major,M12 PROD - To-Do-List not available,Bertram Schon,2024-09-09 16:47:00,2024-09-09 21:53:00,NO,"Me (and my colleagues) have issues with opening the To-Do-List => 

!image-2024-09-09-16-46-42-204.png! 


Could you please check? 
Thanks, Bert",colleague issue opening todolist imagepng could please check thanks bert,0.032214757055044174,0.0,252,0.014995************,0.24194631073623896,5.****************,0.2,0.13,0.15,58.29%
Incident,OEKB-6265,Blocker,Datenmigration im Rahmen M12 Go-Live führte zu fehlerhaften Buchungen in T2S,Automation test,2024-09-09 10:43:00,2024-09-09 11:23:00,NO,"Im Rahmen einer Datenmigration beim Umstieg auf Megacor 12 (M12) kam es am Wochenende zu einem Fehlverhalten. Dadurch wurden Instruktionen, die bereits zuvor abgewickelt wurden, erneut instruiert und zum Teil erneut abgewickelt. 
Dies betrifft Instruktionen an T2S in Zusammenhang mit 3 Corporate Actions sowie einzelne wenige Claims. 
Fälschlich erteilte Instruktionen und verbundene Buchungen müssen rückabgewickelt werden. 




[This ticket was automatically created]",im rahmen einer datenmigration beim umstieg auf megacor kam e wochenende zu einem fehlverhalten dadurch wurden instruktionen die bereits zuvor abgewickelt wurden erneut instruiert und zum teil erneut abgewickelt dy betrifft instruktionen t zusammenhang mit corporate action sowie einzelne wenige claim flschlich erteilte instruktionen und verbundene buchungen mssen rckabgewickelt werden ticket automatically created,0.07706189900636673,0.0,252,0.014995************,0.*****************,0.*****************,0.0012921485771927904,0.14,0.15,28.3%
Incident,OEKB-6264,Medium,M12 PROD: Wrong tax calculation,Stefan RIBISCH,2024-09-08 13:52:00,2024-09-09 11:23:00,NO,"Event DVCA00099429 

The system rounds up a tax amount of USD 0,072 to USD 0,08 instead of rounding down to USD 0,07 (as it is in M10 PROD). 

This error could be reproduced together with Oussema in M12 QAS (event DVCA0000002506). For a second event with EUR instead of USD and with Nostro Sec Account OCSD instead of CBF, the rounding was different and correct (event DVCA0000002507). 

Please check why the system sometimes rounds up USD 0,072 to USD 0,08 and sometimes rounds it down to USD 0,07. 

Thanks, 
BR, stefan",event dvca system round tax amount usd usd instead rounding usd prod error could reproduced together oussema qas event dvca second event eur instead usd nostro sec account ocsd instead cbf rounding different correct event dvca please check system sometimes round usd usd sometimes round usd thanks br stefan,-0.*****************,0.0,253,0.014747721736528956,0.*****************,0.*****************,0.0054320283355181135,0.1,0.15,27.21%
Information Request,OEKB-6263,Medium,M12 RPOD: Generate CA Meeting Entitlement job configuration,Stefan RIBISCH,2024-09-08 11:13:00,,NO,"Please adapt in all platforms the following configuration: 

  

OLD: 
!image-2024-09-08-11-12-40-293.png! 

NEW: 
!image-2024-09-08-11-13-17-194.png! 

Thanks, 
stefan",please adapt platform following configuration old imagepng new imagepng thanks stefan,0.*****************,0.0,253,0.014747721736528956,0.*****************,0.*****************,0.0054320283355181135,0.1,0.06,10.96%
Information Request,OEKB-6240,Major,M10/M12 NEW Client Cash Account for 225100,Valdes ELIZABEHT,2024-08-26 15:22:00,,NO,"Dear All,  

we have added a new Client Cash Account for 225100 in M10 PROD today.  

!image-2024-08-26-15-21-46-740.png! 

*This has to be considered for the migration to M12!*  

  

KR,  

Eli",dear added new client cash account prod today imagepng considered migration kr eli,0.*****************,0.0,266,0.011874840825337983,0.****************,0.*****************,0.010483112502500294,0.13,0.06,5.41%
Information Request,OEKB-6235,Major,Customer Interface Issue - ORA-00001: unique constraint,Valdes ELIZABEHT,2024-08-22 18:09:00,2024-08-22 18:11:00,NO,"Dear All,  

today I amended Client 202600 (MARBDE6GXXX) - because of OEKB-6015 

  

Now we received the Cutomer Interface at 18:00 which produced this Issue!  

com.palmyra.arch.exception.ProcessException: ORA-00001: unique constraint (DBO_RECETTE.PRC_ALTERNATIVECODEPKPRIMARY_) violated 

  

KR,  

Eli",dear today amended client marbdegxxx oekb received cutomer interface produced issue compalmyraarchexceptionprocessexception os unique constraint dborecetteprcalternativecodepkprimary violated kr eli,0.*****************,0.0,270,0.011108996538242306,0.****************,0.*****************,0.010483112502500294,0.13,0.06,14.39%
Information Request,OEKB-6228,Medium,Security MArket Event not created,Nikolay TSONKOV,2024-08-21 16:03:00,2024-08-21 16:13:00,NO,"Hello colleages,  

today we found one case for one security ( ISIN US67616RHT59 ) with Market Reference RUS67616RHT590 ,visible also in the attached file which we recieved via the ZWP interface in the SecEventDeta 

there are no positions for this security in M10 PROD and we suspect that because of this reason the SecEvent with the reference  RUS67616RHT590 is not created in M10 PROD 

  

can you pls advice if our assumption is correct? or advice why the security event with this reference RUS67616RHT590 can not be found in M10 PROD 

  

BR,  

Niki",hello colleages today found one case one security isin usrht market reference rusrht visible also attached file recieved via zwp interface seceventdeta position security prod suspect reason secevent reference rusrht created prod pls advice assumption correct advice security event reference rusrht found prod br niki,0.007690228521823883,0.0,271,0.010925380975964478,0.*****************,2.***************,0.*****************,0.1,0.06,25.27%
Information Request,OEKB-6225,Major,M10/M12 NEW Cash Account (USD/CHF) for 202900 - Migration,Valdes ELIZABEHT,2024-08-21 11:44:00,2024-08-21 12:07:00,NO,"Dear All,  

we have added two new Client Cash Accounts for Client 202900.  

*This has to be considered for the migration to M12!*  

KR,  

Eli",dear added two new client cash account client considered migration kr eli,0.*****************,0.0,271,0.010925380975964478,0.****************,0.*****************,0.010483112502500294,0.13,0.06,10.61%
Information Request,OEKB-6224,Major,M10/M12 NEW Caseis Depot OCSD214422 - Migration,Valdes ELIZABEHT,2024-08-21 11:41:00,2024-08-21 12:07:00,NO,"Dear All,  

There is an additional custody account for CACEIS (OCSD214422). 

We have only created the Client Cash Account SLA in M10. 

*This is has to be considered in the migration to M12!* 

KR,  

Eli",dear additional custody account caceis ocsd created client cash account sla considered migration kr eli,0.011860046535730362,0.0,271,0.010925380975964478,0.****************,0.*****************,0.010483112502500294,0.13,0.06,17.13%
Incident,OEKB-6222,Medium,M10 - TEND00018102 - Partially Allocation Issue,Bertram Schon,2024-08-20 12:10:00,2024-08-20 13:27:00,NO,"TEND00018102 
As discussed with Oussema during a Skype Session today, we faced issues with Market Payments which were allocated partially which lead to wrong Client Payments => 

!image-2024-08-20-12-05-17-801.png! 

Could you please amend the Allocation Condition for SECU payments in M10?  

Thanks, Bert ",tend discussed oussema skype session today faced issue market payment allocated partially lead wrong client payment imagepng could please amend allocation condition secu payment thanks bert,0.*****************,0.0,272,0.010744800311986828,0.*****************,5.****************,0.2,0.1,0.15,53.09%
Incident,OEKB-6219,Medium,M10 PROD -- Position File --took longer to be processed,Nikolay TSONKOV,2024-08-19 13:04:00,2024-08-19 14:00:00,NO,"Hello colleagues,  

the position file which was delivered today ( 19.08.2024 at 12:10 ) took more than 30 minutes to be processed  

  

can you pls check why it took so long?  

  

BR,  

Niki",hello colleague position file delivered today took minute processed pls check took long br niki,-0.01691337674856186,0.0,273,0.010567204383852655,0.25422834418714046,2.***************,0.*****************,0.1,0.15,39.69%
Information Request,OEKB-6212,Major,PROD: AT000ADDIKO0 wrong eligible Position,christina HUEBNER,2024-08-14 17:25:00,2024-08-14 17:29:00,NO,"unfortunately there are a few wrong position at this Event in Production:  

  

223600 M10 20.333 | CCSYS 17.381 

227200 M10 3.316.620 | CCSYS 3.329.521 

242200 M10 867.761 | CCSYS 857.812 

245300 M10 0 | CCSYS 57.566 - and here we have a Problem! 

We received an instruction about 37.406 shs and the Error accured, that the depot was not found, therefore, Depot was correct, we repaired the instruction with activating the checkbox ""IsCorrect"" and the freeze was done in CCSYS but MegaCore still waits for the freeze cormfirmation.  

  

may we please talk about this in a quick skype-session?  

Thanks 

Greetings 
Christina 

  

 ",unfortunately wrong position event production ccsys ccsys ccsys ccsys problem received instruction shs error accured depot found therefore depot correct repaired instruction activating checkbox iscorrect freeze done ccsys megacore still wait freeze cormfirmation may please talk quick skypesession thanks greeting christina,-0.09816017374396324,0.0,278,0.00972229737161013,0.2745400434359908,0.018873446558108624,0.0006479230969081984,0.13,0.06,19.78%
Incident,OEKB-6192,Medium,"CONV0000012901, CONV0000013702 - Reference Date incorrect",Bertram Schon,2024-08-08 11:01:00,2024-08-08 11:29:00,NO,"Due to unresolved bug OEKB-5267, we have to update some events via EDIT functionality. 
When doing so, the Reference Date is not updated. 

When trying to update via Global View (after we removed WEBB via EDIT functionality), the following error popped up => 

!image-2024-08-08-11-00-28-697.png! 

Could you please check to find a way to update the Reference Date? 

Thanks, Bert",due unresolved bug oekb update event via edit functionality reference date updated trying update via global view removed webb via edit functionality following error popped imagepng could please check find way update reference date thanks bert,-0.4836936127394438,0.0,284,0.008797098451105509,0.37092340318486094,5.****************,0.2,0.1,0.15,73.14%
Information Request,OEKB-6169,Major,SOFF00080390 - Generation of default instructions failed,Bertram Schon,2024-08-01 10:26:00,2024-08-01 10:42:00,NO,"SOFF00080390 
We noticed that for this event (SOFF created via SUPER EVENT CHOS), no default instructions have been generated. 
Moreover, we are also not able to generate them manually. 

Could you please check? 

Thanks, Bert",soff noticed event soff created via super event chos default instruction generated moreover also able generate manually could please check thanks bert,0.013668030500411987,0.0,292,0.007698985849401584,0.246582992374897,5.****************,0.2,0.13,0.06,45.49%
Information Request,OEKB-6162,Medium,"NEW Vermeg Jira User ""Ana Maria Galic""",Valdes ELIZABEHT,2024-07-26 14:44:00,2024-08-14 12:09:00,NO,"Dear Emna,  

Asset Servicing Departement will receive a new collegue starting with 1st of August.  

Please create a VERMEG Jira User for her.  

User in CSD: ana059 

Firstname: Ana Maria 

Lastname: Galic 

e-Mail: [<EMAIL>|mailto:<EMAIL>] 

Please provide User and PW.  

Thanks & KR,  

Eli 

 ",dear emna asset servicing departement receive new collegue starting st august please create vermeg jira user user csd ana firstname ana maria lastname galic email anamariagalicoekbcsdatmailtoanamariagalicoekbcsdat please provide user pw thanks kr eli,0.04331980273127556,18.0,297,0.0070834089290521185,0.2391700493171811,0.*****************,0.010483112502500294,0.1,0.06,11.45%
Information Request,OEKB-6156,Medium,PROD: not able to create EXOF Event for US69343P1057 due to missing positions,christina HUEBNER,2024-07-25 14:46:00,2024-07-25 14:53:00,NO,"There are two Events to create.  

One for ISIN US8766292051 and that worked fine.  
The second one for ISIN US69343P1057 but an error message was received:  

!image-2024-07-25-14-42-50-900.png! 

But there are positions:  

!image-2024-07-25-14-43-18-899.png! 

they may be blocked, but with the other ISIN it worked without error and they are blocked as well:  

!image-2024-07-25-14-44-00-694.png! 

  

Please check, why the EXOF Event for ISIN US69343P1057 can not be created.  

Thanks.  

Greetings 
Christina 

 ",two event create one isin u worked fine second one isin usp error message received imagepng position imagepng may blocked isin worked without error blocked well imagepng please check exof event isin usp created thanks greeting christina,-0.01682426407933235,0.0,298,0.006966330477467914,0.2542060660198331,0.018873446558108624,0.0006479230969081984,0.1,0.06,12.23%
Incident,OEKB-6073,Critical,M10- Urgent Issue - Flow Out (MT565 to custodian) failed,Bertram Schon,2024-06-20 15:03:00,2024-06-20 15:07:00,NO,"BMET00097640 was created as Super Event. 
Client Instruction received/consolidated. 

After trying to send MT565 to the custodian, the following alert was received => 

Flow Out with id 1210104960 failed 

MegaBroker: 

!image-2024-06-20-15-02-08-845.png! 

Obviously, ""SUPR"" was mapped incorrectly instead of the correct CA Option ""CONY"". 

Could you please urgently check? 
Thanks, Bert",bmet created super event client instruction receivedconsolidated trying send mt custodian following alert received flow id failed megabroker imagepng obviously supr mapped incorrectly instead correct ca option cony could please urgently check thanks bert,-0.367961548268795,0.0,333,0.0038874572434761303,0.34199038706719875,5.****************,0.2,0.15,0.15,76.3%
Information Request,OEKB-6058,Medium,E-mail alert: Securities Referential interface processing error,Dalibor VIDIC,2024-06-18 08:46:00,2024-06-18 09:18:00,NO,"Hello, 

We have recieved the following e-mail alert: 

The Securities Referential interface could not be processed completly. 

For further details please referr to ""Interfaces > Incoming Interfaces > Failed Securities Referential Interface"". 

  

It relates to the following ISIN: XS2720321491 

Please check which field is involved here and what should i enter? 

  

Thank you and BR, Dalibor",hello recieved following email alert security referential interface could processed completly detail please referr interface incoming interface failed security referential interface relates following isin x please check field involved enter thank br dalibor,-0.022392338141798973,0.0,336,0.003697863716482932,0.25559808453544974,3.***************,0.*****************,0.1,0.06,28.1%
Information Request,OEKB-6038,Major,Pending trade - Status incorrect,Wallmann GEROLD,2024-06-12 09:59:00,2024-06-12 10:02:00,NO,"The client pendi31ng movement for customer reference 1230127091269742 and client 2030 is not in the correct status. 

It is in the status ""machted"" - but it should be in the status ""settled"" 

The effective settlement date is missing - it should be 31/01/2023 

As far as I know there was a problem in CCSYS - and for this specific trade the update in MegaCor was not done. 

  

Would it be possible to do an update in MegaCor manually ? 

  

Can you please check 

  

I have attached the trade from CCSYS and a screen shot from MegaCor",client pending movement customer reference client correct status status machted status settled effective settlement date missing far know problem ccsys specific trade update megacor done would possible update megacor manually please check attached trade ccsys screen shot megacor,-0.09129484929144382,0.0,342,0.003345965457471272,0.27282371232286096,1.****************,0.*****************,0.13,0.06,28.62%
Incident,OEKB-6023,Major,E-mail Alert = Interface processing error,Dalibor VIDIC,2024-06-07 16:01:00,2024-06-07 16:16:00,NO,"Hello, 

we received today (15:10) an e-mail alert:  

Interface processing error 

An interface could not be processed completly. To determine the interface please refer to ""Interfaces > Interface in Monitoring"", compare End Time column with reception time of the e-mail alert. 

For further details please referr to ""Interfaces > Incoming Interfaces"". 

Please check! 

  

Thank you and BR, Dalibor",hello received today email alert interface processing error interface could processed completly determine interface please refer interface interface monitoring compare end time column reception time email alert detail please referr interface incoming interface please check thank br dalibor,-0.0540686864********,0.0,346,0.003130174057138888,0.*****************,3.***************,0.*****************,0.13,0.15,47.29%
Information Request,OEKB-6015,Major,Client 202600 ist not available in MegaCor M10 PROD,Wallmann GEROLD,2024-06-06 13:31:00,2024-06-06 13:48:00,YES,"Hello, 

  

Unter the menu point Referentials/ Entities / Display Entity Roles / Clients the client 202600 - BIC: MARBDE6GXXX is missing. 

  

Normally the client is generated automatically. 

  

We need this client data in order to create DCA account and SLAs in Megacor. 

  

Please can you check. 

  

Regards, 

  

GEROLD 

 ",hello unter menu point referentials entity display entity role client client bic marbdegxxx missing normally client generated automatically need client data order create dca account slas megacor please check regard gerold,-0.010982483625411987,0.0,347,0.0030784368306890934,0.***************,1.****************,0.*****************,0.13,0.06,25.61%
Incident,OEKB-6009,Medium,M10 PROD - TEND00017702 - Wrong execution date of Related Jobs,Bertram Schon,2024-06-04 10:33:00,2024-06-04 10:35:00,NO,"TEND00017702  
Event was updated via EDIT-functionality (= workaround because of bug described in ticket OEKB-5267).  

When checking Related Jobs, I noticed that the execution times are wrong (not affected by the update via EDIT) => 

  

!image-2024-06-04-10-29-45-295.png! 

Could you please check if you can manipulate the execution times according to the new End of Acceptance Period/Client and Market Deadline? 

Thanks, Bert 

  

 ",tend event updated via editfunctionality workaround bug described ticket oekb checking related job noticed execution time wrong affected update via edit imagepng could please check manipulate execution time according new end acceptance periodclient market deadline thanks bert,-0.26007177866995335,0.0,350,0.0029282996948181888,0.31501794466748834,5.****************,0.2,0.1,0.15,64.75%
Incident,OEKB-5953,Major,Flow Out with id 1209989048 failed,Bertram Schon,2024-05-10 08:56:00,2024-05-10 08:59:00,NO,"Could you please urgently check and process Flow Out 1209989048 (Raw Record attached) for which the following error was received from MegaBroker => 

!image-2024-05-10-08-55-55-247.png! 

We need this MT564 in M10 to set up the event and inform clients asap. 

Thanks, Bert",could please urgently check process flow raw record attached following error received megabroker imagepng need mt set event inform client asap thanks bert,-0.1895446591079235,0.0,375,0.0019304541362277093,0.2973861647769809,5.****************,0.2,0.13,0.15,66.61%
Incident,OEKB-5952,Critical,DRIP00012208- Conflicting Update - Validate/Reject not possible,Bertram Schon,2024-05-10 08:38:00,2024-05-10 08:51:00,NO,"DRIP00012208 
Could you please urgently reject the conflicting update, as we are not able to do so => 

!image-2024-05-10-08-37-33-385.png! 

As we expect cash payment for this event today, your prompt help would be appreciated. 

Thanks, Bert",drip could please urgently reject conflicting update able imagepng expect cash payment event today prompt help would appreciated thanks bert,0.048901475965976715,0.0,375,0.0019304541362277093,0.23777463100850582,5.****************,0.2,0.15,0.15,60.67%
Information Request,OEKB-5947,Medium,Can Megacor deal with a Dummy Issuer,Sonja TAGHIPOUR,2024-05-08 09:38:00,2024-05-08 12:15:00,NO,"Dear all 

we have the following situation: Our dataprovider provides us with specific REDA data sometimes days later and thereby creates a timegap  which delays the creation of a new ISIN in the issuer platform. 

We therefore created a CR with Montran, but we would have to use a DUMMY Issuer in certain cases.  

Our question to you now is if MC can deal with a ""Dummy Issuer"" for a certain ISIN (this dummy issuer will be updated as soon as the correct data is known) for ISIN creation?",dear following situation dataprovider provides u specific reda data sometimes day later thereby creates timegap delay creation new isin issuer platform therefore created cr montran would use dummy issuer certain case question mc deal dummy issuer certain isin dummy issuer updated soon correct data known isin creation,-0.1916994657367468,0.0,377,0.0018671663218015242,0.2979248664341867,0.*****************,0.0045637065289876105,0.1,0.06,19.37%
Incident,OEKB-5946,Major,ASOC Timeout by Megacor.,Lukasz Walczuk,2024-05-07 13:41:00,2024-05-07 14:02:00,NO,"Hello, 

the Customer 225300 cannot enter an instruction for the event DVCA00095753 in the Asset Servicing Client, however the Client works fine but OekB It analyised the problem and said that the displayd error massage is regarding of a timeout from megacor. . 

2024-05-07 12:37:42,977 [https-jsse-nio-8075-exec-5] DEBUG at.oekb.casa.vermeg.oekb.MegaCorServicesOeKBStub - Outbound SOAP-message: <?xml version='1.0' encoding='utf-8'?><soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/""><soapenv:Header><ns2:userName xmlns:ns2=""http://ws.mcOeKB.vermeg.com/metadata"" xmlns:mustUnderstand=""http://schemas.xmlsoap.org/soap/envelope/"" mustUnderstand:mustUnderstand=""0"">casa-webservice@ALL</ns2:userName></soapenv:Header><soapenv:Body><ns2:EventSearchRequest xmlns:ns2=""http://ws.mcOeKB.vermeg.com/metadata""><client>225300</client><status>Activated</status><status>ActivatedBlockedCritical</status><status>ActivatedBlockedNotCritical</status><status>ActivatedBlockedNonMarketData</status><status>CrossChecked</status><status>CrossCheckedBlockedCritical</status><status>CrossCheckedBlockedNotCritical</status><referenceDateFrom>2024-04-30+02:00</referenceDateFrom><referenceDateTo>2024-05-21+02:00</referenceDateTo><numberRequested>25</numberRequested><startPosition>0</startPosition></ns2:EventSearchRequest></soapenv:Body></soapenv:Envelope> 
{color:#ff0000}2024-05-07 12:38:43,046 [https-jsse-nio-8075-exec-5] INFO  org.apache.axis2.transport.http.impl.httpclient3.HTTPSenderImpl - Unable to sendViaPost to url[https://megacor.oekb.at/MegaCor/PalmyraWS/MegaCorServicesOeKB]{color} 
{color:#ff0000}java.net.SocketTimeoutException: Read timed out{color} 

  

Please check urgently. 

*Deadline for issuing instructions is 08.05.2024.* 

  

BR 

Lukasz",hello customer enter instruction event dvca asset servicing client however client work fine oekb analyised problem said displayd error massage regarding timeout megacor debug atoekbcasavermegoekbmegacorservicesoekbstub outbound soapmessage xml version encodingutfsoapenvenvelope xmlnssoapenv xmlnsns xmlnsmustunderstand mustunderstandmustunderstandcasawebserviceallnsusernamesoapenvheadersoapenvbodynseventsearchrequest xmlnsns colorff info orgapacheaxistransport unable sendviapost url colorffjavanetsockettimeoutexception read timed outcolor please check urgently deadline issuing instruction br lukasz,-0.21003603376448154,0.0,377,0.0018671663218015242,0.3025090084411204,0.0022979088623772117,7.888692835938721e-05,0.13,0.15,37.39%
Information Request,OEKB-5924,Major,PROD MEET00033238 Flow Out failed,christina HUEBNER,2024-04-25 14:10:00,2024-04-25 14:13:00,NO,"MT564 for MEET00033238 fail, but the error code is not usefull enough to eliminate the source.  

Also we cant find any special character may cause this problem.  

Please check and revert.  

Thanks.  
Greetings  
Christina",mt meet fail error code usefull enough eliminate source also cant find special character may cause problem please check revert thanks greeting christina,-0.23784100078046322,0.0,389,0.001528706488770407,0.3094602501951158,0.018873446558108624,0.0006479230969081984,0.13,0.06,25.02%
Incident,OEKB-5915,Medium,Security Event Data Interface alert,Valdes ELIZABEHT,2024-04-24 13:28:00,2024-04-24 13:30:00,NO,"Hi All,  

we received an Alert of our Monitoring BOTs for Securities Event Interface: can you please checheck the *Error: String index out of range: -1* 

  
|\\oekb.co.at\fs\services\Casa\Prod\zWP\SecEventData\security-events-delta-reduced-2024-04-24T100110.xml|04/24/2024 12:01:10|Prod|MAT0000723606115|04/24/2024 12:01:42|00:00:31.6942610|Record with 
error found.|String index out of range: -1| 

  

Thanks & KR,  

Eli",hi received alert monitoring bot security event interface please checheck error string index range oekbcoatfsservicescasaprodzwpseceventdatasecurityeventsdeltareducedtxml prodmat record error foundstring index range thanks kr eli,-0.09122048318386078,0.0,390,0.0015034391929775724,0.2728051207959652,0.*****************,0.010483112502500294,0.1,0.15,29.99%
Information Request,OEKB-5903,Minor,Numbering of Pending trades in wrong order,Sonja TAGHIPOUR,2024-04-22 08:51:00,2024-04-22 09:08:00,NO,"Dear all, 

we had an internal pending trades alert and we found out that the numbering of the file from saturday was in the wrong order, see attached. 

I am not sure if this can lead to problems, pls revert if you need anything else (log ..?) 

KR Sonja",dear internal pending trade alert found numbering file saturday wrong order see attached sure lead problem pls revert need anything else log kr sonja,-0.5460927374660969,0.0,393,0.0014301155983078744,0.3865231843665242,0.*****************,0.0045637065289876105,0.07,0.06,28.16%
Incident,OEKB-5862,Medium,"seev.001 failed with error ""Meeting details is a mandatory field"" although <MtgDtls> is available",Bertram Schon,2024-04-02 09:45:00,2024-04-02 09:50:00,NO,"seev.001 NEWM and REPL (attached) were received but impact failed with following error description => 

_""Meeting details is a mandatory field""_ 

  

When checking seev.001, I noticed that Meeting Details are available => 

<MtgDtls><DtAndTm><DtOrDtTm><DtTm>2024-05-13T14:00:00.000+02:00</DtTm></DtOrDtTm></DtAndTm><QrmReqrd>false</QrmReqrd><Lctn><Adr><AdrLine>Van der Valk Hotel Almere</AdrLine><StrtNm>Veluwezoom 45</StrtNm><PstCd>1327 AK</PstCd><TwnNm>Almere</TwnNm><Ctry>NL</Ctry></Adr></Lctn><URLAdr>www.abnamro.com/shareholder</URLAdr></MtgDtls> 


Could you please check? 
Valid impact is required to set up the PROX event and notify clients. 

Thanks, Bert",seev newm repl attached received impact failed following error description meeting detail mandatory field checking seev noticed meeting detail available mtgdtlsdtandtmdtordttmdttmtdttmdtordttmdtandtmqrmreqrdfalseqrmreqrdlctnadradrlinevan der valk hotel almereadrlinestrtnmveluwezoom strtnmpstcd akpstcdtwnnmalmeretwnnmctrynlctryadrlctnurladrwwwabnamrocomshareholderurladrmtgdtls could please check valid impact required set prox event notify client thanks bert,-0.10225447826087475,0.0,413,0.0010247226039275595,0.2755636195652187,5.****************,0.2,0.1,0.15,58.83%
Incident,OEKB-5854,Medium,Super Event MAND - CONV cannot be saved,Bertram Schon,2024-03-27 17:57:00,2024-03-28 08:58:00,NO,"Hi, 
I have to set up a CONV MAND event via SUPER EVENT with CASE option. 
I set up the following data => 

!image-2024-03-27-17-54-46-500.png! 


!image-2024-03-27-17-55-22-572.png! 



!image-2024-03-27-17-55-50-246.png! 

!image-2024-03-27-17-56-32-192.png! 

!image-2024-03-27-17-57-05-740.png! 



However, when trying to save the event, the following error popped up => 


!image-2024-03-27-17-54-09-747.png! 



Please urgently check as I have to process this event tomorrow 28/03! 

Thanks, Bert",hi set conv mand event via super event case option set following data imagepng imagepng imagepng imagepng imagepng however trying save event following error popped imagepng please urgently check process event tomorrow thanks bert,-0.025848152115941048,0.0,418,0.0009427903082879675,0.25646203802898526,5.****************,0.2,0.1,0.15,55.97%
Incident,OEKB-5851,Critical,M10 PROD - BONU00060835 - Generate Market Claims manually failed,Bertram Schon,2024-03-26 10:06:00,2024-03-26 10:39:00,NO,"Hi, 

as already discussed with Thouraya, the following steps have been processed as workaround for claim generation with SECU IN resulting quantity rounded down => 
* Created a draft for BONU00060835 on 22/03 to prevent the system from calculating entitlements and generating claims 
* Rejected the draft on 26/03 in the morning 
* Generated entitlements manually  
* Notified, sent MT564 REPE 
* Generated market payments 
* Updated the rounding in via edit functionality to down 

  

When generating market claims then, i found out that market claims refer to a different event (PARI), in addition, the quantity seems not to consider the Parity In/Out of 1 for 4 => 

!image-2024-03-26-10-04-27-404.png! 

During a Skype Session with Thouraya, I have withdrawn and closed the ""related"" PARI event and tried to generate again, but there are still the same search results with the obscure MainRef of the PARI event. 

So, I did not dare to generate the claims and ask you to check immediately what is wrong here, as claims should be generated today. 

Many thanks, BR Bert 

  

 ",hi already discussed thouraya following step processed workaround claim generation secu resulting quantity rounded created draft bonu prevent system calculating entitlement generating claim rejected draft morning generated entitlement manually notified sent mt repe generated market payment updated rounding via edit functionality generating market claim found market claim refer different event pari addition quantity seems consider parity inout imagepng skype session thouraya withdrawn closed related pari event tried generate still search result obscure mainref pari event dare generate claim ask check immediately wrong claim generated today many thanks br bert,0.04751142859458923,0.0,420,0.0009118819655545162,0.2381221428513527,5.****************,0.2,0.15,0.15,60.72%
Incident,OEKB-5834,Major,Flow Outs (MT564 REPL) failed,Bertram Schon,2024-03-20 12:27:00,2024-03-20 12:31:00,NO,"Could you please check these Flow Outs => 



!image-2024-03-20-12-22-51-061.png! 

I only added this text today => 

+++ UPDATE 20/03/2024 +++ Client Deadlines amended by custodian +++ END OF UPDATE 

and amended the Client Deadlines. Nothing else was changed (NEWM were sent successfully on 18/03), but now MegaBroker response is => 

!image-2024-03-20-12-27-00-182.png! 
Please check and resend the messages, if possible. 

Thanks, Bert 




  

  

 ",could please check flow out imagepng added text today update client deadline amended custodian end update amended client deadline nothing else changed newm sent successfully megabroker response imagepng please check resend message possible thanks bert,0.02291776053607464,0.0,425,0.0008389719092096418,0.24427055986598134,5.****************,0.2,0.13,0.15,58.64%
Incident,OEKB-5819,Critical,e-mail alert = Interface processing error,Wallmann GEROLD,2024-03-08 15:15:00,2024-03-08 15:31:00,NO,"Hello, 

  

On 08.03.2024 at 15:10 we have received again this e-mail alert:  

  

An interface could not be processed completly. To determine the interface please refer to ""Interfaces > Interface in Monitoring"", compare End Time column with reception time of the e-mail alert. 

For further details please referr to ""Interfaces > Incoming Interfaces"". 

  

  

Please check and repair urgently - thank you 

  

GEROLD 

 ",hello received email alert interface could processed completly determine interface please refer interface interface monitoring compare end time column reception time email alert detail please referr interface incoming interface please check repair urgently thank gerold,0.008118176832795143,0.0,437,0.0006868921030384822,0.*****************,1.****************,0.*****************,0.15,0.15,41.4%
Requirement,OEKB-5800,Minor,CSD INTERN MASTER (3i/CC-SYS/MC) Customer Interface Enhancement autom. Depotaktualisierungen,Automation test,2024-03-05 08:16:00,2024-12-19 08:58:00,NO,"Anpassung Customer Interface:  
CC-SYs fungiert als MASTER der Accountsanlage und versorgt die Umsysteme: 
* Issuer Platform 
* MegaCor 

mit dem Customer Interface, derzeit 1 mal täglich (18:00) - sowie 3i bei Updates zu Distribution Accounts.  



Enhancement:  
Erweiterung des Accountstatus ""Inactive"" sodass nicht nur ""aktive"" Accounts angelegt werden, sondern auch inactive gelöscht/deaktiviert werden. Dies würde eine automatisierte Wartung über aktuelle Accounts ermöglichen.  

  

Diese Anpassung benötigt jedoch eine simultane Anpassung in allen 3 Applikationen (CC-SYS/3i/MegaCor) und muss diesbezüglich analysiert werden. 

LG Eli  

  


[This ticket was automatically created]",anpassung customer interface ccsys fungiert al master der accountsanlage und versorgt die umsysteme issuer platform megacor mit dem customer interface derzeit mal tglich sowie bei update zu distribution account enhancement erweiterung de accountstatus inactive sodass nicht nur aktive account angelegt werden sondern auch inactive gelschtdeaktiviert werden dy wrde eine automatisierte wartung ber aktuelle account ermglichen diese anpassung bentigt jedoch eine simultane anpassung allen applikationen ccsysimegacor und mus diesbezglich analysiert werden lg eli ticket automatically created,0.*****************,289.0,441,0.0006425923603555579,0.*****************,0.*****************,0.0012921485771927904,0.07,0.09,9.21%
Incident,OEKB-5790,Critical,CLONE - seev.001 IN failed in MegaBroker,Bertram Schon,2024-03-01 11:51:00,2024-03-01 11:56:00,YES,"Hi, 

today we have the same issue reported with OEKB-5673 in PROD again. 

seev.001 was received from provider IHS Markit and acknowledged in SWIFT Alliance as well as in 3i (message attached). 

However, message failed in MegaBroker, error is => 

_The treatment of the file ID:414d51206d65676162726f6b65722e717b4f7d6521559707 received on the input device MB.TO.MC has failed, a copy of the file was placed in the file device FAILURE_OUTPUT_DEVICE_MB.TO.MC.._ 
_The error message is Invalid string value for field (authrsdPrxy.**********.ntrlCd) in class (com.vermeg.services.swiftConnectorCa.caSwiftConnector.ca.cameeting.AuthrsdPrxy), your value was (NRIN), valid values are [, MASH, MAPO, MAAL, MALR, MAHI, MATK, MANP, MADS, MASL].._ 

Could you please urgently check, we must process this meeting info today and send seev.001 to clients. 

Thanks, Bert",hi today issue reported oekb prod seev received provider ihs markit acknowledged swift alliance well message attached however message failed megabroker error treatment file idddfbebfd received input device mbtomc failed copy file placed file device failureoutputdevicembtomc error message invalid string value field authrsdprxyntrlcd class comvermegservicesswiftconnectorcacaswiftconnectorcacameetingauthrsdprxy value nrin valid value mash mapo maal malr mahi matk manp mads masl could please urgently check must process meeting info today send seev client thanks bert,-0.023042142391204834,0.0,444,0.0006112527611295723,0.2557605355978012,5.****************,0.2,0.15,0.15,63.36%
Information Request,OEKB-5784,Critical,Questions from Montran for the ASOC Webservice Switch from SOAP to REST API,Sonja TAGHIPOUR,2024-02-29 11:48:00,2024-03-01 10:54:00,YES,"Dear all, 

since the M12 ASOC Switch from SOAP to REST also needs to be implemented for the Issuer Platform (3i)  as well, Montran has the follwing quite urgent questions: 
# Pls provide  details for Authentication/ Security 
# Please confirm that HTTP will be used for both Q and Prod, as it is implied in the xlsx, and not HTTPS. 
# Please let us know which port will be used for communication, both Q and Prod 
# Please let us know what authentication mechanism is used for the REST API. Please provide full details. 
# Please let us know if the same authentication mechanism is used for both Q and Prod. 
# In the SOAP version we had to limit the number of breakdowns requested to 25 for performance reasons on the webservice side, using the numberRequested and startPosition parameters. Please let us know if its still the case, and if yes how we can replicate the same behavior. 
# For MegaCor/services/rest/breakdownInstructionOeKB/search is specified that pageNumber, pageSize, forValidation are mandatory parameters, please let us know what values should be used in order to obtain the same results as before. In the SOAP WS we used the  breakdownSearch3I call with the parameters numberRequested, startPosition  and mainReference for the CA reference. 
# Since before we had a specific call for 3i (breakdownSearch3I), please let us know if we need to fill in any other parameters to obtain the same results as before, maybe breakDownAppRef, or how exactly should the requests for MegaCor/services/rest/breakdownInstructionOeKB/search from 3i be configured? 
# For the Response, please give us full details on the format of the data, and any changes from the SOAP API. Especially points 10 and 11. 
# Possible values for breakdownPurpose, are they still: ""Tax Breakdown"" and ""Collection Breakdown""? 
# We did not find a correspondent for the BreakdownReason. Is it breakDownAppRef in the REST API? Are the same options available: Schachtelprivileg, Stiftungen, Öffentlich-rechtliche Körperschaften etc.? 
# Please confirm that the POST method should be used for MegaCor/services/rest/breakdownInstructionOeKB/search, as specified in the xlsx, or if we should actually use GET. 
# Please provide actual examples of requests and responses relevant for 3i. 

Pls urgently help. Thx sonja",dear since asoc switch soap rest also need implemented issuer platform well montran follwing quite urgent question pls provide detail authentication security please confirm http used q prod implied xlsx http please let u know port used communication q prod please let u know authentication mechanism used rest api please provide full detail please let u know authentication mechanism used q prod soap version limit number breakdown requested performance reason webservice side using numberrequested startposition parameter please let u know still case yes replicate behavior megacorservicesrestbreakdowninstructionoekbsearch specified pagenumber pagesize forvalidation mandatory parameter please let u know value used order obtain result soap w used breakdownsearchi call parameter numberrequested startposition mainreference ca reference since specific call breakdownsearchi please let u know need fill parameter obtain result maybe breakdownappref exactly request megacorservicesrestbreakdowninstructionoekbsearch configured response please give u full detail format data change soap api especially point possible value breakdownpurpose still tax breakdown collection breakdown find correspondent breakdownreason breakdownappref rest api option available schachtelprivileg stiftungen ffentlichrechtliche krperschaften etc please confirm post method used megacorservicesrestbreakdowninstructionoekbsearch specified xlsx actually use get please provide actual example request response relevant pls urgently help thx sonja,0.00033723562955856323,0.0,445,0.0006011496416405784,0.24991569109261036,0.*****************,0.0045637065289876105,0.15,0.06,19.67%
Incident,OEKB-5762,Medium,"MT564 - Impact failed due to ""Value with code (2398936701) does not exist any more"" error",Bertram Schon,2024-02-19 15:13:00,2024-03-01 10:51:00,NO,"Could you please check this error => 

_Value with code (2398936701) does not exist any more for the field mvtCashFraction$pk of the class com.vermeg.cm.reverseStockSplit.StockIn._ 

Belongs to 2 MT564 IN (attached) for SPLR event. 

Thanks, Bert 


Additional Info: we find the old ticket OEKB-4667 which is still not resolved but seems to be the same issue. Could you please check?",could please check error value code exist field mvtcashfractionpk class comvermegcmreversestocksplitstockin belongs mt attached splr event thanks bert additional info find old ticket oekb still resolved seems issue could please check,0.004629127681255341,10.0,455,0.0005088621855732917,0.*****************,5.****************,0.2,0.1,0.15,54.83%
Information Request,OEKB-5758,Medium,OCSD9250200 Account not found - although available in M10,Valdes ELIZABEHT,2024-02-16 11:43:00,2024-02-16 12:03:00,YES,"Dear All,  

I analysed the created Issue OEKB-5757 and found out, that the account mentioned as not found is available since 2017 - see screenshot below:  

!image-2024-02-16-11-42-52-351.png! 

  

Please check and advise.  

  

Thanks & KR,  

Eli",dear analysed created issue oekb found account mentioned found available since see screenshot imagepng please check advise thanks kr eli,-0.*****************,0.0,458,0.0004840446839330574,0.****************,0.*****************,0.010483112502500294,0.1,0.06,13.69%
Information Request,OEKB-5757,Medium,Pending Trades interface processing error,Wallmann GEROLD,2024-02-16 09:47:00,2024-02-16 10:14:00,NO,"Today 16.02.2024 at 09:41 we received the following alert 

Please check urgently. 

  

Pending Trades interface processing error 

  

The Pending Trades interface could not be processed completly. 

For further details please referr to ""Interfaces > Incoming Interfaces > Failed Pending Trade Interface"". 

  

Please check urgently.",today received following alert please check urgently pending trade interface processing error pending trade interface could processed completly detail please referr interface incoming interface failed pending trade interface please check urgently,-0.****************,0.0,459,0.****************6933,0.40759964380413294,1.****************,0.*****************,0.1,0.06,44.34%
Incident,OEKB-5748,Medium,e-mail alert: Scheduled Job: Close Event Without Eligible Position failed,Wallmann GEROLD,2024-02-13 08:31:00,2024-02-13 08:42:00,NO,"On 13.02.2024 at 06:45 we received the following e-mail alert: 

  

Scheduled Job: Close Event Without Eligible Position failed 

There has been a problem with the Close Event Without Eligible Position Job. 

  

For further details please refer to ""MegaCor Setup > Administration > Jobs Monitoring > Executed Jobs"" Exception: org.quartz.JobExecutionException: com.palmyra.arch.basicStruct.exception.ValueNotFoundException: Value with code (2391939469) does not exist any more for the field cA$pk of the class com.vermeg.cm.exchangeOfferMand.ExchangeOfferMandEvent. [See nested exception: com.palmyra.arch.basicStruct.exception.ValueNotFoundException: Value with code (2391939469) does not exist any more for the field cA$pk of the class com.vermeg.cm.exchangeOfferMand.ExchangeOfferMandEvent.] 

  

  

Can you please check - thank you. 

  

Gerold 

 ",received following email alert scheduled job close event without eligible position failed problem close event without eligible position job detail please refer megacor setup administration job monitoring executed job exception orgquartzjobexecutionexception compalmyraarchbasicstructexceptionvaluenotfoundexception value code exist field capk class comvermegcmexchangeoffermandexchangeoffermandevent see nested exception compalmyraarchbasicstructexceptionvaluenotfoundexception value code exist field capk class comvermegcmexchangeoffermandexchangeoffermandevent please check thank gerold,-0.31093115732073784,0.0,462,0.00045282718288679695,0.32773278933018446,1.****************,0.*****************,0.1,0.15,45.86%
Incident,OEKB-5743,Medium,Position Interface failed in Prod again,Lukasz Walczuk,2024-02-09 15:32:00,2024-02-09 16:16:00,NO,"Hello,  

today the IPosition Interface failed again. Please check urgently.  

!image-2024-02-09-15-31-59-770.png! 
Error Description: 
com.palmyra.arch.exception.ProcessException: java.sql.SQLSyntaxErrorException: ORA-02049: timeout: distributed transaction waiting for lock 
 ",hello today iposition interface failed please check urgently imagepng error description compalmyraarchexceptionprocessexception javasqlsqlsyntaxerrorexception os timeout distributed transaction waiting lock,-0.23726968839764595,0.0,465,0.00043074254057568753,0.3093174220994115,0.0022979088623772117,7.888692835938721e-05,0.1,0.15,33.91%
Incident,OEKB-5742,Major,MT566 OUT - Outgoing Comment not available in Booking Confirmation,Bertram Schon,2024-02-09 10:14:00,2024-02-09 10:43:00,NO,"LIQU00060119 

I set up an outgoing comment for MT566 OUT => 

!image-2024-02-09-10-11-55-046.png! 

but I noticed that the comment is not available in the SWIFT message => 

!image-2024-02-09-10-13-44-218.png! 
Could you please check! 
Thanks, Bert",liqu set outgoing comment mt imagepng noticed comment available swift message imagepng could please check thanks bert,-0.009145203977823257,0.0,466,0.0004236229926027885,0.2522863009944558,5.****************,0.2,0.13,0.15,59.84%
Information Request,OEKB-5741,Medium,PRODUCTION XMET00035801 - Event not editable,christina HUEBNER,2024-02-08 13:15:00,2024-02-08 13:18:00,NO,"Hello,  

due to interferences the Update of Event XMET00035801 is neither rejectable nor validatable.  

This is known behaviour due to Error:  

!image-2024-02-08-13-14-28-975.png! 

We already created a new working Event, but may you please close the Event? 

Thanks.  

Greetings 
Christina",hello due interference update event xmet neither rejectable validatable known behaviour due error imagepng already created new working event may please close event thanks greeting christina,-0.04399687796831131,0.0,466,0.0004236229926027885,0.2609992194920778,0.018873446558108624,0.0006479230969081984,0.1,0.06,13.25%
Information Request,OEKB-5735,Minor,Emergency Procedure Update 2024,Valdes ELIZABEHT,2024-02-06 11:50:00,2024-02-08 17:10:00,NO,"Dear Emna,  

please find attached our emergency procedure which needs to be updated.  

Can you please start on your side - if you have improvment points please don't hesitate to amend them.  

Thanks & KR,  

Eli",dear emna please find attached emergency procedure need updated please start side improvment point please dont hesitate amend thanks kr eli,0.10107957385480404,2.0,468,0.0004097349789797868,0.224730106536299,0.*****************,0.010483112502500294,0.07,0.06,4.78%
Incident,OEKB-5733,Medium,"SOFF00006001 - Event automatically updated / ""Rounding In"" must be a Critical Field?",Bertram Schon,2024-02-05 07:26:00,2024-02-05 09:01:00,NO,"SOFF00006001 

We noticed that the event was updated by the system automatically although the ""Rounding In"" value was different (event data = ACTUAL, value from incoming 564 = DOWN) and therefore the event should move to ""blocked critical""?! 

MT564 IN received with different Rounding IN indicator on 02/02/2024, 18.04 => 
!image-2024-02-05-07-52-27-048.png! 

MT564 REPE sent with wrong value ""DOWN"" on 02/02/2024, 22.15 => 
[^FlowOut_1209757151 (1).txt] 

I'm quite sure that ""Rounding In"" was defined as Critical Field for all events, could you please check? 

Thanks, Bert ",soff noticed event updated system automatically although rounding value different event data actual value incoming therefore event move blocked critical mt received different rounding indicator imagepng mt repe sent wrong value flowout txt im quite sure rounding defined critical field event could please check thanks bert,-0.01175244152545929,0.0,470,0.00039630226859990606,0.2529381103813648,5.****************,0.2,0.1,0.15,55.44%
Incident,OEKB-5726,Medium,e-mail alert: FlowOut in the status Message Generated,Wallmann GEROLD,2024-01-31 08:20:00,2024-01-31 08:24:00,YES,"On 31.01.2024 at 1:00 we recieved the following e-mail alert: 

FlowOut in the status Message Generated 

FlowOut with id 1209748697 is still in the status Message Generated 

  

Please check and repair - thank you 

  

Gerold",recieved following email alert flowout status message generated flowout id still status message generated please check repair thank gerold,0.02748243324458599,0.0,475,0.0003646156887302732,0.2431293916888535,1.****************,0.*****************,0.1,0.15,33.17%
Incident,OEKB-5708,Medium,For ISIN AT0000A32638 no INTR Event is created,Wallmann GEROLD,2024-01-24 11:25:00,2024-01-24 11:42:00,NO,"For ISIN AT0000A32638 no INTR event is created. 

There should be a coupon payment (=first coupon payment) on 29.02.2024. 

If I try to create the INTR event via ""Recalc Of Future Occurrences Of Loan Schedule"" under Financial Instruments the following error appears: 
|The program has generated an error described in the log file| 
|Description:| 
|Invalid date format for the field (null) in class (null), your input is: (29/2/2025).| 
|Please send the log file to Vermeg:|<EMAIL>| 

  

Can you please check. 

  

Thank you 

  

  

Gerold 

 ",isin ata intr event created coupon payment first coupon payment try create intr event via recalc future occurrence loan schedule financial instrument following error appears program generated error described log file description invalid date format field null class null input please send log file vermegbugreportvermegcom please check thank gerold,-0.3504554182291031,0.0,481,0.00032991791836610337,0.33761385455727577,1.****************,0.*****************,0.1,0.15,47.34%
Incident,OEKB-5703,Blocker,Position Interface failed in Prod,Sonja TAGHIPOUR,2024-01-19 13:22:00,2024-01-19 13:42:00,NO,"Hi Oussema 

the position interface is also failed pls check urgently 

Error Description: com.palmyra.arch.exception.ProcessException: ORA-00001: unique constraint (DBO_RECETTE.BPOS_SECACC_7I0H2H_TYPEUNIQUE_) violated 

We had the same Issue OEKB-3660 in 2019 

  

 ",hi oussema position interface also failed pls check urgently error description compalmyraarchexceptionprocessexception os unique constraint dborecettebpossecaccihhtypeunique violated issue oekb,-0.****************,0.0,486,0.0003035391380788668,0.*****************,0.*****************,0.0045637065289876105,0.14,0.15,60.62%
Information Request,OEKB-5702,Major,pending trade file error due to new custodian Natinal Bank of Belgium,Wallmann GEROLD,2024-01-19 11:33:00,2024-01-19 11:40:00,NO,"Hello, 

  

We have an pending trade file error for ISIN EU000A3K4EQ8. 

The reason is that this ISIN is deposited by the new custodian National Bank of Belgium.  

See attachment for alert in MegaCor. 

  

We created the new custodian in MegaCor PROD unter this points:  

We created a new organization with Identifier = 75120 

We created a new custodian with Identifier = 75120 

We created Nostro Security Account with = NBBEOEKBCSD0101 

  

Then we tried to reprocess the failed pending trades for this ISIN, but it still does not work and creates an error. 

  

Can you please check the reason for this? 

Maybe some data is still missing? 

  

Thank you. 

  

Gerold 

  

 ",hello pending trade file error isin euakeq reason isin deposited new custodian national bank belgium see attachment alert megacor created new custodian megacor prod unter point created new organization identifier created new custodian identifier created nostro security account nbbeoekbcsd tried reprocess failed pending trade isin still work creates error please check reason maybe data still missing thank gerold,-0.*****************,0.0,486,0.0003035391380788668,0.*****************,1.****************,0.*****************,0.13,0.06,32.83%
Incident,OEKB-5675,Medium,M10 PROD SHDS00059513 Validation of Response Cancelation not possible,christina HUEBNER,2024-01-04 09:42:00,2024-01-04 10:07:00,YES,"Hello and a happy new year! 

The Validation of Response Cancelation is not possible:  

!image-2024-01-04-09-39-19-289.png! 

Event is already canceled, but the response should be able to be canceled (and validated) as well - regardless of status of the Event.  

Please check.  
Thanks 

Greetings 
Christina",hello happy new year validation response cancelation possible imagepng event already canceled response able canceled validated well regardless status event please check thanks greeting christina,-0.****************,0.0,502,0.0002324892276573839,0.39103840151801705,0.018873446558108624,0.0006479230969081984,0.1,0.15,46.25%
Incident,OEKB-5673,Critical,seev.001 IN failed in MegaBroker,Bertram Schon,2024-01-03 08:39:00,2024-01-03 08:49:00,YES,"Hi, 

seev.001 was received from provider IHS Markit and acknowledged in SWIFT Alliance as well as in 3i (message attached). 

However, message failed in MegaBroker, error is => 



_The treatment of the file ID:414d51206d65676162726f6b65722e717b4f7d6521559707 received on the input device MB.TO.MC has failed, a copy of the file was placed in the file device FAILURE_OUTPUT_DEVICE_MB.TO.MC.._ 
_The error message is Invalid string value for field (authrsdPrxy.**********.ntrlCd) in class (com.vermeg.services.swiftConnectorCa.caSwiftConnector.ca.cameeting.AuthrsdPrxy), your value was (NRIN), valid values are [, MASH, MAPO, MAAL, MALR, MAHI, MATK, MANP, MADS, MASL].._ 


Could you please urgently check, we must process this meeting info today and send seev.001 to clients. 

Thanks, Bert",hi seev received provider ihs markit acknowledged swift alliance well message attached however message failed megabroker error treatment file idddfbebfd received input device mbtomc failed copy file placed file device failureoutputdevicembtomc error message invalid string value field authrsdprxyntrlcd class comvermegservicesswiftconnectorcacaswiftconnectorcacameetingauthrsdprxy value nrin valid value mash mapo maal malr mahi matk manp mads masl could please urgently check must process meeting info today send seev client thanks bert,-0.019627373665571213,0.0,503,0.00022864651872207256,0.2549068434163928,5.****************,0.2,0.15,0.15,63.24%
Incident,OEKB-5666,Critical,M10 - Business Date incorrect,Bertram Schon,2024-01-02 08:19:00,2024-01-02 08:32:00,NO,"Good morning and happy new year! 

We noticed that the Business Date is incorrect in M10 => 

!image-2024-01-02-08-18-51-119.png! 

Could you please urgently correct this and also check if there are any other issues? 

Thanks, Bert",good morning happy new year noticed business date incorrect imagepng could please urgently correct also check issue thanks bert,-0.09911446273326874,0.0,504,0.0002248673241788482,0.2747786156833172,5.****************,0.2,0.15,0.15,66.22%
Incident,OEKB-5665,Medium,Flow OUT with id 1209674599 failed,Wallmann GEROLD,2023-12-27 09:02:00,2023-12-27 09:06:00,NO,"On 25.12.2023 at 10:00:00 we had the following 2 alerts. 

  

1) Flow Out with id 1209674599 failed 

Event Code : FlowOutSendingError Event Type : FlowOutError Event Level : 5 

The Flow Out with flowId: 1209674599 failed. 

The actual Flow Status is: Sending.Sending Flow 

  

2) FlowOut in the status Created 

FlowOut with id 1209674599 is still in the status Created 

  

Can you please check and repair. 

  

Thank you. 

  

Gerold 

  

 ",following alert flow id failed event code flowoutsendingerror event type flowouterror event level flow flowid failed actual flow status sendingsending flow flowout status created flowout id still status created please check repair thank gerold,-0.3874232042580843,0.0,510,0.00020346836901064417,0.3468558010645211,1.****************,0.*****************,0.1,0.15,48.73%
Incident,OEKB-5660,Major,EXOFM00058815 - Ad Hoc Booking Market Payment failed,Bertram Schon,2023-12-21 10:00:00,2023-12-21 11:11:00,NO,"EXOFM00058815 
Could you please check the following => 

Ad Hoc Booking CASH OUT CLIENT side processed in two steps (2 clients could not be debited immediately, payments had to be resent manually). 

However, MARKET PAYMENT stays in status ""WaitingCashOutPayment"". 
I also do not see a chance to resend the Market Payment manually! 

Could you please let me know how to proceed? 

Thanks, Bert",exofm could please check following ad hoc booking cash client side processed two step client could debited immediately payment resent manually however market payment stay status waitingcashoutpayment also see chance resend market payment manually could please let know proceed thanks bert,0.019570285454392433,0.0,516,0.0001841057936675792,0.2451074286364019,5.****************,0.2,0.13,0.15,58.77%
