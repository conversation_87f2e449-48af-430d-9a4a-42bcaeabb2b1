{% extends 'base.html' %}
{% load static %}
{% load analyzer_filters %}

{% block title %}Client Overview | Vermeg Insights{% endblock %}

{% block extra_css %}
<style>
    /* Card styling */
    .client-card {
        transition: transform 0.2s, box-shadow 0.2s;
        border-radius: 8px;
        overflow: hidden;
        height: 100%;
    }
    .client-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }

    /* Health indicator styling */
    .health-indicator {
        width: 100%;
        height: 8px;
        position: absolute;
        top: 0;
        left: 0;
    }
    .health-excellent {
        background-color: #28a745;
    }
    .health-good {
        background-color: #20c997;
    }
    .health-fair {
        background-color: #ffc107;
    }
    .health-poor {
        background-color: #fd7e14;
    }
    .health-critical {
        background-color: #dc3545;
    }

    /* Metric styling */
    .metric-value {
        font-size: 1.5rem;
        font-weight: 700;
        white-space: nowrap;
    }
    .metric-label {
        font-size: 0.8rem;
        color: var(--text-light);
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Client name styling */
    .client-name {
        font-weight: 600;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-top: 8px;
    }

    /* Trend indicators */
    .trend-indicator {
        font-size: 0.9rem;
        margin-left: 5px;
    }
    .trend-up {
        color: #28a745;
    }
    .trend-down {
        color: #dc3545;
    }
    .trend-neutral {
        color: #6c757d;
    }

    /* Chart container */
    .chart-container {
        height: 300px;
        margin-bottom: 20px;
    }

    /* Sentiment colors */
    .sentiment-positive {
        color: #28a745;
    }
    .sentiment-neutral {
        color: #6c757d;
    }
    .sentiment-uncomfortable {
        color: #ffc107;
    }
    .sentiment-annoyed {
        color: #fd7e14;
    }
    .sentiment-frustrated {
        color: #dc3545;
    }
    .sentiment-very-frustrated {
        color: #990000;
    }
    .sentiment-negative {
        color: #dc3545;
    }

    /* Filter and sort controls */
    .filter-controls {
        background-color: var(--gray-light);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .sort-btn {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.85rem;
        margin-right: 5px;
        margin-bottom: 5px;
        background-color: var(--white);
        border: 1px solid var(--gray-medium);
        color: var(--text-dark);
    }

    .sort-btn.active {
        background-color: var(--dark);
        color: var(--white);
        border-color: var(--dark);
    }

    /* Key metrics summary */
    .key-metrics-summary {
        margin-bottom: 20px;
    }

    .metric-card {
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        background-color: var(--white);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .metric-card i {
        font-size: 2rem;
        margin-bottom: 10px;
        color: var(--primary);
    }

    .metric-card .metric-value {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 5px;
        white-space: nowrap;
    }

    /* Client card badge */
    .client-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page header -->
    <div class="page-header d-flex justify-content-between align-items-center mb-4">
        <div>
            <div class="d-flex align-items-center">
                <div class="me-3" style="width: 5px; height: 25px; background-color: var(--primary);"></div>
                <h1 class="fw-bold mb-0">Client Overview</h1>
            </div>
            <p class="text-secondary mt-2 mb-0">Track client metrics and performance over time</p>
        </div>
    </div>

    {% if has_data %}
        <!-- Key Metrics Summary -->
        <div class="row key-metrics-summary g-4 mb-4">
            <div class="col-md-3">
                <div class="metric-card">
                    <i class="fas fa-users"></i>
                    <div class="metric-value">{{ all_clients|length }}</div>
                    <div class="metric-label">Total Clients</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <i class="fas fa-ticket-alt"></i>
                    <div class="metric-value">
                        {{ total_tickets|floatformat:0 }}
                    </div>
                    <div class="metric-label">Total Tickets</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <i class="fas fa-clock"></i>
                    <div class="metric-value">
                        {{ avg_resolution_time|floatformat:1 }}
                    </div>
                    <div class="metric-label">Avg Resolution Time (Days)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <i class="fas fa-chart-line"></i>
                    <div class="metric-value">
                        {{ avg_client_impact|floatformat:2 }}
                    </div>
                    <div class="metric-label">Avg Customer Experience Score</div>
                </div>
            </div>
        </div>

        <!-- Filter and Sort Controls -->
        <div class="filter-controls mb-4">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3">Sort By</h5>
                    <div class="d-flex flex-wrap">
                        <button class="sort-btn active" data-sort="impact">
                            <i class="fas fa-sort-amount-down me-1"></i> Experience Score
                        </button>
                        <button class="sort-btn" data-sort="sentiment">
                            <i class="fas fa-smile me-1"></i> Sentiment
                        </button>
                        <button class="sort-btn" data-sort="resolution">
                            <i class="fas fa-clock me-1"></i> Resolution Time
                        </button>
                        <button class="sort-btn" data-sort="tickets">
                            <i class="fas fa-ticket-alt me-1"></i> Tickets
                        </button>
                        <button class="sort-btn" data-sort="name">
                            <i class="fas fa-font me-1"></i> Name
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5 class="mb-3">Filter</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <select class="form-select mb-2" id="sentimentFilter">
                                <option value="all">All Sentiments</option>
                                <option value="positive">Positive</option>
                                <option value="neutral">Neutral</option>
                                <option value="uncomfortable">Uncomfortable</option>
                                <option value="annoyed">Annoyed</option>
                                <option value="frustrated">Frustrated</option>
                                <option value="very-frustrated">Very Frustrated</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <select class="form-select mb-2" id="ticketFilter">
                                <option value="all">All Ticket Counts</option>
                                <option value="1-10">1-10 Tickets</option>
                                <option value="11-50">11-50 Tickets</option>
                                <option value="51-100">51-100 Tickets</option>
                                <option value="100+">100+ Tickets</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Cards Grid -->
        <div class="row g-4 mb-5" id="clientGrid">
            {% for client in all_clients %}
                {% if client in latest_metrics %}
                {% with sentiment=latest_metrics|get_item:client|get_item:'sentiment' resolution_time=latest_metrics|get_item:client|get_item:'resolution_time' client_impact=latest_metrics|get_item:client|get_item:'client_impact' tickets=latest_metrics|get_item:client|get_item:'tickets' %}
                <div class="col-md-6 col-lg-4 client-card-container"
                     data-client="{{ client }}"
                     data-sentiment="{{ sentiment }}"
                     data-resolution="{{ resolution_time }}"
                     data-impact="{{ client_impact }}"
                     data-tickets="{{ tickets }}">
                    <div class="card client-card shadow-sm">
                        <!-- Health indicator bar -->
                        {% if client_impact < 0.2 %}
                            <div class="health-indicator health-excellent"></div>
                            <div class="client-badge"><span class="badge bg-success">Excellent</span></div>
                        {% elif client_impact < 0.3 %}
                            <div class="health-indicator health-good"></div>
                            <div class="client-badge"><span class="badge bg-info">Good</span></div>
                        {% elif client_impact < 0.4 %}
                            <div class="health-indicator health-fair"></div>
                            <div class="client-badge"><span class="badge bg-warning">Fair</span></div>
                        {% elif client_impact < 0.6 %}
                            <div class="health-indicator health-poor"></div>
                            <div class="client-badge"><span class="badge bg-orange" style="background-color: #fd7e14; color: white;">Poor</span></div>
                        {% else %}
                            <div class="health-indicator health-critical"></div>
                            <div class="client-badge"><span class="badge bg-danger">Critical</span></div>
                        {% endif %}

                        <div class="card-body pt-4">
                            <div class="text-center mb-3">
                                <h5 class="client-name">{{ client }}</h5>
                                <div class="mt-2">
                                    {% if sentiment > 0.3 %}
                                        <i class="fas fa-smile fa-2x text-success"></i>
                                    {% elif sentiment >= 0 %}
                                        <i class="fas fa-meh fa-2x text-secondary"></i>
                                    {% elif sentiment >= -0.2 %}
                                        <i class="fas fa-meh-rolling-eyes fa-2x text-warning"></i>
                                    {% elif sentiment >= -0.5 %}
                                        <i class="fas fa-angry fa-2x" style="color: #fd7e14;"></i>
                                    {% elif sentiment >= -0.8 %}
                                        <i class="fas fa-frown fa-2x text-danger"></i>
                                    {% else %}
                                        <i class="fas fa-dizzy fa-2x" style="color: #990000;"></i>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row g-3">
                                <!-- Customer Experience Score -->
                                <div class="col-6">
                                    <div class="p-2 bg-light rounded text-center">
                                        <div class="metric-label">Experience Score</div>
                                        <div class="metric-value">{{ client_impact|floatformat:2 }}</div>
                                        {% if client in client_trends %}
                                        <div class="small">
                                            {% with trend=client_trends|get_item:client|get_item:'client_impact_change' %}
                                            {% if trend > 0.1 %}
                                            <span class="text-danger"><i class="fas fa-arrow-up"></i> {{ trend|floatformat:2 }}</span>
                                            {% elif trend < -0.1 %}
                                            <span class="text-success"><i class="fas fa-arrow-down"></i> {{ trend|absolute|floatformat:2 }}</span>
                                            {% else %}
                                            <span class="text-secondary"><i class="fas fa-equals"></i> Stable</span>
                                            {% endif %}
                                            {% endwith %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Resolution Time -->
                                <div class="col-6">
                                    <div class="p-2 bg-light rounded text-center">
                                        <div class="metric-label">Resolution Time</div>
                                        <div class="metric-value">{{ resolution_time|floatformat:1 }}</div>
                                        {% if client in client_trends %}
                                        <div class="small">
                                            {% with trend=client_trends|get_item:client|get_item:'resolution_time_change' %}
                                            {% if trend < -0.5 %}
                                            <span class="text-success"><i class="fas fa-arrow-down"></i> {{ trend|absolute|floatformat:1 }}</span>
                                            {% elif trend > 0.5 %}
                                            <span class="text-danger"><i class="fas fa-arrow-up"></i> {{ trend|floatformat:1 }}</span>
                                            {% else %}
                                            <span class="text-secondary"><i class="fas fa-equals"></i> Stable</span>
                                            {% endif %}
                                            {% endwith %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Tickets -->
                                <div class="col-12">
                                    <div class="p-2 bg-light rounded text-center">
                                        <div class="metric-label">Total Tickets</div>
                                        <div class="metric-value">{{ tickets|floatformat:0 }}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-3">
                                <a href="{% url 'client_detail' client_name=client %}" class="btn btn-danger">
                                    <i class="fas fa-chart-line me-1"></i> View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endwith %}
                {% endif %}
            {% endfor %}
        </div>



    {% else %}
        <!-- Empty state -->
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center py-5">
                <div class="mb-4">
                    <img src="{% static 'images/data-flow-image.png' %}" alt="No Data" class="img-fluid rounded-3 shadow" style="max-height: 200px;">
                </div>
                <h3 class="fw-bold mb-3">No Client Data Available</h3>
                <p class="text-secondary mb-4">Upload and process JIRA files to see client metrics and insights.</p>
                <a href="{% url 'upload_file' %}" class="btn btn-danger btn-lg">
                    <i class="fas fa-upload me-2"></i>Upload Your First File
                </a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        {% if has_data %}
        // Initialize sorting and filtering
        initClientGrid();





        // Initialize client grid sorting and filtering
        function initClientGrid() {
            const clientGrid = document.getElementById('clientGrid');
            const sortButtons = document.querySelectorAll('.sort-btn');
            const sentimentFilter = document.getElementById('sentimentFilter');
            const ticketFilter = document.getElementById('ticketFilter');

            // Sort function
            function sortClients(sortBy) {
                const clientCards = Array.from(document.querySelectorAll('.client-card-container'));

                clientCards.sort((a, b) => {
                    if (sortBy === 'name') {
                        const nameA = a.getAttribute('data-client').toLowerCase();
                        const nameB = b.getAttribute('data-client').toLowerCase();
                        return nameA.localeCompare(nameB);
                    } else {
                        const valueA = parseFloat(a.getAttribute(`data-${sortBy}`));
                        const valueB = parseFloat(b.getAttribute(`data-${sortBy}`));

                        // For impact and sentiment, lower is better
                        if (sortBy === 'impact' || sortBy === 'sentiment') {
                            return valueB - valueA; // Descending
                        }
                        // For resolution, lower is better
                        else if (sortBy === 'resolution') {
                            return valueB - valueA; // Descending
                        }
                        // For tickets, higher is more significant
                        else {
                            return valueB - valueA; // Descending
                        }
                    }
                });

                // Reorder the DOM
                clientCards.forEach(card => {
                    clientGrid.appendChild(card);
                });
            }

            // Filter function
            function filterClients() {
                const sentimentValue = sentimentFilter.value;
                const ticketValue = ticketFilter.value;
                const clientCards = document.querySelectorAll('.client-card-container');

                clientCards.forEach(card => {
                    const sentiment = parseFloat(card.getAttribute('data-sentiment'));
                    const tickets = parseFloat(card.getAttribute('data-tickets'));

                    let showBySentiment = true;
                    let showByTickets = true;

                    // Filter by sentiment
                    if (sentimentValue !== 'all') {
                        if (sentimentValue === 'positive' && sentiment <= 0.3) showBySentiment = false;
                        else if (sentimentValue === 'neutral' && (sentiment < 0 || sentiment > 0.3)) showBySentiment = false;
                        else if (sentimentValue === 'uncomfortable' && (sentiment < -0.2 || sentiment >= 0)) showBySentiment = false;
                        else if (sentimentValue === 'annoyed' && (sentiment < -0.5 || sentiment >= -0.2)) showBySentiment = false;
                        else if (sentimentValue === 'frustrated' && (sentiment < -0.8 || sentiment >= -0.5)) showBySentiment = false;
                        else if (sentimentValue === 'very-frustrated' && sentiment >= -0.8) showBySentiment = false;
                    }

                    // Filter by tickets
                    if (ticketValue !== 'all') {
                        if (ticketValue === '1-10' && (tickets < 1 || tickets > 10)) showByTickets = false;
                        else if (ticketValue === '11-50' && (tickets < 11 || tickets > 50)) showByTickets = false;
                        else if (ticketValue === '51-100' && (tickets < 51 || tickets > 100)) showByTickets = false;
                        else if (ticketValue === '100+' && tickets < 100) showByTickets = false;
                    }

                    // Show or hide based on combined filters
                    if (showBySentiment && showByTickets) {
                        card.style.display = '';
                    } else {
                        card.style.display = 'none';
                    }
                });
            }

            // Add event listeners to sort buttons
            sortButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Update active button
                    sortButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Sort clients
                    sortClients(this.getAttribute('data-sort'));
                });
            });

            // Add event listeners to filters
            sentimentFilter.addEventListener('change', filterClients);
            ticketFilter.addEventListener('change', filterClients);

            // Initial sort by impact (default)
            sortClients('impact');
        }
        {% endif %}
    });
</script>
{% endblock %}
