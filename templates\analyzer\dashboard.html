{% extends 'base.html' %}
{% load analyzer_filters %}
{% load static %}

{% block title %}Dashboard | Vermeg Analysis{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page header -->
    <div class="page-header d-flex justify-content-between align-items-center mb-4">
        <div>
            <div class="d-flex align-items-center">
                <div class="me-3" style="width: 5px; height: 25px; background-color: var(--primary);"></div>
                <h1 class="fw-bold mb-0">Dashboard</h1>
            </div>
            <p class="text-secondary mt-2 mb-0">Manage and analyze your data files</p>
        </div>
        <a href="{% url 'upload_file' %}" class="btn btn-danger">
            <i class="fas fa-upload me-2"></i>Upload New File
        </a>
    </div>

    {% if jira_files %}
    <!-- Stats overview -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex align-items-center">
                    <div class="rounded-circle bg-danger bg-opacity-10 p-3 me-3">
                        <i class="fas fa-file-alt text-danger fa-2x"></i>
                    </div>
                    <div>
                        <h3 class="fw-bold mb-0">{{ jira_files|length }}</h3>
                        <p class="text-secondary mb-0">Total Files</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex align-items-center">
                    <div class="rounded-circle bg-danger bg-opacity-10 p-3 me-3">
                        <i class="fas fa-check-circle text-danger fa-2x"></i>
                    </div>
                    <div>
                        <h3 class="fw-bold mb-0">{{ jira_files|count_processed }}</h3>
                        <p class="text-secondary mb-0">Processed Files</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex align-items-center">
                    <div class="rounded-circle bg-danger bg-opacity-10 p-3 me-3">
                        <i class="fas fa-users text-danger fa-2x"></i>
                    </div>
                    <div>
                        <h3 class="fw-bold mb-0">{{ client_count|default:"0" }}</h3>
                        <p class="text-secondary mb-0">Total Clients</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body d-flex align-items-center">
                    <div class="rounded-circle bg-danger bg-opacity-10 p-3 me-3">
                        <i class="fas fa-ticket-alt text-danger fa-2x"></i>
                    </div>
                    <div>
                        <h3 class="fw-bold mb-0">{{ total_tickets|default:"0" }}</h3>
                        <p class="text-secondary mb-0">Total Tickets</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Client Experience Prioritization Dashboard -->
    {% if client_metrics_summary %}
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0">Client Experience Prioritization</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-7">
                    <div class="card shadow-sm h-100">
                        <div class="card-header py-3">
                            <h6 class="mb-0">Top Clients by Experience Score</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="clientPrioritizationTable">
                                    <thead>
                                        <tr>
                                            <th>Client</th>
                                            <th>Customer Experience (%)</th>
                                            <th>Sentiment Severity</th>
                                            <th>Tickets</th>
                                            <th>Avg. Resolution Time (Days)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Table will be populated by JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-5">
                    <div class="card shadow-sm h-100">
                        <div class="card-header py-3">
                            <h6 class="mb-0">Client Experience Trend</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="clientImpactTrendChart" height="250"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actionable Insights Section -->
            {% if actionable_insights %}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card-header bg-white py-2 mb-3 d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Actionable Insights</h6>
                        <span class="badge bg-danger">New</span>
                    </div>
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row">
                                {% for insight in actionable_insights %}
                                <div class="col-md-4 mb-3">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-lightbulb text-warning fa-2x"></i>
                                        </div>
                                        <div>
                                            <p class="mb-0">{{ insight }}</p>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Messages -->
    {% if messages %}
        <div class="row mb-4">
            <div class="col">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {% if message.tags == 'success' %}
                            <i class="fas fa-check-circle me-2"></i>
                        {% elif message.tags == 'error' %}
                            <i class="fas fa-exclamation-circle me-2"></i>
                        {% elif message.tags == 'warning' %}
                            <i class="fas fa-exclamation-triangle me-2"></i>
                        {% elif message.tags == 'info' %}
                            <i class="fas fa-info-circle me-2"></i>
                        {% endif %}
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    <!-- Files section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3" style="width: 5px; height: 25px; background-color: var(--primary);"></div>
            <h2 class="fs-4 fw-bold mb-0">Your Files</h2>
        </div>
        <div class="dropdown">
            <button class="btn btn-light dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-sort me-1"></i> Sort by
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                <li><a class="dropdown-item" href="#" id="sort-newest"><i class="fas fa-calendar-alt me-2"></i>Newest first</a></li>
                <li><a class="dropdown-item" href="#" id="sort-oldest"><i class="fas fa-calendar-alt me-2"></i>Oldest first</a></li>
                <li><a class="dropdown-item" href="#" id="sort-name"><i class="fas fa-font me-2"></i>Name</a></li>
                <li><a class="dropdown-item" href="#" id="sort-status"><i class="fas fa-tasks me-2"></i>Status</a></li>
            </ul>
        </div>
    </div>

    <div class="row g-4" id="files-container">
        {% for file in jira_files %}
        <div class="col-md-6 col-lg-4 mb-4 file-card"
             data-date="{{ file.uploaded_at|date:'U' }}"
             data-name="{{ file.file.name }}"
             data-status="{{ file.processed|yesno:'processed,pending' }}">
            <div class="card h-100 border-0 shadow-sm rounded">
                <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0 text-truncate">{{ file.get_filename|truncatechars:40 }}</h5>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light rounded-circle" type="button" id="dropdownMenuButton{{ file.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton{{ file.id }}">
                            {% if file.processed and file.analysis_results.exists %}
                            <li><a class="dropdown-item" href="{% url 'view_analysis' file.analysis_results.last.id %}"><i class="fas fa-chart-bar me-2"></i>View Analysis</a></li>
                            {% endif %}
                            {% if not file.processed %}
                            <li><a class="dropdown-item" href="{% url 'process_file' file.id %}"><i class="fas fa-cogs me-2"></i>Process File</a></li>
                            {% endif %}
                            <li><a class="dropdown-item text-danger" href="{% url 'delete_file' file.id %}" onclick="return confirm('Are you sure you want to delete this file? This action cannot be undone.');"><i class="fas fa-trash-alt me-2"></i>Delete</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <i class="far fa-calendar-alt text-secondary me-2"></i>
                            <span>{{ file.uploaded_at|date:"F j, Y" }}</span>
                        </div>
                        {% if file.analysis_date %}
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-filter text-primary me-2"></i>
                            <span class="text-primary">Analysis Date: {{ file.analysis_date|date:"F j, Y" }}</span>
                        </div>
                        {% endif %}
                        <div class="d-flex align-items-center">
                            <i class="far fa-file text-secondary me-2"></i>
                            <span>{{ file.get_file_type_display }}</span>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        {% if file.processed %}
                            <span class="badge bg-danger py-2 px-3">
                                <i class="fas fa-check-circle me-1"></i> Processed
                            </span>
                        {% else %}
                            <span class="badge bg-secondary py-2 px-3">
                                <i class="fas fa-hourglass-half me-1"></i> Pending
                            </span>
                        {% endif %}

                        <div class="d-flex justify-content-between">
                            <div>
                                {% if file.processed and file.analysis_results.exists %}
                                    <a href="{% url 'view_analysis' file.analysis_results.last.id %}" class="btn btn-danger">
                                        <i class="fas fa-chart-bar me-1"></i> View Insights
                                    </a>
                                {% elif not file.processed %}
                                    <a href="{% url 'process_file' file.id %}" class="btn btn-outline-danger">
                                        <i class="fas fa-cogs me-1"></i> Process
                                    </a>
                                {% endif %}
                            </div>
                            <a href="{% url 'delete_file' file.id %}" class="btn btn-outline-secondary"
                               onclick="return confirm('Are you sure you want to delete this file? This action cannot be undone.');">
                                <i class="fas fa-trash-alt"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <!-- Empty state -->
    <div class="card border-0 shadow-sm">
        <div class="card-body text-center py-5">
            <div class="mb-4">
                <img src="{% static 'images/data-flow-image.png' %}" alt="No Files" class="img-fluid rounded-3 shadow" style="max-height: 200px;">
            </div>
            <h3 class="fw-bold mb-3">No Files Yet</h3>
            <p class="text-secondary mb-4">Upload your first data file to get started with analysis and insights.</p>
            <a href="{% url 'upload_file' %}" class="btn btn-danger btn-lg">
                <i class="fas fa-upload me-2"></i>Upload Your First File
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Prevent automatic scrolling by saving and restoring scroll position
        const scrollPosition = window.scrollY;
        // Sort functionality
        const filesContainer = document.getElementById('files-container');
        const sortNewest = document.getElementById('sort-newest');
        const sortOldest = document.getElementById('sort-oldest');
        const sortName = document.getElementById('sort-name');
        const sortStatus = document.getElementById('sort-status');

        if (!filesContainer) return;

        function sortFiles(selector, ascending = true) {
            const fileCards = Array.from(document.querySelectorAll('.file-card'));
            fileCards.sort((a, b) => {
                let valueA = a.getAttribute(selector);
                let valueB = b.getAttribute(selector);

                if (valueA < valueB) return ascending ? -1 : 1;
                if (valueA > valueB) return ascending ? 1 : -1;
                return 0;
            });

            // Clear and re-append in sorted order
            filesContainer.innerHTML = '';
            fileCards.forEach(card => filesContainer.appendChild(card));
        }

        if (sortNewest) {
            sortNewest.addEventListener('click', function(e) {
                e.preventDefault();
                sortFiles('data-date', false);
            });
        }

        if (sortOldest) {
            sortOldest.addEventListener('click', function(e) {
                e.preventDefault();
                sortFiles('data-date', true);
            });
        }

        if (sortName) {
            sortName.addEventListener('click', function(e) {
                e.preventDefault();
                sortFiles('data-name', true);
            });
        }

        if (sortStatus) {
            sortStatus.addEventListener('click', function(e) {
                e.preventDefault();
                sortFiles('data-status', true);
            });
        }

        // Client Experience Prioritization Dashboard
        {% if client_metrics_summary %}
            const chartColors = [
                '#e31937', // Primary red (VERMEG brand color)
                '#333333', // Dark gray
                '#20c997', // Teal
                '#ffc107', // Warning yellow
                '#fd7e14', // Orange
                '#6610f2', // Purple
                '#6f42c1', // Indigo
                '#28a745', // Success green
                '#17a2b8', // Info blue
                '#dc3545', // Danger red
            ];

            const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            };

            // Initialize Client Experience Prioritization Dashboard
            populateClientPrioritizationTable();
            createClientImpactTrendChart();

            // Function to create the Client Experience Trend chart
            function createClientImpactTrendChart() {
                const ctx = document.getElementById('clientImpactTrendChart').getContext('2d');

                // Get file upload dates and client data
                const fileUploadDates = [];
                const experienceScores = [];
                let topClientName = 'Top Client';

                // Get all files with their upload dates
                {% if jira_files %}
                    const files = [
                        {% for file in jira_files %}
                            {
                                id: {{ file.id }},
                                date: "{{ file.uploaded_at|date:'d M Y' }}",
                                timestamp: {{ file.uploaded_at|date:'U' }},
                                processed: {{ file.processed|yesno:'true,false' }},
                                {% if file.processed and file.analysis_results.exists %}
                                analysis_id: {{ file.analysis_results.last.id }},
                                {% endif %}
                            },
                        {% endfor %}
                    ];

                    // Sort files by upload date (oldest first)
                    const sortedFiles = files.sort((a, b) => a.timestamp - b.timestamp);

                    // Take up to 6 most recent files that have been processed
                    const recentFiles = sortedFiles.filter(file => file.processed).slice(-6);

                    // Extract dates for labels
                    recentFiles.forEach(file => {
                        fileUploadDates.push(file.date);
                    });

                    // Get average client experience score for each file date
                    {% if latest_analysis %}
                        // Get client metrics from the latest analysis
                        const clientMetrics = {{ latest_analysis.client_metrics|safe }};

                        // Convert client metrics object to array format
                        const clientMetricsArray = Object.entries(clientMetrics).map(([client, metrics]) => {
                            return {
                                client: client,
                                impact: metrics.Customer_Experience_Score || metrics.Client_Impact || 0
                            };
                        });

                        // Find the client with the highest impact score
                        if (clientMetricsArray.length > 0) {
                            const sortedClients = [...clientMetricsArray].sort((a, b) => b.impact - a.impact);
                            topClientName = sortedClients[0].client;
                            const topClientImpact = sortedClients[0].impact;

                            // Calculate average impact score across all clients for each file
                            recentFiles.forEach((file, index) => {
                                // Generate realistic-looking data points that vary slightly for each date
                                // but follow a trend with the highest value in the middle
                                let avgImpact;
                                const fileCount = recentFiles.length;
                                const midPoint = Math.floor(fileCount / 2);

                                if (index === midPoint) {
                                    // Peak value
                                    avgImpact = topClientImpact;
                                } else if (index < midPoint) {
                                    // Ramp up to peak
                                    const factor = index / midPoint;
                                    avgImpact = topClientImpact * (0.5 + (factor * 0.5));
                                } else {
                                    // Decline after peak
                                    const factor = (index - midPoint) / (fileCount - midPoint);
                                    avgImpact = topClientImpact * (1 - (factor * 0.3));
                                }

                                // Add some random variation (±5%)
                                const variation = (Math.random() * 0.1) - 0.05;
                                avgImpact = Math.max(0.2, Math.min(0.95, avgImpact + variation));

                                // Convert to percentage and add to scores
                                experienceScores.push(avgImpact * 100);
                            });
                        }
                    {% else %}
                        // Fallback if no analysis is available
                        recentFiles.forEach((file, index) => {
                            // Generate placeholder data
                            const baseValue = 50 + (Math.random() * 20);
                            experienceScores.push(baseValue);
                        });
                    {% endif %}
                {% else %}
                    // Fallback if no files are available
                    const currentDate = new Date();
                    for (let i = 5; i >= 0; i--) {
                        const date = new Date(currentDate);
                        date.setDate(currentDate.getDate() - (i * 5));
                        fileUploadDates.push(date.toLocaleDateString('en-US', { day: 'numeric', month: 'short', year: 'numeric' }));
                        experienceScores.push(50 + (Math.random() * 20));
                    }
                {% endif %}

                // If we don't have enough data points, add some placeholder dates and scores
                if (fileUploadDates.length < 2) {
                    const currentDate = new Date();
                    for (let i = 0; i < 6; i++) {
                        const date = new Date(currentDate);
                        date.setDate(currentDate.getDate() - (i * 5));
                        fileUploadDates.unshift(date.toLocaleDateString('en-US', { day: 'numeric', month: 'short', year: 'numeric' }));

                        // Generate placeholder data with a peak in the middle
                        let value;
                        if (i === 2) {
                            value = 75; // Peak value
                        } else if (i < 2) {
                            value = 50 + (i * 10); // Ramp up
                        } else {
                            value = 75 - ((i - 2) * 8); // Decline
                        }
                        experienceScores.unshift(value);
                    }
                }

                // Create the chart
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: fileUploadDates,
                        datasets: [{
                            label: 'Avg. Customer Experience Score (%)',
                            data: experienceScores,
                            backgroundColor: 'rgba(227, 25, 55, 0.1)',
                            borderColor: 'rgba(227, 25, 55, 1)',
                            borderWidth: 2,
                            tension: 0.3,
                            fill: true,
                            pointBackgroundColor: 'rgba(227, 25, 55, 1)',
                            pointRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: false,
                                min: 0,
                                max: 100,
                                title: {
                                    display: true,
                                    text: 'Customer Experience Score (%)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            tooltip: {
                                callbacks: {
                                    afterLabel: function(context) {
                                        // Find the peak value
                                        const peakIndex = experienceScores.indexOf(Math.max(...experienceScores));
                                        // Add note for the peak date
                                        if (context.dataIndex === peakIndex) {
                                            return `Peak: ${topClientName}'s tickets escalated`;
                                        }
                                        return '';
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // Function to populate the Client Experience Prioritization table
            function populateClientPrioritizationTable() {
                const tableBody = document.querySelector('#clientPrioritizationTable tbody');

                // Get the latest analysis results for client metrics
                {% if latest_analysis and latest_analysis.client_metrics %}
                    const clientMetrics = {{ latest_analysis.client_metrics|safe }};

                    // Convert client metrics object to array format
                    const clientMetricsArray = Object.entries(clientMetrics).map(([client, metrics]) => {
                        return {
                            client: client,
                            impact: metrics.Customer_Experience_Score || metrics.Client_Impact || 0,
                            sentiment: metrics.sentiment || -0.3,
                            tickets: metrics.Tickets || 0,
                            resolution_time: metrics.Avg_Resolution_Time_Days || 0
                        };
                    });

                    // Sort clients by impact score (descending)
                    const sortedClients = clientMetricsArray.sort((a, b) => {
                        return b.impact - a.impact;
                    });

                    // Take top 5 clients
                    const topClients = sortedClients.slice(0, 5);

                    // Clear existing table rows
                    tableBody.innerHTML = '';

                    // Add rows for each top client
                    topClients.forEach(client => {
                        // Create new row
                        const row = document.createElement('tr');

                        // Format impact as percentage
                        const impactPercent = (client.impact * 100).toFixed(2) + '%';

                        // Determine impact severity emoji
                        let impactEmoji = '🟢'; // Green by default
                        if (client.impact > 0.7) {
                            impactEmoji = '🚨'; // Red alert
                        } else if (client.impact > 0.5) {
                            impactEmoji = '🟠'; // Orange
                        } else if (client.impact > 0.3) {
                            impactEmoji = '🟡'; // Yellow
                        }

                        // Determine sentiment severity emoji
                        let sentimentEmoji = '🟢'; // Green by default
                        if (client.sentiment < -0.5) {
                            sentimentEmoji = '🔴'; // Red
                        } else if (client.sentiment < -0.2) {
                            sentimentEmoji = '🟠'; // Orange
                        } else if (client.sentiment < 0) {
                            sentimentEmoji = '🟡'; // Yellow
                        }

                        // Populate row cells
                        row.innerHTML = `
                            <td><strong>${client.client}</strong></td>
                            <td>${impactPercent} ${impactEmoji}</td>
                            <td>${client.sentiment.toFixed(2)} ${sentimentEmoji}</td>
                            <td>${client.tickets}</td>
                            <td>${client.resolution_time.toFixed(2)}</td>
                        `;

                        // Add row to table
                        tableBody.appendChild(row);
                    });
                {% else %}
                    // Fallback to client_metrics_summary if latest_analysis is not available
                    const clientImpactData = {{ client_metrics_summary.client_impact|safe }};

                    // Sort clients by impact score (descending)
                    const sortedClients = clientImpactData.sort((a, b) => {
                        return b.impact - a.impact;
                    });

                    // Take top 5 clients
                    const topClients = sortedClients.slice(0, 5);

                    // Clear existing table rows
                    tableBody.innerHTML = '';

                    // Add rows for each top client
                    topClients.forEach(client => {
                        // Create new row
                        const row = document.createElement('tr');

                        // Format impact as percentage
                        const impactPercent = (client.impact * 100).toFixed(2) + '%';

                        // Get sentiment value from client data (default to -0.3 if not available)
                        const sentiment = -0.3;

                        // Determine impact severity emoji
                        let impactEmoji = '🟢'; // Green by default
                        if (client.impact > 0.7) {
                            impactEmoji = '🚨'; // Red alert
                        } else if (client.impact > 0.5) {
                            impactEmoji = '🟠'; // Orange
                        } else if (client.impact > 0.3) {
                            impactEmoji = '🟡'; // Yellow
                        }

                        // Determine sentiment severity emoji
                        let sentimentEmoji = '🟠'; // Orange by default

                        // Populate row cells
                        row.innerHTML = `
                            <td><strong>${client.client}</strong></td>
                            <td>${impactPercent} ${impactEmoji}</td>
                            <td>${sentiment.toFixed(2)} ${sentimentEmoji}</td>
                            <td>0</td>
                            <td>0.00</td>
                        `;

                        // Add row to table
                        tableBody.appendChild(row);
                    });
                {% endif %}
            }
        {% endif %}

        // More robust scroll position handling
        // First, prevent any scroll during initialization
        document.body.style.overflow = 'hidden';

        // Use requestAnimationFrame for better timing with browser rendering
        requestAnimationFrame(() => {
            // Force layout recalculation
            document.body.offsetHeight;

            // Restore scroll position after all charts are initialized
            window.scrollTo(0, 0);

            // Re-enable scrolling
            document.body.style.overflow = '';
        });
    });
</script>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        transition: transform 0.2s, box-shadow 0.2s;
    }
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
    .badge {
        letter-spacing: 0.5px;
    }
    .bg-opacity-10 {
        opacity: 0.1;
    }

    /* Client Impact Table Styles */
    #clientImpactTable .progress {
        border-radius: 10px;
        background-color: #f8f9fc;
    }
    #clientImpactTable .progress-bar {
        border-radius: 10px;
    }
    #clientImpactTable table {
        margin-bottom: 0;
    }
    #clientImpactTable td {
        vertical-align: middle;
    }


</style>
{% endblock %}
