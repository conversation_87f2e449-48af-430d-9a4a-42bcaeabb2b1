2025-05-01 17:54:47,621 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-01 17:55:06,548 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-01 17:55:20,002 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-01 17:57:47,968 - ERROR - Internal Server Error: /analysis/26/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1026, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'custom_filters'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 85, in view_analysis
    return render(request, 'analyzer/view_analysis.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1088, in load
    lib = find_library(parser, name)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1028, in find_library
    raise TemplateSyntaxError(
    ...<5 lines>...
    )
django.template.exceptions.TemplateSyntaxError: 'custom_filters' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
analyzer_filters
cache
i18n
l10n
log
static
tz
2025-05-01 17:57:48,043 - WARNING - Not Found: /favicon.ico
2025-05-01 18:00:11,952 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-01 18:01:19,483 - ERROR - Internal Server Error: /analysis/26/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 85, in view_analysis
    return render(request, 'analyzer/view_analysis.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'debug_analysis' not found. 'debug_analysis' is not a valid view function or pattern name.
2025-05-01 18:01:20,617 - ERROR - Internal Server Error: /analysis/26/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 85, in view_analysis
    return render(request, 'analyzer/view_analysis.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'debug_analysis' not found. 'debug_analysis' is not a valid view function or pattern name.
2025-05-01 18:01:36,072 - ERROR - Internal Server Error: /analysis/26/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 85, in view_analysis
    return render(request, 'analyzer/view_analysis.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'debug_analysis' not found. 'debug_analysis' is not a valid view function or pattern name.
2025-05-01 18:01:47,461 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\186c10308c1e45df9b5616a6f7093192.xlsx
2025-05-01 18:02:44,229 - INFO - Generated client metrics for 8 creators
2025-05-01 18:02:44,231 - INFO - Analysis completed successfully
2025-05-01 18:03:02,742 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\186c10308c1e45df9b5616a6f7093192.xlsx
2025-05-01 18:03:23,262 - INFO - Generated client metrics for 8 creators
2025-05-01 18:03:23,264 - INFO - Analysis completed successfully
2025-05-01 18:03:44,376 - ERROR - Internal Server Error: /analysis/26/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 85, in view_analysis
    return render(request, 'analyzer/view_analysis.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'debug_analysis' not found. 'debug_analysis' is not a valid view function or pattern name.
2025-05-01 18:03:47,698 - ERROR - Internal Server Error: /analysis/17/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 85, in view_analysis
    return render(request, 'analyzer/view_analysis.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'debug_analysis' not found. 'debug_analysis' is not a valid view function or pattern name.
2025-05-01 18:05:27,890 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:05:50,676 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:06:21,895 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:06:38,778 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:07:12,317 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:07:29,231 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:08:07,747 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\186c10308c1e45df9b5616a6f7093192.xlsx
2025-05-01 18:08:14,983 - ERROR - Internal Server Error: /analysis/26/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 85, in view_analysis
    return render(request, 'analyzer/view_analysis.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'debug_analysis' not found. 'debug_analysis' is not a valid view function or pattern name.
2025-05-01 18:08:23,535 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\b8c7a5b6d64e4c3aa06b346dd2d6c7cf.xlsx
2025-05-01 18:09:42,334 - INFO - Generated client metrics for 8 creators
2025-05-01 18:09:42,338 - INFO - Analysis completed successfully
2025-05-01 18:09:59,148 - INFO - Generated client metrics for 8 creators
2025-05-01 18:09:59,151 - INFO - Analysis completed successfully
2025-05-01 18:09:59,238 - ERROR - Internal Server Error: /analysis/28/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 85, in view_analysis
    return render(request, 'analyzer/view_analysis.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'debug_analysis' not found. 'debug_analysis' is not a valid view function or pattern name.
2025-05-01 18:14:52,717 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:15:15,539 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:15:37,351 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:16:00,593 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:16:21,544 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:16:43,374 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:17:09,705 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:17:42,438 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:18:13,303 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:18:43,044 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:19:07,009 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 18:20:00,732 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\urls.py changed, reloading.
2025-05-01 18:22:15,181 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\be37792395dc450282f51d3fd00d7d54.xlsx
2025-05-01 18:22:15,761 - INFO - Successfully loaded 'general_report' sheet
2025-05-01 18:22:15,763 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-01 18:22:15,763 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-01 18:22:15,765 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-01 18:22:16,084 - INFO - Cleaned Creator column
2025-05-01 18:22:16,088 - INFO - Removed duplicates based on Key column
2025-05-01 18:22:21,902 - INFO - Using 'Description.1' column for text analysis
2025-05-01 18:24:01,235 - INFO - Using 'Created' as creation date
2025-05-01 18:24:01,236 - INFO - Using 'Date of First Response' as response date
2025-05-01 18:24:01,244 - INFO - Using 'Priority' for priority impact calculation
2025-05-01 18:24:01,247 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-01 18:24:01,304 - INFO - Generated client metrics for 8 creators
2025-05-01 18:24:01,696 - WARNING - No Status column found, using default status distribution
2025-05-01 18:24:01,698 - INFO - Analysis completed successfully
2025-05-01 19:01:33,274 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-01 19:02:08,268 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\urls.py changed, reloading.
2025-05-01 19:08:17,652 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 19:08:44,764 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\d06c2ec456604fdfb748c3e78cb93719.xlsx
2025-05-01 19:08:45,512 - INFO - Successfully loaded 'general_report' sheet
2025-05-01 19:08:45,516 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-01 19:08:45,520 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-01 19:08:45,522 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-01 19:08:45,834 - INFO - Cleaned Creator column
2025-05-01 19:08:45,836 - INFO - Removed duplicates based on Key column
2025-05-01 19:08:51,276 - INFO - Using 'Description.1' column for text analysis
2025-05-01 19:10:22,519 - INFO - Using 'Created' as creation date
2025-05-01 19:10:22,521 - INFO - Using 'Date of First Response' as response date
2025-05-01 19:10:22,530 - INFO - Using 'Priority' for priority impact calculation
2025-05-01 19:10:22,532 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-01 19:10:22,576 - INFO - Generated client metrics for 11 creators
2025-05-01 19:10:22,715 - WARNING - No Status column found, using default status distribution
2025-05-01 19:10:22,716 - INFO - Analysis completed successfully
2025-05-01 22:13:55,326 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 22:14:16,397 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\e170e7059e9949a69af2f25f363ff7a6.xlsx
2025-05-01 22:14:16,709 - INFO - Successfully loaded 'general_report' sheet
2025-05-01 22:14:16,710 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-01 22:14:16,711 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-01 22:14:16,712 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-01 22:14:16,941 - INFO - Cleaned Creator column
2025-05-01 22:14:16,942 - INFO - Removed duplicates based on Key column
2025-05-01 22:14:21,752 - INFO - Using 'Description.1' column for text analysis
2025-05-01 22:15:44,472 - INFO - Using 'Created' as creation date
2025-05-01 22:15:44,473 - INFO - Using 'Date of First Response' as response date
2025-05-01 22:15:44,478 - INFO - Using 'Priority' for priority impact calculation
2025-05-01 22:15:44,479 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-01 22:15:44,524 - INFO - Generated client metrics for 11 creators
2025-05-01 22:15:44,854 - WARNING - No Status column found, using default status distribution
2025-05-01 22:15:44,862 - INFO - Analysis completed successfully
2025-05-01 22:29:54,726 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-01 22:30:13,565 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-01 22:33:37,354 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-01 23:41:50,180 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 23:41:50,415 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 23:43:44,136 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 23:43:44,185 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 23:49:35,430 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\4f895fb2b0d445989ab5f6af433b6b14.xlsx
2025-05-01 23:49:35,789 - INFO - Successfully loaded 'general_report' sheet
2025-05-01 23:49:35,791 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-01 23:49:35,792 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-01 23:49:35,793 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-01 23:49:36,088 - INFO - Cleaned Creator column
2025-05-01 23:49:36,090 - INFO - Removed duplicates based on Key column
2025-05-01 23:49:41,074 - INFO - Using 'Description.1' column for text analysis
2025-05-01 23:50:40,074 - INFO - Using 'Created' as creation date
2025-05-01 23:50:40,075 - INFO - Using 'Date of First Response' as response date
2025-05-01 23:50:40,079 - INFO - Using 'Priority' for priority impact calculation
2025-05-01 23:50:40,081 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-01 23:50:40,139 - INFO - Generated client metrics for 8 creators
2025-05-01 23:52:28,199 - WARNING - No Status column found, using default status distribution
2025-05-01 23:52:28,209 - INFO - Analysis completed successfully
2025-05-01 23:56:40,176 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\4f895fb2b0d445989ab5f6af433b6b14.xlsx
2025-05-01 23:56:40,237 - INFO - Successfully loaded 'general_report' sheet
2025-05-01 23:56:40,239 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-01 23:56:40,239 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-01 23:56:40,240 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-01 23:56:40,446 - INFO - Cleaned Creator column
2025-05-01 23:56:40,447 - INFO - Removed duplicates based on Key column
2025-05-01 23:56:44,589 - INFO - Using 'Description.1' column for text analysis
2025-05-01 23:57:06,048 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 23:57:06,413 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 23:57:12,969 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\4f895fb2b0d445989ab5f6af433b6b14.xlsx
2025-05-01 23:57:13,191 - INFO - Successfully loaded 'general_report' sheet
2025-05-01 23:57:13,192 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-01 23:57:13,192 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-01 23:57:13,193 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-01 23:57:13,302 - INFO - Cleaned Creator column
2025-05-01 23:57:13,304 - INFO - Removed duplicates based on Key column
2025-05-01 23:57:16,388 - INFO - Using 'Description.1' column for text analysis
2025-05-01 23:57:32,563 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 23:57:32,805 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-01 23:57:38,320 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\4f895fb2b0d445989ab5f6af433b6b14.xlsx
2025-05-02 00:02:24,974 - ERROR - Sentiment error for text 'The system consistently crashe...': name 'tokenizer' is not defined
2025-05-02 00:02:24,974 - ERROR - Sentiment error for text 'Users have requested the abili...': name 'tokenizer' is not defined
2025-05-02 00:02:24,974 - ERROR - Sentiment error for text 'The login page does not displa...': name 'tokenizer' is not defined
2025-05-02 00:02:24,974 - ERROR - Sentiment error for text 'Database queries are taking to...': name 'tokenizer' is not defined
2025-05-02 00:02:24,974 - ERROR - Sentiment error for text 'A critical security vulnerabil...': name 'tokenizer' is not defined
2025-05-02 00:02:24,975 - ERROR - Sentiment error for text 'As we expand to international ...': name 'tokenizer' is not defined
2025-05-02 00:02:24,975 - ERROR - Sentiment error for text 'The pie charts and bar graphs ...': name 'tokenizer' is not defined
2025-05-02 00:02:24,975 - ERROR - Sentiment error for text 'We need to implement SSO integ...': name 'tokenizer' is not defined
2025-05-02 00:02:24,975 - ERROR - Sentiment error for text 'When generating reports with m...': name 'tokenizer' is not defined
2025-05-02 00:02:24,975 - ERROR - Sentiment error for text 'Our current error logging syst...': name 'tokenizer' is not defined
2025-05-02 13:52:46,715 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 96, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 259: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 13:52:47,018 - WARNING - Not Found: /favicon.ico
2025-05-02 13:59:12,381 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 96, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 259: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:01:18,027 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-02 14:03:37,577 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:03:38,547 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:03:45,659 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:03:48,891 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:03:56,582 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:05:49,812 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:05:50,901 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:05:53,902 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:06:57,816 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:08:00,958 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:09:05,142 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:11:15,553 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:11:17,098 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:11:33,313 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:16:16,250 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:16:23,041 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:16:38,942 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:16:42,896 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:16:44,983 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:16:49,732 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:16:53,378 - ERROR - Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endblock'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 80, in dashboard
    return render(request, 'analyzer/dashboard.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 228: 'endblock', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-02 14:21:14,922 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\06847574493f4426a62f075b51a96d2e.xlsx
2025-05-02 14:21:15,990 - INFO - Successfully loaded 'general_report' sheet
2025-05-02 14:21:15,996 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-02 14:21:15,997 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-02 14:21:15,998 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-02 14:21:16,325 - INFO - Cleaned Creator column
2025-05-02 14:21:16,329 - INFO - Removed duplicates based on Key column
2025-05-02 14:21:23,372 - INFO - Using 'Description.1' column for text analysis
2025-05-02 14:22:35,844 - INFO - Using 'Created' as creation date
2025-05-02 14:22:35,845 - INFO - Using 'Date of First Response' as response date
2025-05-02 14:22:35,859 - INFO - Using 'Priority' for priority impact calculation
2025-05-02 14:22:35,860 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-02 14:22:35,990 - INFO - Generated client metrics for 8 creators
2025-05-02 14:22:38,099 - WARNING - No Status column found, using default status distribution
2025-05-02 14:22:38,101 - INFO - Analysis completed successfully
2025-05-02 14:24:57,554 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-03 23:20:48,025 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\urls.py changed, reloading.
2025-05-03 23:21:05,038 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-03 23:32:14,584 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-03 23:32:27,679 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-03 23:35:24,973 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-03 23:36:15,993 - ERROR - Internal Server Error: /client-survey/
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 284, in client_survey
    return render(request, 'analyzer/client_survey.html', context)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 961, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'get_item'
2025-05-03 23:36:16,118 - WARNING - Not Found: /favicon.ico
2025-05-03 23:39:42,389 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-03 23:40:02,498 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-04 15:31:23,737 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 15:32:19,772 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\google_sheets_service.py changed, reloading.
2025-05-04 15:34:19,958 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 15:35:14,218 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\google_sheets_service.py changed, reloading.
2025-05-04 15:37:08,036 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 15:40:03,331 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 16:00:33,582 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 16:04:48,184 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\urls.py changed, reloading.
2025-05-04 16:06:01,111 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-04 16:06:37,254 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-04 16:20:51,590 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 16:21:48,052 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\075d115162cc4513ad587adb008e2b64.xlsx
2025-05-04 16:21:48,865 - INFO - Successfully loaded 'general_report' sheet
2025-05-04 16:21:48,869 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-04 16:21:48,869 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-04 16:21:48,870 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-04 16:21:49,192 - INFO - Cleaned Creator column
2025-05-04 16:21:49,194 - INFO - Removed duplicates based on Key column
2025-05-04 16:21:54,468 - INFO - Using 'Description.1' column for text analysis
2025-05-04 16:23:04,912 - INFO - Using 'Created' as creation date
2025-05-04 16:23:04,928 - INFO - Using 'Date of First Response' as response date
2025-05-04 16:23:04,956 - INFO - Using 'Priority' for priority impact calculation
2025-05-04 16:23:04,957 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-04 16:23:05,023 - INFO - Generated client metrics for 11 creators
2025-05-04 16:23:06,231 - WARNING - No Status column found, using default status distribution
2025-05-04 16:23:06,231 - INFO - Analysis completed successfully
2025-05-04 16:23:27,104 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\6e6d6facb13f4bfc9e3a3f3a88e55963.xlsx
2025-05-04 16:23:27,430 - INFO - Successfully loaded 'general_report' sheet
2025-05-04 16:23:27,431 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-04 16:23:27,432 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-04 16:23:27,435 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-04 16:23:27,687 - INFO - Cleaned Creator column
2025-05-04 16:23:27,688 - INFO - Removed duplicates based on Key column
2025-05-04 16:23:29,096 - INFO - Using 'Description.1' column for text analysis
2025-05-04 16:24:50,228 - INFO - Using 'Created' as creation date
2025-05-04 16:24:50,229 - INFO - Using 'Date of First Response' as response date
2025-05-04 16:24:50,235 - INFO - Using 'Priority' for priority impact calculation
2025-05-04 16:24:50,237 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-04 16:24:50,321 - INFO - Generated client metrics for 8 creators
2025-05-04 16:24:51,696 - WARNING - No Status column found, using default status distribution
2025-05-04 16:24:51,697 - INFO - Analysis completed successfully
2025-05-04 16:25:29,847 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\404fcb3d419c415084767ac422b434fd.xlsx
2025-05-04 16:25:29,955 - INFO - Successfully loaded 'general_report' sheet
2025-05-04 16:25:29,956 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-04 16:25:29,956 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-04 16:25:29,958 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-04 16:25:30,239 - INFO - Cleaned Creator column
2025-05-04 16:25:30,247 - INFO - Removed duplicates based on Key column
2025-05-04 16:25:31,847 - INFO - Using 'Description.1' column for text analysis
2025-05-04 16:26:41,210 - INFO - Using 'Created' as creation date
2025-05-04 16:26:41,212 - INFO - Using 'Date of First Response' as response date
2025-05-04 16:26:41,218 - INFO - Using 'Priority' for priority impact calculation
2025-05-04 16:26:41,219 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-04 16:26:41,280 - INFO - Generated client metrics for 8 creators
2025-05-04 16:26:42,171 - WARNING - No Status column found, using default status distribution
2025-05-04 16:26:42,172 - INFO - Analysis completed successfully
2025-05-04 16:32:15,555 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 16:38:45,993 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 16:42:27,688 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 17:40:38,206 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 17:51:39,425 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 17:51:41,597 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 17:56:18,392 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\urls.py changed, reloading.
2025-05-04 17:57:12,329 - WARNING - Not Found: /favicon.ico
2025-05-04 18:05:08,926 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 18:08:04,521 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 19:24:46,790 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-04 19:25:08,707 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\urls.py changed, reloading.
2025-05-04 19:25:32,267 - INFO - C:\Users\<USER>\Desktop\APP D\jira_analyzer\settings.py changed, reloading.
2025-05-04 19:28:31,330 - INFO - C:\Users\<USER>\Desktop\APP D\accounts\views.py changed, reloading.
2025-05-04 19:28:48,599 - INFO - C:\Users\<USER>\Desktop\APP D\accounts\views.py changed, reloading.
2025-05-04 19:35:15,428 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 19:36:41,871 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 19:48:59,523 - INFO - C:\Users\<USER>\Desktop\APP D\accounts\urls.py changed, reloading.
2025-05-04 20:00:32,643 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-04 20:03:49,495 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-04 23:00:46,838 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 23:04:49,229 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-04 23:11:38,434 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-04 23:47:32,040 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-05 15:27:06,921 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-05 15:27:48,285 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\2b27126ef97c4c5d9b31a97290654d2c.xlsx
2025-05-05 15:27:48,647 - INFO - Successfully loaded 'general_report' sheet
2025-05-05 15:27:48,650 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-05 15:27:48,650 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-05 15:27:48,651 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-05 15:27:48,771 - INFO - Cleaned Creator column
2025-05-05 15:27:48,772 - INFO - Removed duplicates based on Key column
2025-05-05 15:27:53,479 - INFO - Using 'Description.1' column for text analysis
2025-05-05 15:29:03,901 - INFO - Using 'Created' as creation date
2025-05-05 15:29:03,902 - INFO - Using 'Date of First Response' as response date
2025-05-05 15:29:03,915 - INFO - Using 'Priority' for priority impact calculation
2025-05-05 15:29:03,916 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-05 15:29:03,975 - INFO - Generated client metrics for 8 creators
2025-05-05 15:29:05,672 - WARNING - No Status column found, using default status distribution
2025-05-05 15:29:05,674 - INFO - Analysis completed successfully
2025-05-05 16:00:51,576 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-05 16:05:51,600 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-05 16:26:59,080 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-06 08:30:57,910 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-06 08:52:27,511 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\91f9b8f933404f5c9a7bd301ce502258.xlsx
2025-05-06 08:52:27,979 - INFO - Successfully loaded 'general_report' sheet
2025-05-06 08:52:27,984 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-06 08:52:27,984 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-06 08:52:27,986 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-06 08:52:28,215 - INFO - Cleaned Creator column
2025-05-06 08:52:28,216 - INFO - Removed duplicates based on Key column
2025-05-06 08:52:34,375 - INFO - Using 'Description.1' column for text analysis
2025-05-06 08:53:57,969 - INFO - Using 'Created' as creation date
2025-05-06 08:53:57,971 - INFO - Using 'Date of First Response' as response date
2025-05-06 08:53:57,979 - INFO - Using 'Priority' for priority impact calculation
2025-05-06 08:53:57,979 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-06 08:53:58,024 - INFO - Generated client metrics for 8 creators
2025-05-06 08:53:59,244 - WARNING - No Status column found, using default status distribution
2025-05-06 08:53:59,248 - INFO - Analysis completed successfully
2025-05-06 09:18:13,742 - WARNING - Not Found: /favicon.ico
2025-05-06 09:21:57,368 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 09:22:27,458 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\urls.py changed, reloading.
2025-05-06 09:46:01,145 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 10:18:26,971 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 10:20:35,885 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 10:24:42,099 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 10:34:30,413 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-06 10:37:24,311 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-06 10:37:58,073 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\models.py changed, reloading.
2025-05-06 10:38:20,523 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\forms.py changed, reloading.
2025-05-06 10:39:23,489 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\migrations\0001_initial.py changed, reloading.
2025-05-06 10:39:38,362 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\migrations\0001_initial.py changed, reloading.
2025-05-06 10:40:35,978 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-06 11:01:37,989 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:19:09,329 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:20:34,609 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-06 11:20:52,147 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 11:20:52,226 - WARNING - Not Found: /favicon.ico
2025-05-06 11:23:19,634 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:23:19,725 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:23:32,410 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:23:32,427 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:24:01,982 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 11:24:20,489 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 11:24:32,496 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 11:28:59,064 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-06 11:40:13,867 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:40:13,883 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:40:45,799 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:40:45,845 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:41:14,933 - WARNING - Not Found: /client-detail/Lukasz Walczuk
2025-05-06 11:54:58,855 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:54:58,873 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:55:30,536 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:55:30,547 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:55:42,536 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:55:42,551 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:55:56,781 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:55:56,810 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:56:06,858 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:56:07,874 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 11:56:21,925 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:56:22,185 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:56:36,002 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:56:36,249 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:56:46,817 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:56:46,983 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:57:03,090 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:57:03,164 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:57:15,123 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:57:15,147 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:57:43,525 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:57:43,547 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 11:58:32,126 - WARNING - Not Found: /client-detail/Lukasz Walczuk
2025-05-06 12:04:39,191 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:04:39,317 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:05:28,441 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 12:07:20,289 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 12:10:55,119 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:10:55,609 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:15:00,206 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 12:15:34,831 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-06 12:15:34,891 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-06 12:16:54,519 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 12:19:09,757 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-06 12:19:09,950 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-06 12:19:24,876 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 12:19:24,931 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 12:19:41,271 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 12:19:41,272 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 12:20:26,104 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 12:21:43,897 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:21:44,017 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:21:57,219 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:21:57,235 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:22:14,665 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:22:14,673 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:23:05,189 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 12:24:42,278 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 12:27:27,313 - WARNING - Not Found: /client-detail/Bertram Schon
2025-05-06 12:29:26,918 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:29:26,932 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-06 12:35:06,940 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-06 12:35:06,956 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-08 15:25:10,344 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-08 15:35:44,319 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-08 15:36:24,295 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-08 15:40:26,690 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-08 16:04:37,536 - ERROR - Internal Server Error: /client-overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 462, in client_overview
    return render(request, 'analyzer/client_overview.html', context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'div'
2025-05-08 16:04:37,699 - WARNING - Not Found: /favicon.ico
2025-05-08 16:04:40,942 - ERROR - Internal Server Error: /client-overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 462, in client_overview
    return render(request, 'analyzer/client_overview.html', context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'div'
2025-05-08 16:05:13,875 - ERROR - Internal Server Error: /client-overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 462, in client_overview
    return render(request, 'analyzer/client_overview.html', context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'div'
2025-05-08 16:05:14,810 - ERROR - Internal Server Error: /client-overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 462, in client_overview
    return render(request, 'analyzer/client_overview.html', context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'div'
2025-05-08 16:08:51,907 - ERROR - Internal Server Error: /client-overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 462, in client_overview
    return render(request, 'analyzer/client_overview.html', context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 961, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'abs'
2025-05-08 16:10:59,676 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-08 16:11:00,029 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-08 16:11:58,899 - ERROR - Internal Server Error: /client-overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 462, in client_overview
    return render(request, 'analyzer/client_overview.html', context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 1491, in do_with
    nodelist = parser.parse(("endwith",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 961, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'abs'
2025-05-08 16:12:24,614 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-08 16:12:29,960 - ERROR - Internal Server Error: /client-overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endwith'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 462, in client_overview
    return render(request, 'analyzer/client_overview.html', context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 430: 'endwith', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-08 16:12:47,813 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\templatetags\analyzer_filters.py changed, reloading.
2025-05-08 16:14:21,665 - ERROR - Internal Server Error: /client-overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endwith'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 462, in client_overview
    return render(request, 'analyzer/client_overview.html', context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 430: 'endwith', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-08 16:14:35,840 - ERROR - Internal Server Error: /client-overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endwith'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 462, in client_overview
    return render(request, 'analyzer/client_overview.html', context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 430: 'endwith', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-08 16:15:10,742 - ERROR - Internal Server Error: /client-overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 505, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'endwith'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 462, in client_overview
    return render(request, 'analyzer/client_overview.html', context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
        (
    ...<2 lines>...
        )
    )
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 507, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 558, in invalid_block_tag
    raise self.error(
    ...<8 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 430: 'endwith', expected 'elif', 'else' or 'endif'. Did you forget to register or load this tag?
2025-05-08 16:27:21,313 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-08 16:27:21,622 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-08 16:41:16,906 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-08 16:41:17,035 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-08 16:41:43,852 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-08 16:41:43,856 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-08 16:49:32,300 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-08 16:59:20,028 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-08 16:59:26,271 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-08 17:06:23,174 - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-08 23:17:38,154 - WARNING - Not Found: /client-detail/Client A/
2025-05-08 23:43:09,445 - WARNING - Not Found: /client-detail/Client A/
2025-05-08 23:49:40,395 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-08 23:49:40,572 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-10 16:24:25,185 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\urls.py changed, reloading.
2025-05-10 16:25:04,612 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-10 16:25:37,099 - ERROR - Internal Server Error: /overview/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\contrib\auth\decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\APP D\analyzer\views.py", line 204, in overview
    return render(request, 'analyzer/overview.html')
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'client_survey' not found. 'client_survey' is not a valid view function or pattern name.
2025-05-10 16:25:37,178 - WARNING - Not Found: /favicon.ico
2025-05-10 17:00:33,006 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-12 14:50:29,342 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 14:50:49,436 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 14:51:42,452 - ERROR - Error extracting themes with BERTopic: No module named 'bertopic'
2025-05-12 14:56:42,576 - ERROR - Error extracting themes with BERTopic: No module named 'bertopic'
2025-05-12 14:59:14,149 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 14:59:14,398 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 14:59:37,796 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 15:06:03,369 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-12 15:06:04,272 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-12 15:28:09,728 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 15:30:11,757 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 15:30:33,937 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 15:30:57,673 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 15:31:33,496 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 15:32:05,740 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 15:32:30,267 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 15:35:48,686 - WARNING - Not Found: /process_file/37/
2025-05-12 15:35:48,743 - WARNING - Not Found: /favicon.ico
2025-05-12 15:37:26,684 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\2b27126ef97c4c5d9b31a97290654d2c.xlsx
2025-05-12 15:37:27,089 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 15:37:27,094 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 15:37:27,094 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 15:37:27,095 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 15:37:27,223 - INFO - Cleaned Creator column
2025-05-12 15:37:27,225 - INFO - Removed duplicates based on Key column
2025-05-12 15:37:34,497 - INFO - Using 'Description.1' column for text analysis
2025-05-12 15:38:03,476 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\2b27126ef97c4c5d9b31a97290654d2c.xlsx
2025-05-12 15:38:03,807 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 15:38:03,814 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 15:38:03,817 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 15:38:03,821 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 15:38:04,213 - INFO - Cleaned Creator column
2025-05-12 15:38:04,214 - INFO - Removed duplicates based on Key column
2025-05-12 15:38:05,617 - INFO - Using 'Description.1' column for text analysis
2025-05-12 15:38:14,139 - INFO - Using 'Created' as creation date
2025-05-12 15:38:14,140 - INFO - Using 'Date of First Response' as response date
2025-05-12 15:38:14,149 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 15:38:14,150 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 15:38:14,194 - INFO - Generated client metrics for 8 creators
2025-05-12 15:38:14,196 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 15:38:14,700 - WARNING - No Status column found, using default status distribution
2025-05-12 15:38:14,700 - INFO - Analysis completed successfully
2025-05-12 15:38:30,122 - INFO - Using 'Created' as creation date
2025-05-12 15:38:30,123 - INFO - Using 'Date of First Response' as response date
2025-05-12 15:38:30,127 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 15:38:30,128 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 15:38:30,152 - INFO - Generated client metrics for 8 creators
2025-05-12 15:38:30,153 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 15:38:30,586 - WARNING - No Status column found, using default status distribution
2025-05-12 15:38:30,586 - INFO - Analysis completed successfully
2025-05-12 17:22:30,371 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\075d115162cc4513ad587adb008e2b64.xlsx
2025-05-12 17:22:30,676 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:22:30,677 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:22:30,677 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:22:30,678 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:22:30,790 - INFO - Cleaned Creator column
2025-05-12 17:22:30,791 - INFO - Removed duplicates based on Key column
2025-05-12 17:22:33,472 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:22:52,035 - INFO - Using 'Created' as creation date
2025-05-12 17:22:52,037 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:22:52,041 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:22:52,042 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:22:52,080 - INFO - Generated client metrics for 11 creators
2025-05-12 17:22:52,081 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:22:52,534 - WARNING - No Status column found, using default status distribution
2025-05-12 17:22:52,534 - INFO - Analysis completed successfully
2025-05-12 17:22:52,545 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\2b27126ef97c4c5d9b31a97290654d2c.xlsx
2025-05-12 17:22:52,673 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:22:52,674 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:22:52,674 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:22:52,675 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:22:52,782 - INFO - Cleaned Creator column
2025-05-12 17:22:52,783 - INFO - Removed duplicates based on Key column
2025-05-12 17:22:53,881 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:23:11,905 - INFO - Using 'Created' as creation date
2025-05-12 17:23:11,906 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:23:11,910 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:23:11,910 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:23:11,939 - INFO - Generated client metrics for 8 creators
2025-05-12 17:23:11,941 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:23:12,338 - WARNING - No Status column found, using default status distribution
2025-05-12 17:23:12,338 - INFO - Analysis completed successfully
2025-05-12 17:23:12,352 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\91f9b8f933404f5c9a7bd301ce502258.xlsx
2025-05-12 17:23:12,402 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:23:12,403 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:23:12,403 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:23:12,404 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:23:12,506 - INFO - Cleaned Creator column
2025-05-12 17:23:12,506 - INFO - Removed duplicates based on Key column
2025-05-12 17:23:13,522 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:23:28,518 - INFO - Using 'Created' as creation date
2025-05-12 17:23:28,519 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:23:28,522 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:23:28,523 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:23:28,552 - INFO - Generated client metrics for 8 creators
2025-05-12 17:23:28,553 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:23:28,874 - WARNING - No Status column found, using default status distribution
2025-05-12 17:23:28,874 - INFO - Analysis completed successfully
2025-05-12 17:34:35,767 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 17:34:57,805 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 17:36:13,795 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\models.py changed, reloading.
2025-05-12 17:38:50,387 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-12 17:39:08,785 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-12 17:39:31,343 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\075d115162cc4513ad587adb008e2b64.xlsx
2025-05-12 17:39:31,630 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:39:31,631 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:39:31,632 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:39:31,632 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:39:31,746 - INFO - Cleaned Creator column
2025-05-12 17:39:31,747 - INFO - Removed duplicates based on Key column
2025-05-12 17:39:34,354 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:39:52,987 - INFO - Using 'Created' as creation date
2025-05-12 17:39:52,988 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:39:52,992 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:39:52,993 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:39:53,028 - INFO - Generated client metrics for 11 creators
2025-05-12 17:39:53,033 - WARNING - Enhanced theme extraction not available, falling back to legacy methods
2025-05-12 17:39:53,034 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:39:53,454 - WARNING - No Status column found, using default status distribution
2025-05-12 17:39:53,454 - INFO - Analysis completed successfully
2025-05-12 17:39:53,466 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\2b27126ef97c4c5d9b31a97290654d2c.xlsx
2025-05-12 17:39:53,590 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:39:53,591 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:39:53,591 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:39:53,592 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:39:53,700 - INFO - Cleaned Creator column
2025-05-12 17:39:53,700 - INFO - Removed duplicates based on Key column
2025-05-12 17:39:54,822 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:40:12,712 - INFO - Using 'Created' as creation date
2025-05-12 17:40:12,713 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:40:12,717 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:40:12,717 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:40:12,747 - INFO - Generated client metrics for 8 creators
2025-05-12 17:40:12,749 - WARNING - Enhanced theme extraction not available, falling back to legacy methods
2025-05-12 17:40:12,749 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:40:13,136 - WARNING - No Status column found, using default status distribution
2025-05-12 17:40:13,136 - INFO - Analysis completed successfully
2025-05-12 17:40:13,150 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\91f9b8f933404f5c9a7bd301ce502258.xlsx
2025-05-12 17:40:13,190 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:40:13,191 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:40:13,191 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:40:13,192 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:40:13,296 - INFO - Cleaned Creator column
2025-05-12 17:40:13,297 - INFO - Removed duplicates based on Key column
2025-05-12 17:40:14,275 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:40:31,421 - INFO - Using 'Created' as creation date
2025-05-12 17:40:31,422 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:40:31,426 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:40:31,427 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:40:31,459 - INFO - Generated client metrics for 8 creators
2025-05-12 17:40:31,463 - WARNING - Enhanced theme extraction not available, falling back to legacy methods
2025-05-12 17:40:31,463 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:40:31,782 - WARNING - No Status column found, using default status distribution
2025-05-12 17:40:31,783 - INFO - Analysis completed successfully
2025-05-12 17:56:37,658 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\075d115162cc4513ad587adb008e2b64.xlsx
2025-05-12 17:56:37,954 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:56:37,955 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:56:37,955 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:56:37,956 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:56:38,062 - INFO - Cleaned Creator column
2025-05-12 17:56:38,063 - INFO - Removed duplicates based on Key column
2025-05-12 17:56:41,021 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:56:59,339 - INFO - Using 'Created' as creation date
2025-05-12 17:56:59,340 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:56:59,344 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:56:59,345 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:56:59,383 - INFO - Generated client metrics for 11 creators
2025-05-12 17:56:59,394 - WARNING - Enhanced theme extraction not available, falling back to legacy methods
2025-05-12 17:56:59,395 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:56:59,833 - WARNING - No Status column found, using default status distribution
2025-05-12 17:56:59,833 - INFO - Analysis completed successfully
2025-05-12 17:56:59,845 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\2b27126ef97c4c5d9b31a97290654d2c.xlsx
2025-05-12 17:56:59,970 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:56:59,970 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:56:59,971 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:56:59,971 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:57:00,097 - INFO - Cleaned Creator column
2025-05-12 17:57:00,098 - INFO - Removed duplicates based on Key column
2025-05-12 17:57:01,206 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:57:18,909 - INFO - Using 'Created' as creation date
2025-05-12 17:57:18,910 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:57:18,914 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:57:18,914 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:57:18,935 - INFO - Generated client metrics for 8 creators
2025-05-12 17:57:18,937 - WARNING - Enhanced theme extraction not available, falling back to legacy methods
2025-05-12 17:57:18,937 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:57:19,319 - WARNING - No Status column found, using default status distribution
2025-05-12 17:57:19,319 - INFO - Analysis completed successfully
2025-05-12 17:57:19,332 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\91f9b8f933404f5c9a7bd301ce502258.xlsx
2025-05-12 17:57:19,373 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:57:19,373 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:57:19,374 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:57:19,374 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:57:19,479 - INFO - Cleaned Creator column
2025-05-12 17:57:19,480 - INFO - Removed duplicates based on Key column
2025-05-12 17:57:20,560 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:57:35,537 - INFO - Using 'Created' as creation date
2025-05-12 17:57:35,538 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:57:35,543 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:57:35,544 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:57:35,581 - INFO - Generated client metrics for 8 creators
2025-05-12 17:57:35,583 - WARNING - Enhanced theme extraction not available, falling back to legacy methods
2025-05-12 17:57:35,584 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:57:35,913 - WARNING - No Status column found, using default status distribution
2025-05-12 17:57:35,913 - INFO - Analysis completed successfully
2025-05-12 17:59:15,181 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\075d115162cc4513ad587adb008e2b64.xlsx
2025-05-12 17:59:15,462 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:59:15,463 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:59:15,463 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:59:15,464 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:59:15,572 - INFO - Cleaned Creator column
2025-05-12 17:59:15,573 - INFO - Removed duplicates based on Key column
2025-05-12 17:59:18,084 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:59:36,591 - INFO - Using 'Created' as creation date
2025-05-12 17:59:36,592 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:59:36,597 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:59:36,597 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:59:36,628 - INFO - Generated client metrics for 11 creators
2025-05-12 17:59:36,630 - WARNING - Enhanced theme extraction not available, falling back to legacy methods
2025-05-12 17:59:36,630 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:59:37,063 - WARNING - No Status column found, using default status distribution
2025-05-12 17:59:37,064 - INFO - Analysis completed successfully
2025-05-12 17:59:37,077 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\2b27126ef97c4c5d9b31a97290654d2c.xlsx
2025-05-12 17:59:37,200 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:59:37,201 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:59:37,201 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:59:37,202 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:59:37,307 - INFO - Cleaned Creator column
2025-05-12 17:59:37,308 - INFO - Removed duplicates based on Key column
2025-05-12 17:59:38,408 - INFO - Using 'Description.1' column for text analysis
2025-05-12 17:59:56,347 - INFO - Using 'Created' as creation date
2025-05-12 17:59:56,347 - INFO - Using 'Date of First Response' as response date
2025-05-12 17:59:56,351 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 17:59:56,352 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 17:59:56,383 - INFO - Generated client metrics for 8 creators
2025-05-12 17:59:56,385 - WARNING - Enhanced theme extraction not available, falling back to legacy methods
2025-05-12 17:59:56,385 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 17:59:56,768 - WARNING - No Status column found, using default status distribution
2025-05-12 17:59:56,769 - INFO - Analysis completed successfully
2025-05-12 17:59:56,780 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\91f9b8f933404f5c9a7bd301ce502258.xlsx
2025-05-12 17:59:56,819 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 17:59:56,820 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 17:59:56,820 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 17:59:56,821 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 17:59:56,926 - INFO - Cleaned Creator column
2025-05-12 17:59:56,927 - INFO - Removed duplicates based on Key column
2025-05-12 17:59:57,882 - INFO - Using 'Description.1' column for text analysis
2025-05-12 18:00:15,001 - INFO - Using 'Created' as creation date
2025-05-12 18:00:15,001 - INFO - Using 'Date of First Response' as response date
2025-05-12 18:00:15,006 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 18:00:15,007 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 18:00:15,030 - INFO - Generated client metrics for 8 creators
2025-05-12 18:00:15,033 - WARNING - Enhanced theme extraction not available, falling back to legacy methods
2025-05-12 18:00:15,033 - WARNING - BERTopic not available, falling back to TF-IDF approach
2025-05-12 18:00:15,355 - WARNING - No Status column found, using default status distribution
2025-05-12 18:00:15,356 - INFO - Analysis completed successfully
2025-05-12 18:08:10,208 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-12 18:08:28,410 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-12 18:11:14,845 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 18:12:36,072 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 22:43:07,995 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\tickets_prod.xls.xlsx
2025-05-12 22:43:08,220 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 22:43:08,223 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 22:43:08,223 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 22:43:08,224 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 22:43:08,376 - INFO - Cleaned Creator column
2025-05-12 22:43:08,377 - INFO - Removed duplicates based on Key column
2025-05-12 22:43:12,949 - INFO - Using 'Description.1' column for text analysis
2025-05-12 22:44:37,303 - INFO - Using 'Created' as creation date
2025-05-12 22:44:37,304 - INFO - Using 'Date of First Response' as response date
2025-05-12 22:44:37,311 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 22:44:37,313 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 22:44:37,368 - INFO - Generated client metrics for 8 creators
2025-05-12 22:44:37,380 - WARNING - No Status column found, using default status distribution
2025-05-12 22:44:37,380 - INFO - Analysis completed successfully
2025-05-12 23:08:32,641 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-12 23:33:53,003 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:42:53,758 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:43:12,469 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:43:36,711 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:44:02,074 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:44:23,126 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:44:42,183 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:44:59,248 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:45:19,539 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:45:43,996 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:46:01,224 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:46:17,400 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:46:36,537 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:46:52,638 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:47:13,283 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:47:31,639 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-12 23:50:04,477 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls.xlsx
2025-05-12 23:50:05,107 - INFO - Successfully loaded 'general_report' sheet
2025-05-12 23:50:05,111 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-12 23:50:05,111 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-12 23:50:05,114 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-12 23:50:05,117 - INFO - Cleaned Creator column
2025-05-12 23:50:05,118 - INFO - Removed duplicates based on Key column
2025-05-12 23:50:09,497 - INFO - Using 'Description.1' column for text analysis
2025-05-12 23:51:33,987 - INFO - Using 'Created' as creation date
2025-05-12 23:51:33,989 - INFO - Using 'Date of First Response' as response date
2025-05-12 23:51:33,997 - INFO - Using 'Priority' for priority impact calculation
2025-05-12 23:51:33,999 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-12 23:51:34,248 - INFO - Generated client metrics for 11 creators
2025-05-12 23:51:34,249 - WARNING - No Status column found, using default status distribution
2025-05-12 23:51:34,250 - INFO - Analysis completed successfully
2025-05-13 01:02:49,923 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls_WgQ3XQ8.xlsx
2025-05-13 01:02:50,089 - INFO - Successfully loaded 'general_report' sheet
2025-05-13 01:02:50,090 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-13 01:02:50,090 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-13 01:02:50,091 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-13 01:02:50,092 - INFO - Cleaned Creator column
2025-05-13 01:02:50,093 - INFO - Removed duplicates based on Key column
2025-05-13 01:02:50,093 - INFO - Using 'Description.1' column for text analysis
2025-05-13 01:03:06,956 - INFO - Using 'Created' as creation date
2025-05-13 01:03:06,957 - INFO - Using 'Date of First Response' as response date
2025-05-13 01:03:06,961 - INFO - Using 'Priority' for priority impact calculation
2025-05-13 01:03:06,962 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-13 01:03:06,985 - INFO - Generated client metrics for 11 creators
2025-05-13 01:03:06,987 - WARNING - No Status column found, using default status distribution
2025-05-13 01:03:06,987 - INFO - Analysis completed successfully
2025-05-13 01:03:59,745 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Ticket_UAT.xls.xlsx
2025-05-13 01:03:59,911 - INFO - Successfully loaded 'general_report' sheet
2025-05-13 01:03:59,912 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-13 01:03:59,912 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-13 01:03:59,913 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-13 01:03:59,913 - INFO - Cleaned Creator column
2025-05-13 01:03:59,914 - INFO - Removed duplicates based on Key column
2025-05-13 01:03:59,914 - INFO - Using 'Description.1' column for text analysis
2025-05-13 01:04:17,282 - INFO - Using 'Created' as creation date
2025-05-13 01:04:17,283 - INFO - Using 'Date of First Response' as response date
2025-05-13 01:04:17,286 - INFO - Using 'Priority' for priority impact calculation
2025-05-13 01:04:17,287 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-13 01:04:17,318 - INFO - Generated client metrics for 8 creators
2025-05-13 01:04:17,319 - WARNING - No Status column found, using default status distribution
2025-05-13 01:04:17,319 - INFO - Analysis completed successfully
2025-05-15 00:42:17,235 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Ticket_UAT.xls_mbe42p3.xlsx
2025-05-15 00:42:17,719 - INFO - Successfully loaded 'general_report' sheet
2025-05-15 00:42:17,722 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-15 00:42:17,722 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-15 00:42:17,723 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-15 00:42:17,725 - INFO - Cleaned Creator column
2025-05-15 00:42:17,727 - INFO - Removed duplicates based on Key column
2025-05-15 00:42:24,330 - INFO - Using 'Description.1' column for text analysis
2025-05-15 00:44:04,148 - INFO - Using 'Created' as creation date
2025-05-15 00:44:04,149 - INFO - Using 'Date of First Response' as response date
2025-05-15 00:44:04,165 - INFO - Using 'Priority' for priority impact calculation
2025-05-15 00:44:04,170 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-15 00:44:04,306 - INFO - Generated client metrics for 8 creators
2025-05-15 00:44:04,312 - WARNING - No Status column found, using default status distribution
2025-05-15 00:44:04,313 - INFO - Analysis completed successfully
2025-05-17 22:11:13,029 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\tickets_prod.xls_G6I7u5p.xlsx
2025-05-17 22:11:13,357 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:11:13,364 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:11:13,364 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:11:13,367 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:11:13,370 - INFO - Cleaned Creator column
2025-05-17 22:11:13,372 - INFO - Removed duplicates based on Key column
2025-05-17 22:11:20,345 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:12:38,167 - INFO - Using 'Created' as creation date
2025-05-17 22:12:38,169 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:12:38,186 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:12:38,188 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:12:38,270 - INFO - Generated client metrics for 8 creators
2025-05-17 22:12:38,274 - WARNING - No Status column found, using default status distribution
2025-05-17 22:12:38,275 - INFO - Analysis completed successfully
2025-05-17 22:16:11,617 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-17 22:16:52,924 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls_WgQ3XQ8.xlsx
2025-05-17 22:16:53,902 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:16:53,914 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:16:53,917 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:16:53,922 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:16:53,928 - INFO - Cleaned Creator column
2025-05-17 22:16:53,937 - INFO - Removed duplicates based on Key column
2025-05-17 22:16:58,964 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:18:26,267 - INFO - Using 'Created' as creation date
2025-05-17 22:18:26,269 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:18:26,279 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:18:26,281 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:18:26,579 - INFO - Generated client metrics for 11 creators
2025-05-17 22:18:26,582 - WARNING - No Status column found, using default status distribution
2025-05-17 22:18:26,582 - INFO - Analysis completed successfully
2025-05-17 22:20:23,201 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-17 22:20:42,232 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls_WgQ3XQ8.xlsx
2025-05-17 22:20:43,633 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:20:43,641 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:20:43,649 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:20:43,653 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:20:43,658 - INFO - Cleaned Creator column
2025-05-17 22:20:43,663 - INFO - Removed duplicates based on Key column
2025-05-17 22:20:48,544 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:20:52,108 - ERROR - Sentiment error for text 'exof processed one client paym...': name 'urgency_terms' is not defined
2025-05-17 22:20:52,433 - ERROR - Sentiment error for text 'concerning dashboard alert val...': name 'urgency_terms' is not defined
2025-05-17 22:20:52,749 - ERROR - Sentiment error for text 'hello emna need one new userpr...': name 'urgency_terms' is not defined
2025-05-17 22:20:53,088 - ERROR - Sentiment error for text 'hello come attention three int...': name 'urgency_terms' is not defined
2025-05-17 22:20:53,321 - ERROR - Sentiment error for text 'hello emna colleague mc via da...': name 'urgency_terms' is not defined
2025-05-17 22:20:53,589 - ERROR - Sentiment error for text 'hi due mapping error received ...': name 'urgency_terms' is not defined
2025-05-17 22:20:53,747 - ERROR - Sentiment error for text 'hello january redm event updat...': name 'urgency_terms' is not defined
2025-05-17 22:20:53,954 - ERROR - Sentiment error for text 'hi emna colleague today reciev...': name 'urgency_terms' is not defined
2025-05-17 22:20:54,123 - ERROR - Sentiment error for text 'prox set according seev attach...': name 'urgency_terms' is not defined
2025-05-17 22:20:54,425 - ERROR - Sentiment error for text 'shds elig po blocked account s...': name 'urgency_terms' is not defined
2025-05-17 22:20:54,650 - ERROR - Sentiment error for text 'hello emnacolleagues need crea...': name 'urgency_terms' is not defined
2025-05-17 22:20:55,028 - ERROR - Sentiment error for text 'isin go financial instrument c...': name 'urgency_terms' is not defined
2025-05-17 22:20:55,159 - ERROR - Sentiment error for text 'hi wanted check something mega...': name 'urgency_terms' is not defined
2025-05-17 22:20:55,442 - ERROR - Sentiment error for text 'hi emna colleague thursday goi...': name 'urgency_terms' is not defined
2025-05-17 22:20:55,688 - ERROR - Sentiment error for text 'hello discussed let please add...': name 'urgency_terms' is not defined
2025-05-17 22:20:55,864 - ERROR - Sentiment error for text 'dear colleague outage weekend ...': name 'urgency_terms' is not defined
2025-05-17 22:20:56,195 - ERROR - Sentiment error for text 'tend tried send mt mt newm not...': name 'urgency_terms' is not defined
2025-05-17 22:20:56,973 - ERROR - Sentiment error for text 'test ticket consol interface i...': name 'urgency_terms' is not defined
2025-05-17 22:20:57,110 - ERROR - Sentiment error for text 'hello dear gerhard login mc pr...': name 'urgency_terms' is not defined
2025-05-17 22:20:57,306 - ERROR - Sentiment error for text 'prox noticed input client mark...': name 'urgency_terms' is not defined
2025-05-17 22:20:57,733 - ERROR - Sentiment error for text 'yesterday received first seev ...': name 'urgency_terms' is not defined
2025-05-17 22:20:58,204 - ERROR - Sentiment error for text 'hello emna colleague isins nee...': name 'urgency_terms' is not defined
2025-05-17 22:20:58,500 - ERROR - Sentiment error for text 'prox received seev moved statu...': name 'urgency_terms' is not defined
2025-05-17 22:20:58,699 - ERROR - Sentiment error for text 'hello please check loan schedu...': name 'urgency_terms' is not defined
2025-05-17 22:20:58,818 - ERROR - Sentiment error for text 'hello unfortunately possible u...': name 'urgency_terms' is not defined
2025-05-17 22:20:58,992 - ERROR - Sentiment error for text 'hello please check loan schedu...': name 'urgency_terms' is not defined
2025-05-17 22:20:59,101 - ERROR - Sentiment error for text 'hello january received three m...': name 'urgency_terms' is not defined
2025-05-17 22:20:59,462 - ERROR - Sentiment error for text 'hi emna noticed client based n...': name 'urgency_terms' is not defined
2025-05-17 22:20:59,635 - ERROR - Sentiment error for text 'several dashboard alert ignore...': name 'urgency_terms' is not defined
2025-05-17 22:20:59,821 - ERROR - Sentiment error for text 'please note several meet event...': name 'urgency_terms' is not defined
2025-05-17 22:20:59,931 - ERROR - Sentiment error for text 'hi issue oekb still appears pr...': name 'urgency_terms' is not defined
2025-05-17 22:21:00,174 - ERROR - Sentiment error for text 'issue seev sent def wmdeff tod...': name 'urgency_terms' is not defined
2025-05-17 22:21:00,261 - ERROR - Sentiment error for text 'issue reported oekb retested s...': name 'urgency_terms' is not defined
2025-05-17 22:21:00,417 - ERROR - Sentiment error for text 'prox want send seev repl autho...': name 'urgency_terms' is not defined
2025-05-17 22:21:00,493 - ERROR - Sentiment error for text 'dear colleague noticed pending...': name 'urgency_terms' is not defined
2025-05-17 22:21:00,628 - ERROR - Sentiment error for text 'today found lei mandatory send...': name 'urgency_terms' is not defined
2025-05-17 22:21:00,820 - ERROR - Sentiment error for text 'hello correct payment date int...': name 'urgency_terms' is not defined
2025-05-17 22:21:00,938 - ERROR - Sentiment error for text 'issue detected reported oekb w...': name 'urgency_terms' is not defined
2025-05-17 22:21:01,104 - ERROR - Sentiment error for text 'noticed announcement date rece...': name 'urgency_terms' is not defined
2025-05-17 22:21:01,242 - ERROR - Sentiment error for text 'hello update possible abovemen...': name 'urgency_terms' is not defined
2025-05-17 22:21:01,453 - ERROR - Sentiment error for text 'issue three isins pcal event c...': name 'urgency_terms' is not defined
2025-05-17 22:21:01,696 - ERROR - Sentiment error for text 'bput isin cash instruction shs...': name 'urgency_terms' is not defined
2025-05-17 22:21:01,904 - ERROR - Sentiment error for text 'isin usm main ref dvca created...': name 'urgency_terms' is not defined
2025-05-17 22:21:02,321 - ERROR - Sentiment error for text 'trying remove invalid disclosu...': name 'urgency_terms' is not defined
2025-05-17 22:21:02,584 - ERROR - Sentiment error for text 'tried create entry ustax data ...': name 'urgency_terms' is not defined
2025-05-17 22:21:02,727 - ERROR - Sentiment error for text 'could please check send disclo...': name 'urgency_terms' is not defined
2025-05-17 22:21:02,939 - ERROR - Sentiment error for text 'dear pls let analyse starting ...': name 'urgency_terms' is not defined
2025-05-17 22:21:03,184 - ERROR - Sentiment error for text 'received attached mt message f...': name 'urgency_terms' is not defined
2025-05-17 22:21:03,406 - ERROR - Sentiment error for text 'trying ignore item update frac...': name 'urgency_terms' is not defined
2025-05-17 22:21:03,710 - ERROR - Sentiment error for text 'dear colleague noticed lukas m...': name 'urgency_terms' is not defined
2025-05-17 22:21:03,981 - ERROR - Sentiment error for text 'dear coleagues pls help settin...': name 'urgency_terms' is not defined
2025-05-17 22:21:04,212 - ERROR - Sentiment error for text 'dvop mp validated still status...': name 'urgency_terms' is not defined
2025-05-17 22:21:04,412 - ERROR - Sentiment error for text 'hello recieved attached notifi...': name 'urgency_terms' is not defined
2025-05-17 22:21:04,630 - ERROR - Sentiment error for text 'could please urgently check at...': name 'urgency_terms' is not defined
2025-05-17 22:21:04,856 - ERROR - Sentiment error for text 'dvop update event data add fin...': name 'urgency_terms' is not defined
2025-05-17 22:21:05,011 - ERROR - Sentiment error for text 'hello abovementioned pcal even...': name 'urgency_terms' is not defined
2025-05-17 22:21:05,235 - ERROR - Sentiment error for text 'hello stock exchange claim abo...': name 'urgency_terms' is not defined
2025-05-17 22:21:05,418 - ERROR - Sentiment error for text 'dvse set including tax movemen...': name 'urgency_terms' is not defined
2025-05-17 22:21:05,741 - ERROR - Sentiment error for text 'dvse cash client payment autom...': name 'urgency_terms' is not defined
2025-05-17 22:21:05,991 - ERROR - Sentiment error for text 'hi sometimes face situation pr...': name 'urgency_terms' is not defined
2025-05-17 22:21:06,145 - ERROR - Sentiment error for text 'hello dvca event mentioned tak...': name 'urgency_terms' is not defined
2025-05-17 22:21:06,271 - ERROR - Sentiment error for text 'dashboard alert redm loan calc...': name 'urgency_terms' is not defined
2025-05-17 22:21:06,584 - ERROR - Sentiment error for text 'could please check feasibility...': name 'urgency_terms' is not defined
2025-05-17 22:21:06,739 - ERROR - Sentiment error for text 'could please check following e...': name 'urgency_terms' is not defined
2025-05-17 22:21:06,923 - ERROR - Sentiment error for text 'today received seev repl custo...': name 'urgency_terms' is not defined
2025-05-17 22:21:07,093 - ERROR - Sentiment error for text 'prox issrddlnforvtng received ...': name 'urgency_terms' is not defined
2025-05-17 22:21:07,298 - ERROR - Sentiment error for text 'prox sent seev client containi...': name 'urgency_terms' is not defined
2025-05-17 22:21:07,461 - ERROR - Sentiment error for text 'prox set according incoming se...': name 'urgency_terms' is not defined
2025-05-17 22:21:07,639 - ERROR - Sentiment error for text 'hello currency conversion dvca...': name 'urgency_terms' is not defined
2025-05-17 22:21:08,071 - ERROR - Sentiment error for text 'hi niki omar today swift lot s...': name 'urgency_terms' is not defined
2025-05-17 22:21:08,184 - ERROR - Sentiment error for text 'hello isins listed target paym...': name 'urgency_terms' is not defined
2025-05-17 22:21:08,292 - ERROR - Sentiment error for text 'hello two redm event listed bl...': name 'urgency_terms' is not defined
2025-05-17 22:21:08,618 - ERROR - Sentiment error for text 'hello noticed migrated event s...': name 'urgency_terms' is not defined
2025-05-17 22:21:08,778 - ERROR - Sentiment error for text 'hello updated price mentioned ...': name 'urgency_terms' is not defined
2025-05-17 22:21:09,126 - ERROR - Sentiment error for text 'hello although market payment ...': name 'urgency_terms' is not defined
2025-05-17 22:21:09,334 - ERROR - Sentiment error for text 'shds crosschecked via set reli...': name 'urgency_terms' is not defined
2025-05-17 22:21:09,606 - ERROR - Sentiment error for text 'hello menu item repair receive...': name 'urgency_terms' is not defined
2025-05-17 22:21:09,778 - ERROR - Sentiment error for text 'hello event mentioned updated ...': name 'urgency_terms' is not defined
2025-05-17 22:21:10,071 - ERROR - Sentiment error for text 'hi exof event activated fact s...': name 'urgency_terms' is not defined
2025-05-17 22:21:10,279 - ERROR - Sentiment error for text 'exof niki noticed mt rmdr toda...': name 'urgency_terms' is not defined
2025-05-17 22:21:10,624 - ERROR - Sentiment error for text 'chan today found client reques...': name 'urgency_terms' is not defined
2025-05-17 22:21:10,840 - ERROR - Sentiment error for text 'hello update two isins listed ...': name 'urgency_terms' is not defined
2025-05-17 22:21:11,050 - ERROR - Sentiment error for text 'noticed event already closed l...': name 'urgency_terms' is not defined
2025-05-17 22:21:11,347 - ERROR - Sentiment error for text 'hello recieved following alert...': name 'urgency_terms' is not defined
2025-05-17 22:21:11,554 - ERROR - Sentiment error for text 'hello three mcal event listed ...': name 'urgency_terms' is not defined
2025-05-17 22:21:11,700 - ERROR - Sentiment error for text 'dashboard alert tax slas valid...': name 'urgency_terms' is not defined
2025-05-17 22:21:11,895 - ERROR - Sentiment error for text 'following ticket oekb megacor ...': name 'urgency_terms' is not defined
2025-05-17 22:21:12,110 - ERROR - Sentiment error for text 'mrgrevents pay date confirmed ...': name 'urgency_terms' is not defined
2025-05-17 22:21:12,328 - ERROR - Sentiment error for text 'hi emna job client notificatio...': name 'urgency_terms' is not defined
2025-05-17 22:21:12,673 - ERROR - Sentiment error for text 'hello intr event intr correctl...': name 'urgency_terms' is not defined
2025-05-17 22:21:12,927 - ERROR - Sentiment error for text 'hello emna please check alert ...': name 'urgency_terms' is not defined
2025-05-17 22:21:13,236 - ERROR - Sentiment error for text 'meet issue oekb first repl rec...': name 'urgency_terms' is not defined
2025-05-17 22:21:13,580 - ERROR - Sentiment error for text 'dear emna file generated name ...': name 'urgency_terms' is not defined
2025-05-17 22:21:13,748 - ERROR - Sentiment error for text 'hello function ignore button l...': name 'urgency_terms' is not defined
2025-05-17 22:21:14,124 - ERROR - Sentiment error for text 'exof set manually mt newm sent...': name 'urgency_terms' is not defined
2025-05-17 22:21:14,516 - ERROR - Sentiment error for text 'splr set manually yesterday mt...': name 'urgency_terms' is not defined
2025-05-17 22:21:14,863 - ERROR - Sentiment error for text 'testing import seev including ...': name 'urgency_terms' is not defined
2025-05-17 22:21:15,076 - ERROR - Sentiment error for text 'could please check add followi...': name 'urgency_terms' is not defined
2025-05-17 22:21:15,435 - ERROR - Sentiment error for text 'exof updated saved event aroun...': name 'urgency_terms' is not defined
2025-05-17 22:21:15,539 - ERROR - Sentiment error for text 'dashboard alert failed receive...': name 'urgency_terms' is not defined
2025-05-17 22:21:15,671 - ERROR - Sentiment error for text 'attached mt rmdr received arou...': name 'urgency_terms' is not defined
2025-05-17 22:21:15,977 - ERROR - Sentiment error for text 'redm atak hello redm event red...': name 'urgency_terms' is not defined
2025-05-17 22:21:16,187 - ERROR - Sentiment error for text 'dashboard alert loan calc erro...': name 'urgency_terms' is not defined
2025-05-17 22:21:16,482 - ERROR - Sentiment error for text 'problem ticket oekb still exis...': name 'urgency_terms' is not defined
2025-05-17 22:21:16,610 - ERROR - Sentiment error for text 'hello dashbort alert failed bo...': name 'urgency_terms' is not defined
2025-05-17 22:21:16,648 - ERROR - Sentiment error for text 'hello update possible intr eve...': name 'urgency_terms' is not defined
2025-05-17 22:21:16,696 - ERROR - Sentiment error for text 'hello look isin menu item vali...': name 'urgency_terms' is not defined
2025-05-17 22:21:16,794 - ERROR - Sentiment error for text 'hello intr pcal event isin dec...': name 'urgency_terms' is not defined
2025-05-17 22:21:17,235 - ERROR - Sentiment error for text 'xmet received message event ne...': name 'urgency_terms' is not defined
2025-05-17 22:21:17,416 - ERROR - Sentiment error for text 'hello entered saved tax rate e...': name 'urgency_terms' is not defined
2025-05-17 22:21:17,639 - ERROR - Sentiment error for text 'notice receive feed wm several...': name 'urgency_terms' is not defined
2025-05-17 22:21:17,973 - ERROR - Sentiment error for text 'following dashboard alert dele...': name 'urgency_terms' is not defined
2025-05-17 22:21:18,187 - ERROR - Sentiment error for text 'prox set according incoming se...': name 'urgency_terms' is not defined
2025-05-17 22:21:18,574 - ERROR - Sentiment error for text 'valid dashbaord alert validate...': name 'urgency_terms' is not defined
2025-05-17 22:21:18,847 - ERROR - Sentiment error for text 'prox event updated validated h...': name 'urgency_terms' is not defined
2025-05-17 22:21:19,042 - ERROR - Sentiment error for text 'checking mt drop folder found ...': name 'urgency_terms' is not defined
2025-05-17 22:21:19,182 - ERROR - Sentiment error for text 'hello found isin atalc dashboa...': name 'urgency_terms' is not defined
2025-05-17 22:21:19,362 - ERROR - Sentiment error for text 'hello ignore incoming message ...': name 'urgency_terms' is not defined
2025-05-17 22:21:19,641 - ERROR - Sentiment error for text 'hello found mb flow id related...': name 'urgency_terms' is not defined
2025-05-17 22:21:19,816 - ERROR - Sentiment error for text 'trying investigate msg megacor...': name 'urgency_terms' is not defined
2025-05-17 22:21:20,014 - ERROR - Sentiment error for text 'othr set via super event mand ...': name 'urgency_terms' is not defined
2025-05-17 22:21:20,120 - ERROR - Sentiment error for text 'problem dashboard alert valida...': name 'urgency_terms' is not defined
2025-05-17 22:21:20,160 - ERROR - Sentiment error for text 'hello update possible abovemen...': name 'urgency_terms' is not defined
2025-05-17 22:21:20,347 - ERROR - Sentiment error for text 'hello entitlement calculated c...': name 'urgency_terms' is not defined
2025-05-17 22:21:20,572 - ERROR - Sentiment error for text 'today noticed new confirm aler...': name 'urgency_terms' is not defined
2025-05-17 22:21:20,857 - ERROR - Sentiment error for text 'testing seevmessages wm turned...': name 'urgency_terms' is not defined
2025-05-17 22:21:21,235 - ERROR - Sentiment error for text 'client email adress purpose ac...': name 'urgency_terms' is not defined
2025-05-17 22:21:21,520 - ERROR - Sentiment error for text 'dashboard alert entitlement ca...': name 'urgency_terms' is not defined
2025-05-17 22:21:21,901 - ERROR - Sentiment error for text 'today received mt newm xmet ev...': name 'urgency_terms' is not defined
2025-05-17 22:21:22,307 - ERROR - Sentiment error for text 'today attached mt newm receive...': name 'urgency_terms' is not defined
2025-05-17 22:21:22,533 - ERROR - Sentiment error for text 'received mt failed following e...': name 'urgency_terms' is not defined
2025-05-17 22:21:22,789 - ERROR - Sentiment error for text 'retesting oekb splr qas notice...': name 'urgency_terms' is not defined
2025-05-17 22:21:22,880 - ERROR - Sentiment error for text 'hello try cross check several ...': name 'urgency_terms' is not defined
2025-05-17 22:21:23,046 - ERROR - Sentiment error for text 'shds received disclosure reque...': name 'urgency_terms' is not defined
2025-05-17 22:21:23,152 - ERROR - Sentiment error for text 'dflt migrated event imagepng t...': name 'urgency_terms' is not defined
2025-05-17 22:21:23,349 - ERROR - Sentiment error for text 'xmet received mt repl attached...': name 'urgency_terms' is not defined
2025-05-17 22:21:23,443 - ERROR - Sentiment error for text 'event payment date today still...': name 'urgency_terms' is not defined
2025-05-17 22:21:23,664 - ERROR - Sentiment error for text 'dvop set creating custodian sl...': name 'urgency_terms' is not defined
2025-05-17 22:21:23,794 - ERROR - Sentiment error for text 'noticed event type tndp wrtc a...': name 'urgency_terms' is not defined
2025-05-17 22:21:24,019 - ERROR - Sentiment error for text 'today received data feed flowi...': name 'urgency_terms' is not defined
2025-05-17 22:21:24,221 - ERROR - Sentiment error for text 'bid updated event today adding...': name 'urgency_terms' is not defined
2025-05-17 22:21:24,414 - ERROR - Sentiment error for text 'following isins early redempti...': name 'urgency_terms' is not defined
2025-05-17 22:21:24,661 - ERROR - Sentiment error for text 'exof checking mt attached secu...': name 'urgency_terms' is not defined
2025-05-17 22:21:24,903 - ERROR - Sentiment error for text 'isins target payment date publ...': name 'urgency_terms' is not defined
2025-05-17 22:21:25,100 - ERROR - Sentiment error for text 'hello emna already email discu...': name 'urgency_terms' is not defined
2025-05-17 22:21:25,603 - ERROR - Sentiment error for text 'hello menu item repair receive...': name 'urgency_terms' is not defined
2025-05-17 22:21:25,802 - ERROR - Sentiment error for text 'repair received feed following...': name 'urgency_terms' is not defined
2025-05-17 22:21:25,927 - ERROR - Sentiment error for text 'bid set today validated need n...': name 'urgency_terms' is not defined
2025-05-17 22:21:26,031 - ERROR - Sentiment error for text 'hi swift mt pred event field n...': name 'urgency_terms' is not defined
2025-05-17 22:21:26,209 - ERROR - Sentiment error for text 'today noticed button edit tole...': name 'urgency_terms' is not defined
2025-05-17 22:21:26,339 - ERROR - Sentiment error for text 'still issue list trying ignore...': name 'urgency_terms' is not defined
2025-05-17 22:21:26,481 - ERROR - Sentiment error for text 'hello noticed internal comment...': name 'urgency_terms' is not defined
2025-05-17 22:21:26,574 - ERROR - Sentiment error for text 'hello intr event per october i...': name 'urgency_terms' is not defined
2025-05-17 22:21:26,725 - ERROR - Sentiment error for text 'mt canc received yesterday ima...': name 'urgency_terms' is not defined
2025-05-17 22:21:26,855 - ERROR - Sentiment error for text 'good morning dear today saw al...': name 'urgency_terms' is not defined
2025-05-17 22:21:26,943 - ERROR - Sentiment error for text 'hello received last two day em...': name 'urgency_terms' is not defined
2025-05-17 22:21:27,167 - ERROR - Sentiment error for text 'hello generated schedule creat...': name 'urgency_terms' is not defined
2025-05-17 22:21:27,295 - ERROR - Sentiment error for text 'could please check possible ig...': name 'urgency_terms' is not defined
2025-05-17 22:21:27,470 - ERROR - Sentiment error for text 'bid set via super event volu i...': name 'urgency_terms' is not defined
2025-05-17 22:21:27,670 - ERROR - Sentiment error for text 'hello two intr event interest ...': name 'urgency_terms' is not defined
2025-05-17 22:21:27,954 - ERROR - Sentiment error for text 'hi discussed within oekb field...': name 'urgency_terms' is not defined
2025-05-17 22:21:28,218 - ERROR - Sentiment error for text 'dvca exercise dividend right s...': name 'urgency_terms' is not defined
2025-05-17 22:21:28,424 - ERROR - Sentiment error for text 'hello dvca dividend processed ...': name 'urgency_terms' is not defined
2025-05-17 22:21:28,782 - ERROR - Sentiment error for text 'exof yesterday instructed slle...': name 'urgency_terms' is not defined
2025-05-17 22:21:29,141 - ERROR - Sentiment error for text 'cert could please urgently che...': name 'urgency_terms' is not defined
2025-05-17 22:21:29,447 - ERROR - Sentiment error for text 'xmet received newm repl within...': name 'urgency_terms' is not defined
2025-05-17 22:21:29,804 - ERROR - Sentiment error for text 'exof secu option set miex milt...': name 'urgency_terms' is not defined
2025-05-17 22:21:30,160 - ERROR - Sentiment error for text 'exofexof received huge amount ...': name 'urgency_terms' is not defined
2025-05-17 22:21:30,428 - ERROR - Sentiment error for text 'today client sent two instruct...': name 'urgency_terms' is not defined
2025-05-17 22:21:30,783 - ERROR - Sentiment error for text 'could please check following r...': name 'urgency_terms' is not defined
2025-05-17 22:21:30,989 - ERROR - Sentiment error for text 'exof issue reported oekb flow ...': name 'urgency_terms' is not defined
2025-05-17 22:21:31,324 - ERROR - Sentiment error for text 'cert two new client position x...': name 'urgency_terms' is not defined
2025-05-17 22:21:31,574 - ERROR - Sentiment error for text 'coincidentally found nd rmdr m...': name 'urgency_terms' is not defined
2025-05-17 22:21:31,804 - ERROR - Sentiment error for text 'process mrgr event friday mark...': name 'urgency_terms' is not defined
2025-05-17 22:21:31,965 - ERROR - Sentiment error for text 'event payment day today ca sta...': name 'urgency_terms' is not defined
2025-05-17 22:21:32,037 - ERROR - Sentiment error for text 'dvca hello unfortunately possi...': name 'urgency_terms' is not defined
2025-05-17 22:21:32,592 - ERROR - Sentiment error for text 'u hello isin philip morris con...': name 'urgency_terms' is not defined
2025-05-17 22:21:32,889 - ERROR - Sentiment error for text 'xmet received mt repl custodia...': name 'urgency_terms' is not defined
2025-05-17 22:21:33,142 - ERROR - Sentiment error for text 'dear received first pending tr...': name 'urgency_terms' is not defined
2025-05-17 22:21:33,374 - ERROR - Sentiment error for text 'menu event u tax reporting go ...': name 'urgency_terms' is not defined
2025-05-17 22:21:33,856 - ERROR - Sentiment error for text 'intr hello event breakdown ins...': name 'urgency_terms' is not defined
2025-05-17 22:21:34,123 - ERROR - Sentiment error for text 'payment foreign currency fx eu...': name 'urgency_terms' is not defined
2025-05-17 22:21:34,223 - ERROR - Sentiment error for text 'exof issue yesterday see oekb ...': name 'urgency_terms' is not defined
2025-05-17 22:21:34,323 - ERROR - Sentiment error for text 'could please provide u ignoreb...': name 'urgency_terms' is not defined
2025-05-17 22:21:34,437 - ERROR - Sentiment error for text 'xmet checked internal swift dr...': name 'urgency_terms' is not defined
2025-05-17 22:21:34,525 - ERROR - Sentiment error for text 'good morning dear pending trad...': name 'urgency_terms' is not defined
2025-05-17 22:21:34,701 - ERROR - Sentiment error for text 'exof received several mt instr...': name 'urgency_terms' is not defined
2025-05-17 22:21:34,841 - ERROR - Sentiment error for text 'exof conflicting update rmdr w...': name 'urgency_terms' is not defined
2025-05-17 22:21:35,298 - ERROR - Sentiment error for text 'good morning dear colleague up...': name 'urgency_terms' is not defined
2025-05-17 22:21:35,659 - ERROR - Sentiment error for text 'prox set according received se...': name 'urgency_terms' is not defined
2025-05-17 22:21:35,770 - ERROR - Sentiment error for text 'dear checking saw batch proces...': name 'urgency_terms' is not defined
2025-05-17 22:21:36,192 - ERROR - Sentiment error for text 'exof event received two mt sec...': name 'urgency_terms' is not defined
2025-05-17 22:21:36,391 - ERROR - Sentiment error for text 'exof please urgently check two...': name 'urgency_terms' is not defined
2025-05-17 22:21:36,595 - ERROR - Sentiment error for text 'dear expecting f value cashmov...': name 'urgency_terms' is not defined
2025-05-17 22:21:36,951 - ERROR - Sentiment error for text 'exof checking mt flowouts noti...': name 'urgency_terms' is not defined
2025-05-17 22:21:37,309 - ERROR - Sentiment error for text 'hello intr event price calcula...': name 'urgency_terms' is not defined
2025-05-17 22:21:37,517 - ERROR - Sentiment error for text 'intr hello interest rate calcu...': name 'urgency_terms' is not defined
2025-05-17 22:21:37,816 - ERROR - Sentiment error for text 'dscl conflicting update valida...': name 'urgency_terms' is not defined
2025-05-17 22:21:37,988 - ERROR - Sentiment error for text 'rhdi updated event following a...': name 'urgency_terms' is not defined
2025-05-17 22:21:38,159 - ERROR - Sentiment error for text 'exof updated event saved after...': name 'urgency_terms' is not defined
2025-05-17 22:21:38,292 - ERROR - Sentiment error for text 'good morning today pending tra...': name 'urgency_terms' is not defined
2025-05-17 22:21:38,393 - ERROR - Sentiment error for text 'good morning dear info today p...': name 'urgency_terms' is not defined
2025-05-17 22:21:38,663 - ERROR - Sentiment error for text 'splr processed new workaround ...': name 'urgency_terms' is not defined
2025-05-17 22:21:38,798 - ERROR - Sentiment error for text 'mrgr conflicting update wanted...': name 'urgency_terms' is not defined
2025-05-17 22:21:38,990 - ERROR - Sentiment error for text 'hi ive imported together oekb ...': name 'urgency_terms' is not defined
2025-05-17 22:21:39,655 - ERROR - Sentiment error for text 'montag um wurde festgestellt d...': name 'urgency_terms' is not defined
2025-05-17 22:21:40,141 - ERROR - Sentiment error for text 'hi oussema today flow stukced ...': name 'urgency_terms' is not defined
2025-05-17 22:21:40,251 - ERROR - Sentiment error for text 'omet conflicting update wanted...': name 'urgency_terms' is not defined
2025-05-17 22:21:40,360 - ERROR - Sentiment error for text 'hello colleague noticed batch ...': name 'urgency_terms' is not defined
2025-05-17 22:21:40,882 - ERROR - Sentiment error for text 'dear ive found flow status gen...': name 'urgency_terms' is not defined
2025-05-17 22:21:41,079 - ERROR - Sentiment error for text 'good morning dear currently po...': name 'urgency_terms' is not defined
2025-05-17 22:21:41,286 - ERROR - Sentiment error for text 'exof exof set event today mt s...': name 'urgency_terms' is not defined
2025-05-17 22:21:41,435 - ERROR - Sentiment error for text 'hello atasgh dividend processe...': name 'urgency_terms' is not defined
2025-05-17 22:21:41,589 - ERROR - Sentiment error for text 'dear missing mt custodian star...': name 'urgency_terms' is not defined
2025-05-17 22:21:41,709 - ERROR - Sentiment error for text 'hello attached list payment wi...': name 'urgency_terms' is not defined
2025-05-17 22:21:41,891 - ERROR - Sentiment error for text 'hello today working togheter t...': name 'urgency_terms' is not defined
2025-05-17 22:21:41,984 - ERROR - Sentiment error for text 'today received security event ...': name 'urgency_terms' is not defined
2025-05-17 22:21:42,044 - ERROR - Sentiment error for text 'dear colleague pending trade b...': name 'urgency_terms' is not defined
2025-05-17 22:21:42,172 - ERROR - Sentiment error for text 'hello ca status correct event ...': name 'urgency_terms' is not defined
2025-05-17 22:21:42,372 - ERROR - Sentiment error for text 'good morning info prod ist ava...': name 'urgency_terms' is not defined
2025-05-17 22:21:42,855 - ERROR - Sentiment error for text 'dvca nl hello discussed oussem...': name 'urgency_terms' is not defined
2025-05-17 22:21:43,090 - ERROR - Sentiment error for text 'hi received mt error tradable ...': name 'urgency_terms' is not defined
2025-05-17 22:21:43,439 - ERROR - Sentiment error for text 'splr set manually yesterday mt...': name 'urgency_terms' is not defined
2025-05-17 22:21:43,511 - ERROR - Sentiment error for text 'please check confirmedeventscs...': name 'urgency_terms' is not defined
2025-05-17 22:21:43,770 - ERROR - Sentiment error for text 'received seev message found li...': name 'urgency_terms' is not defined
2025-05-17 22:21:44,080 - ERROR - Sentiment error for text 'redm reverse mp processed twic...': name 'urgency_terms' is not defined
2025-05-17 22:21:44,263 - ERROR - Sentiment error for text 'could please check following t...': name 'urgency_terms' is not defined
2025-05-17 22:21:44,471 - ERROR - Sentiment error for text 'today lot event ca status exec...': name 'urgency_terms' is not defined
2025-05-17 22:21:44,655 - ERROR - Sentiment error for text 'xmet event updated friday succ...': name 'urgency_terms' is not defined
2025-05-17 22:21:44,837 - ERROR - Sentiment error for text 'drip conflicting update wanted...': name 'urgency_terms' is not defined
2025-05-17 22:21:45,024 - ERROR - Sentiment error for text 'mt received unknown secu isin ...': name 'urgency_terms' is not defined
2025-05-17 22:21:45,190 - ERROR - Sentiment error for text 'drip must update event adding ...': name 'urgency_terms' is not defined
2025-05-17 22:21:45,427 - ERROR - Sentiment error for text 'hello please check mtrepe outg...': name 'urgency_terms' is not defined
2025-05-17 22:21:45,553 - ERROR - Sentiment error for text 'checking repair received feedl...': name 'urgency_terms' is not defined
2025-05-17 22:21:45,650 - ERROR - Sentiment error for text 'hello team deinstall firefox o...': name 'urgency_terms' is not defined
2025-05-17 22:21:45,830 - ERROR - Sentiment error for text 'received several mt repl mass ...': name 'urgency_terms' is not defined
2025-05-17 22:21:45,963 - ERROR - Sentiment error for text 'chan field event purpose gener...': name 'urgency_terms' is not defined
2025-05-17 22:21:46,080 - ERROR - Sentiment error for text 'cert mt attached received cont...': name 'urgency_terms' is not defined
2025-05-17 22:21:46,270 - ERROR - Sentiment error for text 'exof market payment received s...': name 'urgency_terms' is not defined
2025-05-17 22:21:46,550 - ERROR - Sentiment error for text 'prox received client complaint...': name 'urgency_terms' is not defined
2025-05-17 22:21:46,636 - ERROR - Sentiment error for text 'dscl update possible due block...': name 'urgency_terms' is not defined
2025-05-17 22:21:46,795 - ERROR - Sentiment error for text 'noticed sometimes input isin i...': name 'urgency_terms' is not defined
2025-05-17 22:21:47,014 - ERROR - Sentiment error for text 'chan conflicting update wanted...': name 'urgency_terms' is not defined
2025-05-17 22:21:47,170 - ERROR - Sentiment error for text 'hello received following email...': name 'urgency_terms' is not defined
2025-05-17 22:21:47,523 - ERROR - Sentiment error for text 'coincidentelly found mt repe r...': name 'urgency_terms' is not defined
2025-05-17 22:21:47,743 - ERROR - Sentiment error for text 'chan set chan term event provi...': name 'urgency_terms' is not defined
2025-05-17 22:21:47,986 - ERROR - Sentiment error for text 'drip received cash payment es ...': name 'urgency_terms' is not defined
2025-05-17 22:21:48,203 - ERROR - Sentiment error for text 'drip received two mt message e...': name 'urgency_terms' is not defined
2025-05-17 22:21:48,388 - ERROR - Sentiment error for text 'meet meet update possible due ...': name 'urgency_terms' is not defined
2025-05-17 22:21:48,753 - ERROR - Sentiment error for text 'rejecting update non critical ...': name 'urgency_terms' is not defined
2025-05-17 22:21:49,059 - ERROR - Sentiment error for text 'prox set due incoming seev upd...': name 'urgency_terms' is not defined
2025-05-17 22:21:49,282 - ERROR - Sentiment error for text 'found corp event bid repl sent...': name 'urgency_terms' is not defined
2025-05-17 22:21:49,498 - ERROR - Sentiment error for text 'hello client payment rejected ...': name 'urgency_terms' is not defined
2025-05-17 22:21:49,710 - ERROR - Sentiment error for text 'hello dvca event entitlement c...': name 'urgency_terms' is not defined
2025-05-17 22:21:50,006 - ERROR - Sentiment error for text 'found ignore functionality wor...': name 'urgency_terms' is not defined
2025-05-17 22:21:50,260 - ERROR - Sentiment error for text 'checking list found intr event...': name 'urgency_terms' is not defined
2025-05-17 22:21:50,542 - ERROR - Sentiment error for text 'cert unfreeze position success...': name 'urgency_terms' is not defined
2025-05-17 22:21:50,853 - ERROR - Sentiment error for text 'dear emna please find needed s...': name 'urgency_terms' is not defined
2025-05-17 22:21:51,110 - ERROR - Sentiment error for text 'see number event listed failed...': name 'urgency_terms' is not defined
2025-05-17 22:21:51,417 - ERROR - Sentiment error for text 'liqu set yesterday automatical...': name 'urgency_terms' is not defined
2025-05-17 22:21:51,599 - ERROR - Sentiment error for text 'trying search repair received ...': name 'urgency_terms' is not defined
2025-05-17 22:21:51,714 - ERROR - Sentiment error for text 'discussed oussema please amend...': name 'urgency_terms' is not defined
2025-05-17 22:21:51,871 - ERROR - Sentiment error for text 'cert trying unfreeze position ...': name 'urgency_terms' is not defined
2025-05-17 22:21:52,084 - ERROR - Sentiment error for text 'cert set without sec movement ...': name 'urgency_terms' is not defined
2025-05-17 22:21:52,331 - ERROR - Sentiment error for text 'xmet conflicting update reject...': name 'urgency_terms' is not defined
2025-05-17 22:21:52,514 - ERROR - Sentiment error for text 'dashboard alert financialinstr...': name 'urgency_terms' is not defined
2025-05-17 22:21:52,688 - ERROR - Sentiment error for text 'today received two new meet ev...': name 'urgency_terms' is not defined
2025-05-17 22:21:53,105 - ERROR - Sentiment error for text 'prox received seev could pleas...': name 'urgency_terms' is not defined
2025-05-17 22:21:53,316 - ERROR - Sentiment error for text 'dvop trying reject conflicting...': name 'urgency_terms' is not defined
2025-05-17 22:21:53,493 - ERROR - Sentiment error for text 'splf mt received friday around...': name 'urgency_terms' is not defined
2025-05-17 22:21:53,719 - ERROR - Sentiment error for text 'dashboard alert client subject...': name 'urgency_terms' is not defined
2025-05-17 22:21:54,005 - ERROR - Sentiment error for text 'today received new prox event ...': name 'urgency_terms' is not defined
2025-05-17 22:21:54,222 - ERROR - Sentiment error for text 'hello please adapt menu entitl...': name 'urgency_terms' is not defined
2025-05-17 22:21:54,361 - ERROR - Sentiment error for text 'bid update possible due blocke...': name 'urgency_terms' is not defined
2025-05-17 22:21:54,497 - ERROR - Sentiment error for text 'hi look like mt message sent c...': name 'urgency_terms' is not defined
2025-05-17 22:21:54,744 - ERROR - Sentiment error for text 'splr nostro sec account entitl...': name 'urgency_terms' is not defined
2025-05-17 22:21:54,874 - ERROR - Sentiment error for text 'please urgently check repe mes...': name 'urgency_terms' is not defined
2025-05-17 22:21:55,007 - ERROR - Sentiment error for text 'hello please adapt two dashboa...': name 'urgency_terms' is not defined
2025-05-17 22:21:55,192 - ERROR - Sentiment error for text 'update issue notification job ...': name 'urgency_terms' is not defined
2025-05-17 22:21:55,623 - ERROR - Sentiment error for text 'noticed freeze instruction rej...': name 'urgency_terms' is not defined
2025-05-17 22:21:55,952 - ERROR - Sentiment error for text 'isin cnex input currency hkd f...': name 'urgency_terms' is not defined
2025-05-17 22:21:56,054 - ERROR - Sentiment error for text 'ignored event found event clie...': name 'urgency_terms' is not defined
2025-05-17 22:21:56,164 - ERROR - Sentiment error for text 'xmet update possible due block...': name 'urgency_terms' is not defined
2025-05-17 22:21:56,306 - ERROR - Sentiment error for text 'please amend name search resul...': name 'urgency_terms' is not defined
2025-05-17 22:21:56,463 - ERROR - Sentiment error for text 'following dashboard alert need...': name 'urgency_terms' is not defined
2025-05-17 22:21:56,795 - ERROR - Sentiment error for text 'hi today rejected flow message...': name 'urgency_terms' is not defined
2025-05-17 22:21:57,081 - ERROR - Sentiment error for text 'currently client instruction p...': name 'urgency_terms' is not defined
2025-05-17 22:21:57,747 - ERROR - Sentiment error for text 'dear chekcing mb found followi...': name 'urgency_terms' is not defined
2025-05-17 22:21:57,940 - ERROR - Sentiment error for text 'noticed shds seev message list...': name 'urgency_terms' is not defined
2025-05-17 22:21:58,176 - ERROR - Sentiment error for text 'hello checking future interest...': name 'urgency_terms' is not defined
2025-05-17 22:21:58,273 - ERROR - Sentiment error for text 'trying find remove invalid dis...': name 'urgency_terms' is not defined
2025-05-17 22:21:58,426 - ERROR - Sentiment error for text 'drip discussed oussema mt rece...': name 'urgency_terms' is not defined
2025-05-17 22:21:58,541 - ERROR - Sentiment error for text 'hello client receive corporate...': name 'urgency_terms' is not defined
2025-05-17 22:21:58,688 - ERROR - Sentiment error for text 'deleted dash board alert somet...': name 'urgency_terms' is not defined
2025-05-17 22:21:58,898 - ERROR - Sentiment error for text 'dashboard alert client entitle...': name 'urgency_terms' is not defined
2025-05-17 22:21:59,119 - ERROR - Sentiment error for text 'isin nl dvca market payment cr...': name 'urgency_terms' is not defined
2025-05-17 22:21:59,261 - ERROR - Sentiment error for text 'dear noticed actual configurat...': name 'urgency_terms' is not defined
2025-05-17 22:21:59,425 - ERROR - Sentiment error for text 'shds set manually due issue in...': name 'urgency_terms' is not defined
2025-05-17 22:21:59,741 - ERROR - Sentiment error for text 'hello mt message sent line bps...': name 'urgency_terms' is not defined
2025-05-17 22:22:00,009 - ERROR - Sentiment error for text 'hello generated schedule creat...': name 'urgency_terms' is not defined
2025-05-17 22:22:00,213 - ERROR - Sentiment error for text 'mcal error regarding tax mcal ...': name 'urgency_terms' is not defined
2025-05-17 22:22:00,393 - ERROR - Sentiment error for text 'hi checking todo list event cl...': name 'urgency_terms' is not defined
2025-05-17 22:22:00,553 - ERROR - Sentiment error for text 'error alert try execute follow...': name 'urgency_terms' is not defined
2025-05-17 22:22:00,747 - ERROR - Sentiment error for text 'drip must update event adding ...': name 'urgency_terms' is not defined
2025-05-17 22:22:01,026 - ERROR - Sentiment error for text 'dear position interface receiv...': name 'urgency_terms' is not defined
2025-05-17 22:22:01,150 - ERROR - Sentiment error for text 'colleague issue opening todoli...': name 'urgency_terms' is not defined
2025-05-17 22:22:01,744 - ERROR - Sentiment error for text 'im rahmen einer datenmigration...': name 'urgency_terms' is not defined
2025-05-17 22:22:02,074 - ERROR - Sentiment error for text 'event dvca system round tax am...': name 'urgency_terms' is not defined
2025-05-17 22:22:02,187 - ERROR - Sentiment error for text 'please adapt platform followin...': name 'urgency_terms' is not defined
2025-05-17 22:22:02,329 - ERROR - Sentiment error for text 'dear added new client cash acc...': name 'urgency_terms' is not defined
2025-05-17 22:22:02,588 - ERROR - Sentiment error for text 'dear today amended client marb...': name 'urgency_terms' is not defined
2025-05-17 22:22:02,762 - ERROR - Sentiment error for text 'hello colleages today found on...': name 'urgency_terms' is not defined
2025-05-17 22:22:02,883 - ERROR - Sentiment error for text 'dear added two new client cash...': name 'urgency_terms' is not defined
2025-05-17 22:22:03,026 - ERROR - Sentiment error for text 'dear additional custody accoun...': name 'urgency_terms' is not defined
2025-05-17 22:22:03,345 - ERROR - Sentiment error for text 'tend discussed oussema skype s...': name 'urgency_terms' is not defined
2025-05-17 22:22:03,478 - ERROR - Sentiment error for text 'hello colleague position file ...': name 'urgency_terms' is not defined
2025-05-17 22:22:03,879 - ERROR - Sentiment error for text 'unfortunately wrong position e...': name 'urgency_terms' is not defined
2025-05-17 22:22:04,292 - ERROR - Sentiment error for text 'due unresolved bug oekb update...': name 'urgency_terms' is not defined
2025-05-17 22:22:04,490 - ERROR - Sentiment error for text 'soff noticed event soff create...': name 'urgency_terms' is not defined
2025-05-17 22:22:04,752 - ERROR - Sentiment error for text 'dear emna asset servicing depa...': name 'urgency_terms' is not defined
2025-05-17 22:22:05,084 - ERROR - Sentiment error for text 'two event create one isin u wo...': name 'urgency_terms' is not defined
2025-05-17 22:22:05,369 - ERROR - Sentiment error for text 'bmet created super event clien...': name 'urgency_terms' is not defined
2025-05-17 22:22:05,651 - ERROR - Sentiment error for text 'hello recieved following email...': name 'urgency_terms' is not defined
2025-05-17 22:22:05,956 - ERROR - Sentiment error for text 'client pending movement custom...': name 'urgency_terms' is not defined
2025-05-17 22:22:06,241 - ERROR - Sentiment error for text 'hello received today email ale...': name 'urgency_terms' is not defined
2025-05-17 22:22:06,577 - ERROR - Sentiment error for text 'hello unter menu point referen...': name 'urgency_terms' is not defined
2025-05-17 22:22:06,963 - ERROR - Sentiment error for text 'tend event updated via editfun...': name 'urgency_terms' is not defined
2025-05-17 22:22:07,191 - ERROR - Sentiment error for text 'could please urgently check pr...': name 'urgency_terms' is not defined
2025-05-17 22:22:07,377 - ERROR - Sentiment error for text 'drip could please urgently rej...': name 'urgency_terms' is not defined
2025-05-17 22:22:07,753 - ERROR - Sentiment error for text 'dear following situation datap...': name 'urgency_terms' is not defined
2025-05-17 22:22:08,230 - ERROR - Sentiment error for text 'hello customer enter instructi...': name 'urgency_terms' is not defined
2025-05-17 22:22:08,473 - ERROR - Sentiment error for text 'mt meet fail error code useful...': name 'urgency_terms' is not defined
2025-05-17 22:22:08,835 - ERROR - Sentiment error for text 'hi received alert monitoring b...': name 'urgency_terms' is not defined
2025-05-17 22:22:09,032 - ERROR - Sentiment error for text 'dear internal pending trade al...': name 'urgency_terms' is not defined
2025-05-17 22:22:09,552 - ERROR - Sentiment error for text 'seev newm repl attached receiv...': name 'urgency_terms' is not defined
2025-05-17 22:22:09,823 - ERROR - Sentiment error for text 'hi set conv mand event via sup...': name 'urgency_terms' is not defined
2025-05-17 22:22:10,131 - ERROR - Sentiment error for text 'hi already discussed thouraya ...': name 'urgency_terms' is not defined
2025-05-17 22:22:10,356 - ERROR - Sentiment error for text 'could please check flow out im...': name 'urgency_terms' is not defined
2025-05-17 22:22:10,499 - ERROR - Sentiment error for text 'hello received email alert int...': name 'urgency_terms' is not defined
2025-05-17 22:22:10,958 - ERROR - Sentiment error for text 'anpassung customer interface c...': name 'urgency_terms' is not defined
2025-05-17 22:22:11,395 - ERROR - Sentiment error for text 'hi today issue reported oekb p...': name 'urgency_terms' is not defined
2025-05-17 22:22:12,206 - ERROR - Sentiment error for text 'dear since asoc switch soap re...': name 'urgency_terms' is not defined
2025-05-17 22:22:12,458 - ERROR - Sentiment error for text 'could please check error value...': name 'urgency_terms' is not defined
2025-05-17 22:22:12,617 - ERROR - Sentiment error for text 'dear analysed created issue oe...': name 'urgency_terms' is not defined
2025-05-17 22:22:12,783 - ERROR - Sentiment error for text 'today received following alert...': name 'urgency_terms' is not defined
2025-05-17 22:22:13,263 - ERROR - Sentiment error for text 'received following email alert...': name 'urgency_terms' is not defined
2025-05-17 22:22:13,470 - ERROR - Sentiment error for text 'hello today iposition interfac...': name 'urgency_terms' is not defined
2025-05-17 22:22:13,559 - ERROR - Sentiment error for text 'liqu set outgoing comment mt i...': name 'urgency_terms' is not defined
2025-05-17 22:22:13,660 - ERROR - Sentiment error for text 'hello due interference update ...': name 'urgency_terms' is not defined
2025-05-17 22:22:13,815 - ERROR - Sentiment error for text 'dear emna please find attached...': name 'urgency_terms' is not defined
2025-05-17 22:22:14,034 - ERROR - Sentiment error for text 'soff noticed event updated sys...': name 'urgency_terms' is not defined
2025-05-17 22:22:14,172 - ERROR - Sentiment error for text 'recieved following email alert...': name 'urgency_terms' is not defined
2025-05-17 22:22:14,420 - ERROR - Sentiment error for text 'isin ata intr event created co...': name 'urgency_terms' is not defined
2025-05-17 22:22:14,582 - ERROR - Sentiment error for text 'hi oussema position interface ...': name 'urgency_terms' is not defined
2025-05-17 22:22:15,036 - ERROR - Sentiment error for text 'hello pending trade file error...': name 'urgency_terms' is not defined
2025-05-17 22:22:15,193 - ERROR - Sentiment error for text 'hello happy new year validatio...': name 'urgency_terms' is not defined
2025-05-17 22:22:15,584 - ERROR - Sentiment error for text 'hi seev received provider ihs ...': name 'urgency_terms' is not defined
2025-05-17 22:22:15,700 - ERROR - Sentiment error for text 'good morning happy new year no...': name 'urgency_terms' is not defined
2025-05-17 22:22:15,883 - ERROR - Sentiment error for text 'following alert flow id failed...': name 'urgency_terms' is not defined
2025-05-17 22:22:16,063 - ERROR - Sentiment error for text 'exofm could please check follo...': name 'urgency_terms' is not defined
2025-05-17 22:22:16,067 - INFO - Using 'Created' as creation date
2025-05-17 22:22:16,068 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:22:16,103 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:22:16,104 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:22:16,183 - INFO - Generated client metrics for 11 creators
2025-05-17 22:22:16,186 - WARNING - No Status column found, using default status distribution
2025-05-17 22:22:16,186 - INFO - Analysis completed successfully
2025-05-17 22:24:13,213 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-17 22:24:32,039 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls_WgQ3XQ8.xlsx
2025-05-17 22:24:32,451 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:24:32,452 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:24:32,452 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:24:32,453 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:24:32,455 - INFO - Cleaned Creator column
2025-05-17 22:24:32,456 - INFO - Removed duplicates based on Key column
2025-05-17 22:24:35,814 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:24:37,810 - ERROR - Sentiment error for text 'exof processed one client paym...': name 'urgency_terms' is not defined
2025-05-17 22:24:37,896 - ERROR - Sentiment error for text 'concerning dashboard alert val...': name 'urgency_terms' is not defined
2025-05-17 22:24:37,949 - ERROR - Sentiment error for text 'hello emna need one new userpr...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,012 - ERROR - Sentiment error for text 'hello come attention three int...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,059 - ERROR - Sentiment error for text 'hello emna colleague mc via da...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,117 - ERROR - Sentiment error for text 'hi due mapping error received ...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,157 - ERROR - Sentiment error for text 'hello january redm event updat...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,194 - ERROR - Sentiment error for text 'hi emna colleague today reciev...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,241 - ERROR - Sentiment error for text 'prox set according seev attach...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,294 - ERROR - Sentiment error for text 'shds elig po blocked account s...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,332 - ERROR - Sentiment error for text 'hello emnacolleagues need crea...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,403 - ERROR - Sentiment error for text 'isin go financial instrument c...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,442 - ERROR - Sentiment error for text 'hi wanted check something mega...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,496 - ERROR - Sentiment error for text 'hi emna colleague thursday goi...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,538 - ERROR - Sentiment error for text 'hello discussed let please add...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,575 - ERROR - Sentiment error for text 'dear colleague outage weekend ...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,628 - ERROR - Sentiment error for text 'tend tried send mt mt newm not...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,762 - ERROR - Sentiment error for text 'test ticket consol interface i...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,811 - ERROR - Sentiment error for text 'hello dear gerhard login mc pr...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,862 - ERROR - Sentiment error for text 'prox noticed input client mark...': name 'urgency_terms' is not defined
2025-05-17 22:24:38,930 - ERROR - Sentiment error for text 'yesterday received first seev ...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,001 - ERROR - Sentiment error for text 'hello emna colleague isins nee...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,057 - ERROR - Sentiment error for text 'prox received seev moved statu...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,098 - ERROR - Sentiment error for text 'hello please check loan schedu...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,139 - ERROR - Sentiment error for text 'hello unfortunately possible u...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,202 - ERROR - Sentiment error for text 'hello please check loan schedu...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,238 - ERROR - Sentiment error for text 'hello january received three m...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,321 - ERROR - Sentiment error for text 'hi emna noticed client based n...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,384 - ERROR - Sentiment error for text 'several dashboard alert ignore...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,449 - ERROR - Sentiment error for text 'please note several meet event...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,490 - ERROR - Sentiment error for text 'hi issue oekb still appears pr...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,563 - ERROR - Sentiment error for text 'issue seev sent def wmdeff tod...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,599 - ERROR - Sentiment error for text 'issue reported oekb retested s...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,652 - ERROR - Sentiment error for text 'prox want send seev repl autho...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,684 - ERROR - Sentiment error for text 'dear colleague noticed pending...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,730 - ERROR - Sentiment error for text 'today found lei mandatory send...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,764 - ERROR - Sentiment error for text 'hello correct payment date int...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,806 - ERROR - Sentiment error for text 'issue detected reported oekb w...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,853 - ERROR - Sentiment error for text 'noticed announcement date rece...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,894 - ERROR - Sentiment error for text 'hello update possible abovemen...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,940 - ERROR - Sentiment error for text 'issue three isins pcal event c...': name 'urgency_terms' is not defined
2025-05-17 22:24:39,989 - ERROR - Sentiment error for text 'bput isin cash instruction shs...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,033 - ERROR - Sentiment error for text 'isin usm main ref dvca created...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,095 - ERROR - Sentiment error for text 'trying remove invalid disclosu...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,148 - ERROR - Sentiment error for text 'tried create entry ustax data ...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,188 - ERROR - Sentiment error for text 'could please check send disclo...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,234 - ERROR - Sentiment error for text 'dear pls let analyse starting ...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,272 - ERROR - Sentiment error for text 'received attached mt message f...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,313 - ERROR - Sentiment error for text 'trying ignore item update frac...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,372 - ERROR - Sentiment error for text 'dear colleague noticed lukas m...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,418 - ERROR - Sentiment error for text 'dear coleagues pls help settin...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,458 - ERROR - Sentiment error for text 'dvop mp validated still status...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,497 - ERROR - Sentiment error for text 'hello recieved attached notifi...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,537 - ERROR - Sentiment error for text 'could please urgently check at...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,581 - ERROR - Sentiment error for text 'dvop update event data add fin...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,619 - ERROR - Sentiment error for text 'hello abovementioned pcal even...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,659 - ERROR - Sentiment error for text 'hello stock exchange claim abo...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,699 - ERROR - Sentiment error for text 'dvse set including tax movemen...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,752 - ERROR - Sentiment error for text 'dvse cash client payment autom...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,812 - ERROR - Sentiment error for text 'hi sometimes face situation pr...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,851 - ERROR - Sentiment error for text 'hello dvca event mentioned tak...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,887 - ERROR - Sentiment error for text 'dashboard alert redm loan calc...': name 'urgency_terms' is not defined
2025-05-17 22:24:40,970 - ERROR - Sentiment error for text 'could please check feasibility...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,016 - ERROR - Sentiment error for text 'could please check following e...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,069 - ERROR - Sentiment error for text 'today received seev repl custo...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,126 - ERROR - Sentiment error for text 'prox issrddlnforvtng received ...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,193 - ERROR - Sentiment error for text 'prox sent seev client containi...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,248 - ERROR - Sentiment error for text 'prox set according incoming se...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,316 - ERROR - Sentiment error for text 'hello currency conversion dvca...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,443 - ERROR - Sentiment error for text 'hi niki omar today swift lot s...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,488 - ERROR - Sentiment error for text 'hello isins listed target paym...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,526 - ERROR - Sentiment error for text 'hello two redm event listed bl...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,586 - ERROR - Sentiment error for text 'hello noticed migrated event s...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,629 - ERROR - Sentiment error for text 'hello updated price mentioned ...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,692 - ERROR - Sentiment error for text 'hello although market payment ...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,730 - ERROR - Sentiment error for text 'shds crosschecked via set reli...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,781 - ERROR - Sentiment error for text 'hello menu item repair receive...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,819 - ERROR - Sentiment error for text 'hello event mentioned updated ...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,863 - ERROR - Sentiment error for text 'hi exof event activated fact s...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,908 - ERROR - Sentiment error for text 'exof niki noticed mt rmdr toda...': name 'urgency_terms' is not defined
2025-05-17 22:24:41,972 - ERROR - Sentiment error for text 'chan today found client reques...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,009 - ERROR - Sentiment error for text 'hello update two isins listed ...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,044 - ERROR - Sentiment error for text 'noticed event already closed l...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,091 - ERROR - Sentiment error for text 'hello recieved following alert...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,130 - ERROR - Sentiment error for text 'hello three mcal event listed ...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,169 - ERROR - Sentiment error for text 'dashboard alert tax slas valid...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,203 - ERROR - Sentiment error for text 'following ticket oekb megacor ...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,244 - ERROR - Sentiment error for text 'mrgrevents pay date confirmed ...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,284 - ERROR - Sentiment error for text 'hi emna job client notificatio...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,338 - ERROR - Sentiment error for text 'hello intr event intr correctl...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,379 - ERROR - Sentiment error for text 'hello emna please check alert ...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,440 - ERROR - Sentiment error for text 'meet issue oekb first repl rec...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,487 - ERROR - Sentiment error for text 'dear emna file generated name ...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,518 - ERROR - Sentiment error for text 'hello function ignore button l...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,574 - ERROR - Sentiment error for text 'exof set manually mt newm sent...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,634 - ERROR - Sentiment error for text 'splr set manually yesterday mt...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,695 - ERROR - Sentiment error for text 'testing import seev including ...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,733 - ERROR - Sentiment error for text 'could please check add followi...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,790 - ERROR - Sentiment error for text 'exof updated saved event aroun...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,822 - ERROR - Sentiment error for text 'dashboard alert failed receive...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,856 - ERROR - Sentiment error for text 'attached mt rmdr received arou...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,902 - ERROR - Sentiment error for text 'redm atak hello redm event red...': name 'urgency_terms' is not defined
2025-05-17 22:24:42,951 - ERROR - Sentiment error for text 'dashboard alert loan calc erro...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,003 - ERROR - Sentiment error for text 'problem ticket oekb still exis...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,056 - ERROR - Sentiment error for text 'hello dashbort alert failed bo...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,096 - ERROR - Sentiment error for text 'hello update possible intr eve...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,151 - ERROR - Sentiment error for text 'hello look isin menu item vali...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,189 - ERROR - Sentiment error for text 'hello intr pcal event isin dec...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,251 - ERROR - Sentiment error for text 'xmet received message event ne...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,285 - ERROR - Sentiment error for text 'hello entered saved tax rate e...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,322 - ERROR - Sentiment error for text 'notice receive feed wm several...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,385 - ERROR - Sentiment error for text 'following dashboard alert dele...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,426 - ERROR - Sentiment error for text 'prox set according incoming se...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,492 - ERROR - Sentiment error for text 'valid dashbaord alert validate...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,538 - ERROR - Sentiment error for text 'prox event updated validated h...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,577 - ERROR - Sentiment error for text 'checking mt drop folder found ...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,614 - ERROR - Sentiment error for text 'hello found isin atalc dashboa...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,653 - ERROR - Sentiment error for text 'hello ignore incoming message ...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,701 - ERROR - Sentiment error for text 'hello found mb flow id related...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,734 - ERROR - Sentiment error for text 'trying investigate msg megacor...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,771 - ERROR - Sentiment error for text 'othr set via super event mand ...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,807 - ERROR - Sentiment error for text 'problem dashboard alert valida...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,843 - ERROR - Sentiment error for text 'hello update possible abovemen...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,884 - ERROR - Sentiment error for text 'hello entitlement calculated c...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,938 - ERROR - Sentiment error for text 'today noticed new confirm aler...': name 'urgency_terms' is not defined
2025-05-17 22:24:43,986 - ERROR - Sentiment error for text 'testing seevmessages wm turned...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,042 - ERROR - Sentiment error for text 'client email adress purpose ac...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,081 - ERROR - Sentiment error for text 'dashboard alert entitlement ca...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,138 - ERROR - Sentiment error for text 'today received mt newm xmet ev...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,203 - ERROR - Sentiment error for text 'today attached mt newm receive...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,257 - ERROR - Sentiment error for text 'received mt failed following e...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,326 - ERROR - Sentiment error for text 'retesting oekb splr qas notice...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,360 - ERROR - Sentiment error for text 'hello try cross check several ...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,398 - ERROR - Sentiment error for text 'shds received disclosure reque...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,436 - ERROR - Sentiment error for text 'dflt migrated event imagepng t...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,475 - ERROR - Sentiment error for text 'xmet received mt repl attached...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,513 - ERROR - Sentiment error for text 'event payment date today still...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,584 - ERROR - Sentiment error for text 'dvop set creating custodian sl...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,623 - ERROR - Sentiment error for text 'noticed event type tndp wrtc a...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,691 - ERROR - Sentiment error for text 'today received data feed flowi...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,733 - ERROR - Sentiment error for text 'bid updated event today adding...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,790 - ERROR - Sentiment error for text 'following isins early redempti...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,828 - ERROR - Sentiment error for text 'exof checking mt attached secu...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,895 - ERROR - Sentiment error for text 'isins target payment date publ...': name 'urgency_terms' is not defined
2025-05-17 22:24:44,941 - ERROR - Sentiment error for text 'hello emna already email discu...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,054 - ERROR - Sentiment error for text 'hello menu item repair receive...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,108 - ERROR - Sentiment error for text 'repair received feed following...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,147 - ERROR - Sentiment error for text 'bid set today validated need n...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,184 - ERROR - Sentiment error for text 'hi swift mt pred event field n...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,237 - ERROR - Sentiment error for text 'today noticed button edit tole...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,276 - ERROR - Sentiment error for text 'still issue list trying ignore...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,312 - ERROR - Sentiment error for text 'hello noticed internal comment...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,349 - ERROR - Sentiment error for text 'hello intr event per october i...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,392 - ERROR - Sentiment error for text 'mt canc received yesterday ima...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,428 - ERROR - Sentiment error for text 'good morning dear today saw al...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,459 - ERROR - Sentiment error for text 'hello received last two day em...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,497 - ERROR - Sentiment error for text 'hello generated schedule creat...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,534 - ERROR - Sentiment error for text 'could please check possible ig...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,573 - ERROR - Sentiment error for text 'bid set via super event volu i...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,609 - ERROR - Sentiment error for text 'hello two intr event interest ...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,653 - ERROR - Sentiment error for text 'hi discussed within oekb field...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,716 - ERROR - Sentiment error for text 'dvca exercise dividend right s...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,759 - ERROR - Sentiment error for text 'hello dvca dividend processed ...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,813 - ERROR - Sentiment error for text 'exof yesterday instructed slle...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,874 - ERROR - Sentiment error for text 'cert could please urgently che...': name 'urgency_terms' is not defined
2025-05-17 22:24:45,931 - ERROR - Sentiment error for text 'xmet received newm repl within...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,001 - ERROR - Sentiment error for text 'exof secu option set miex milt...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,066 - ERROR - Sentiment error for text 'exofexof received huge amount ...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,107 - ERROR - Sentiment error for text 'today client sent two instruct...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,165 - ERROR - Sentiment error for text 'could please check following r...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,203 - ERROR - Sentiment error for text 'exof issue reported oekb flow ...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,260 - ERROR - Sentiment error for text 'cert two new client position x...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,299 - ERROR - Sentiment error for text 'coincidentally found nd rmdr m...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,339 - ERROR - Sentiment error for text 'process mrgr event friday mark...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,381 - ERROR - Sentiment error for text 'event payment day today ca sta...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,421 - ERROR - Sentiment error for text 'dvca hello unfortunately possi...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,519 - ERROR - Sentiment error for text 'u hello isin philip morris con...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,559 - ERROR - Sentiment error for text 'xmet received mt repl custodia...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,593 - ERROR - Sentiment error for text 'dear received first pending tr...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,629 - ERROR - Sentiment error for text 'menu event u tax reporting go ...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,701 - ERROR - Sentiment error for text 'intr hello event breakdown ins...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,761 - ERROR - Sentiment error for text 'payment foreign currency fx eu...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,799 - ERROR - Sentiment error for text 'exof issue yesterday see oekb ...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,839 - ERROR - Sentiment error for text 'could please provide u ignoreb...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,880 - ERROR - Sentiment error for text 'xmet checked internal swift dr...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,918 - ERROR - Sentiment error for text 'good morning dear pending trad...': name 'urgency_terms' is not defined
2025-05-17 22:24:46,984 - ERROR - Sentiment error for text 'exof received several mt instr...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,042 - ERROR - Sentiment error for text 'exof conflicting update rmdr w...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,133 - ERROR - Sentiment error for text 'good morning dear colleague up...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,215 - ERROR - Sentiment error for text 'prox set according received se...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,247 - ERROR - Sentiment error for text 'dear checking saw batch proces...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,338 - ERROR - Sentiment error for text 'exof event received two mt sec...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,376 - ERROR - Sentiment error for text 'exof please urgently check two...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,416 - ERROR - Sentiment error for text 'dear expecting f value cashmov...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,479 - ERROR - Sentiment error for text 'exof checking mt flowouts noti...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,554 - ERROR - Sentiment error for text 'hello intr event price calcula...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,593 - ERROR - Sentiment error for text 'intr hello interest rate calcu...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,648 - ERROR - Sentiment error for text 'dscl conflicting update valida...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,692 - ERROR - Sentiment error for text 'rhdi updated event following a...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,736 - ERROR - Sentiment error for text 'exof updated event saved after...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,771 - ERROR - Sentiment error for text 'good morning today pending tra...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,805 - ERROR - Sentiment error for text 'good morning dear info today p...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,872 - ERROR - Sentiment error for text 'splr processed new workaround ...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,907 - ERROR - Sentiment error for text 'mrgr conflicting update wanted...': name 'urgency_terms' is not defined
2025-05-17 22:24:47,947 - ERROR - Sentiment error for text 'hi ive imported together oekb ...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,055 - ERROR - Sentiment error for text 'montag um wurde festgestellt d...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,161 - ERROR - Sentiment error for text 'hi oussema today flow stukced ...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,194 - ERROR - Sentiment error for text 'omet conflicting update wanted...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,225 - ERROR - Sentiment error for text 'hello colleague noticed batch ...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,334 - ERROR - Sentiment error for text 'dear ive found flow status gen...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,370 - ERROR - Sentiment error for text 'good morning dear currently po...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,407 - ERROR - Sentiment error for text 'exof exof set event today mt s...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,443 - ERROR - Sentiment error for text 'hello atasgh dividend processe...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,483 - ERROR - Sentiment error for text 'dear missing mt custodian star...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,514 - ERROR - Sentiment error for text 'hello attached list payment wi...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,553 - ERROR - Sentiment error for text 'hello today working togheter t...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,587 - ERROR - Sentiment error for text 'today received security event ...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,621 - ERROR - Sentiment error for text 'dear colleague pending trade b...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,660 - ERROR - Sentiment error for text 'hello ca status correct event ...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,696 - ERROR - Sentiment error for text 'good morning info prod ist ava...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,760 - ERROR - Sentiment error for text 'dvca nl hello discussed oussem...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,818 - ERROR - Sentiment error for text 'hi received mt error tradable ...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,877 - ERROR - Sentiment error for text 'splr set manually yesterday mt...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,911 - ERROR - Sentiment error for text 'please check confirmedeventscs...': name 'urgency_terms' is not defined
2025-05-17 22:24:48,956 - ERROR - Sentiment error for text 'received seev message found li...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,007 - ERROR - Sentiment error for text 'redm reverse mp processed twic...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,051 - ERROR - Sentiment error for text 'could please check following t...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,088 - ERROR - Sentiment error for text 'today lot event ca status exec...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,144 - ERROR - Sentiment error for text 'xmet event updated friday succ...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,191 - ERROR - Sentiment error for text 'drip conflicting update wanted...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,233 - ERROR - Sentiment error for text 'mt received unknown secu isin ...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,271 - ERROR - Sentiment error for text 'drip must update event adding ...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,311 - ERROR - Sentiment error for text 'hello please check mtrepe outg...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,349 - ERROR - Sentiment error for text 'checking repair received feedl...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,390 - ERROR - Sentiment error for text 'hello team deinstall firefox o...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,452 - ERROR - Sentiment error for text 'received several mt repl mass ...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,498 - ERROR - Sentiment error for text 'chan field event purpose gener...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,537 - ERROR - Sentiment error for text 'cert mt attached received cont...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,582 - ERROR - Sentiment error for text 'exof market payment received s...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,625 - ERROR - Sentiment error for text 'prox received client complaint...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,662 - ERROR - Sentiment error for text 'dscl update possible due block...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,703 - ERROR - Sentiment error for text 'noticed sometimes input isin i...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,741 - ERROR - Sentiment error for text 'chan conflicting update wanted...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,781 - ERROR - Sentiment error for text 'hello received following email...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,851 - ERROR - Sentiment error for text 'coincidentelly found mt repe r...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,892 - ERROR - Sentiment error for text 'chan set chan term event provi...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,935 - ERROR - Sentiment error for text 'drip received cash payment es ...': name 'urgency_terms' is not defined
2025-05-17 22:24:49,976 - ERROR - Sentiment error for text 'drip received two mt message e...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,009 - ERROR - Sentiment error for text 'meet meet update possible due ...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,064 - ERROR - Sentiment error for text 'rejecting update non critical ...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,108 - ERROR - Sentiment error for text 'prox set due incoming seev upd...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,150 - ERROR - Sentiment error for text 'found corp event bid repl sent...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,191 - ERROR - Sentiment error for text 'hello client payment rejected ...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,232 - ERROR - Sentiment error for text 'hello dvca event entitlement c...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,284 - ERROR - Sentiment error for text 'found ignore functionality wor...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,330 - ERROR - Sentiment error for text 'checking list found intr event...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,369 - ERROR - Sentiment error for text 'cert unfreeze position success...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,445 - ERROR - Sentiment error for text 'dear emna please find needed s...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,489 - ERROR - Sentiment error for text 'see number event listed failed...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,542 - ERROR - Sentiment error for text 'liqu set yesterday automatical...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,581 - ERROR - Sentiment error for text 'trying search repair received ...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,622 - ERROR - Sentiment error for text 'discussed oussema please amend...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,659 - ERROR - Sentiment error for text 'cert trying unfreeze position ...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,699 - ERROR - Sentiment error for text 'cert set without sec movement ...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,741 - ERROR - Sentiment error for text 'xmet conflicting update reject...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,778 - ERROR - Sentiment error for text 'dashboard alert financialinstr...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,819 - ERROR - Sentiment error for text 'today received two new meet ev...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,884 - ERROR - Sentiment error for text 'prox received seev could pleas...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,920 - ERROR - Sentiment error for text 'dvop trying reject conflicting...': name 'urgency_terms' is not defined
2025-05-17 22:24:50,961 - ERROR - Sentiment error for text 'splf mt received friday around...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,017 - ERROR - Sentiment error for text 'dashboard alert client subject...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,067 - ERROR - Sentiment error for text 'today received new prox event ...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,108 - ERROR - Sentiment error for text 'hello please adapt menu entitl...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,141 - ERROR - Sentiment error for text 'bid update possible due blocke...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,174 - ERROR - Sentiment error for text 'hi look like mt message sent c...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,235 - ERROR - Sentiment error for text 'splr nostro sec account entitl...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,268 - ERROR - Sentiment error for text 'please urgently check repe mes...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,305 - ERROR - Sentiment error for text 'hello please adapt two dashboa...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,351 - ERROR - Sentiment error for text 'update issue notification job ...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,426 - ERROR - Sentiment error for text 'noticed freeze instruction rej...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,487 - ERROR - Sentiment error for text 'isin cnex input currency hkd f...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,525 - ERROR - Sentiment error for text 'ignored event found event clie...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,561 - ERROR - Sentiment error for text 'xmet update possible due block...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,598 - ERROR - Sentiment error for text 'please amend name search resul...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,641 - ERROR - Sentiment error for text 'following dashboard alert need...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,713 - ERROR - Sentiment error for text 'hi today rejected flow message...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,767 - ERROR - Sentiment error for text 'currently client instruction p...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,886 - ERROR - Sentiment error for text 'dear chekcing mb found followi...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,931 - ERROR - Sentiment error for text 'noticed shds seev message list...': name 'urgency_terms' is not defined
2025-05-17 22:24:51,987 - ERROR - Sentiment error for text 'hello checking future interest...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,031 - ERROR - Sentiment error for text 'trying find remove invalid dis...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,096 - ERROR - Sentiment error for text 'drip discussed oussema mt rece...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,142 - ERROR - Sentiment error for text 'hello client receive corporate...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,190 - ERROR - Sentiment error for text 'deleted dash board alert somet...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,233 - ERROR - Sentiment error for text 'dashboard alert client entitle...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,263 - ERROR - Sentiment error for text 'isin nl dvca market payment cr...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,302 - ERROR - Sentiment error for text 'dear noticed actual configurat...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,339 - ERROR - Sentiment error for text 'shds set manually due issue in...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,394 - ERROR - Sentiment error for text 'hello mt message sent line bps...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,445 - ERROR - Sentiment error for text 'hello generated schedule creat...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,492 - ERROR - Sentiment error for text 'mcal error regarding tax mcal ...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,531 - ERROR - Sentiment error for text 'hi checking todo list event cl...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,580 - ERROR - Sentiment error for text 'error alert try execute follow...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,623 - ERROR - Sentiment error for text 'drip must update event adding ...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,685 - ERROR - Sentiment error for text 'dear position interface receiv...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,721 - ERROR - Sentiment error for text 'colleague issue opening todoli...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,819 - ERROR - Sentiment error for text 'im rahmen einer datenmigration...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,878 - ERROR - Sentiment error for text 'event dvca system round tax am...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,911 - ERROR - Sentiment error for text 'please adapt platform followin...': name 'urgency_terms' is not defined
2025-05-17 22:24:52,948 - ERROR - Sentiment error for text 'dear added new client cash acc...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,011 - ERROR - Sentiment error for text 'dear today amended client marb...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,081 - ERROR - Sentiment error for text 'hello colleages today found on...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,111 - ERROR - Sentiment error for text 'dear added two new client cash...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,149 - ERROR - Sentiment error for text 'dear additional custody accoun...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,191 - ERROR - Sentiment error for text 'tend discussed oussema skype s...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,229 - ERROR - Sentiment error for text 'hello colleague position file ...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,298 - ERROR - Sentiment error for text 'unfortunately wrong position e...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,359 - ERROR - Sentiment error for text 'due unresolved bug oekb update...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,401 - ERROR - Sentiment error for text 'soff noticed event soff create...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,461 - ERROR - Sentiment error for text 'dear emna asset servicing depa...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,515 - ERROR - Sentiment error for text 'two event create one isin u wo...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,569 - ERROR - Sentiment error for text 'bmet created super event clien...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,624 - ERROR - Sentiment error for text 'hello recieved following email...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,682 - ERROR - Sentiment error for text 'client pending movement custom...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,737 - ERROR - Sentiment error for text 'hello received today email ale...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,796 - ERROR - Sentiment error for text 'hello unter menu point referen...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,864 - ERROR - Sentiment error for text 'tend event updated via editfun...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,916 - ERROR - Sentiment error for text 'could please urgently check pr...': name 'urgency_terms' is not defined
2025-05-17 22:24:53,960 - ERROR - Sentiment error for text 'drip could please urgently rej...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,027 - ERROR - Sentiment error for text 'dear following situation datap...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,135 - ERROR - Sentiment error for text 'hello customer enter instructi...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,170 - ERROR - Sentiment error for text 'mt meet fail error code useful...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,227 - ERROR - Sentiment error for text 'hi received alert monitoring b...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,266 - ERROR - Sentiment error for text 'dear internal pending trade al...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,367 - ERROR - Sentiment error for text 'seev newm repl attached receiv...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,418 - ERROR - Sentiment error for text 'hi set conv mand event via sup...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,503 - ERROR - Sentiment error for text 'hi already discussed thouraya ...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,553 - ERROR - Sentiment error for text 'could please check flow out im...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,595 - ERROR - Sentiment error for text 'hello received email alert int...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,703 - ERROR - Sentiment error for text 'anpassung customer interface c...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,802 - ERROR - Sentiment error for text 'hi today issue reported oekb p...': name 'urgency_terms' is not defined
2025-05-17 22:24:54,962 - ERROR - Sentiment error for text 'dear since asoc switch soap re...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,017 - ERROR - Sentiment error for text 'could please check error value...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,058 - ERROR - Sentiment error for text 'dear analysed created issue oe...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,101 - ERROR - Sentiment error for text 'today received following alert...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,201 - ERROR - Sentiment error for text 'received following email alert...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,255 - ERROR - Sentiment error for text 'hello today iposition interfac...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,295 - ERROR - Sentiment error for text 'liqu set outgoing comment mt i...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,340 - ERROR - Sentiment error for text 'hello due interference update ...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,382 - ERROR - Sentiment error for text 'dear emna please find attached...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,444 - ERROR - Sentiment error for text 'soff noticed event updated sys...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,486 - ERROR - Sentiment error for text 'recieved following email alert...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,557 - ERROR - Sentiment error for text 'isin ata intr event created co...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,626 - ERROR - Sentiment error for text 'hi oussema position interface ...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,709 - ERROR - Sentiment error for text 'hello pending trade file error...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,745 - ERROR - Sentiment error for text 'hello happy new year validatio...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,843 - ERROR - Sentiment error for text 'hi seev received provider ihs ...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,877 - ERROR - Sentiment error for text 'good morning happy new year no...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,930 - ERROR - Sentiment error for text 'following alert flow id failed...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,990 - ERROR - Sentiment error for text 'exofm could please check follo...': name 'urgency_terms' is not defined
2025-05-17 22:24:55,992 - INFO - Using 'Created' as creation date
2025-05-17 22:24:55,993 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:24:55,997 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:24:55,998 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:24:56,030 - INFO - Generated client metrics for 11 creators
2025-05-17 22:24:56,031 - WARNING - No Status column found, using default status distribution
2025-05-17 22:24:56,031 - INFO - Analysis completed successfully
2025-05-17 22:25:58,306 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-17 22:26:30,368 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls_WgQ3XQ8.xlsx
2025-05-17 22:26:30,694 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:26:30,695 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:26:30,695 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:26:30,696 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:26:30,697 - INFO - Cleaned Creator column
2025-05-17 22:26:30,698 - INFO - Removed duplicates based on Key column
2025-05-17 22:26:37,169 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:28:11,209 - INFO - Using 'Created' as creation date
2025-05-17 22:28:11,211 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:28:11,218 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:28:11,220 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:28:11,279 - INFO - Generated client metrics for 11 creators
2025-05-17 22:28:11,280 - WARNING - No Status column found, using default status distribution
2025-05-17 22:28:11,281 - INFO - Analysis completed successfully
2025-05-17 22:29:20,239 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-17 22:29:33,558 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls_WgQ3XQ8.xlsx
2025-05-17 22:29:34,801 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:29:34,803 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:29:34,804 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:29:34,805 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:29:34,807 - INFO - Cleaned Creator column
2025-05-17 22:29:34,809 - INFO - Removed duplicates based on Key column
2025-05-17 22:29:40,985 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:30:14,153 - INFO - Using 'Created' as creation date
2025-05-17 22:30:14,154 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:30:14,159 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:30:14,160 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:30:14,199 - INFO - Generated client metrics for 11 creators
2025-05-17 22:30:14,201 - WARNING - No Status column found, using default status distribution
2025-05-17 22:30:14,201 - INFO - Analysis completed successfully
2025-05-17 22:32:44,941 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Ticket_UAT.xls_mbe42p3.xlsx
2025-05-17 22:32:45,105 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:32:45,106 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:32:45,106 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:32:45,107 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:32:45,108 - INFO - Cleaned Creator column
2025-05-17 22:32:45,109 - INFO - Removed duplicates based on Key column
2025-05-17 22:32:45,109 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:33:03,380 - INFO - Using 'Created' as creation date
2025-05-17 22:33:03,381 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:33:03,386 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:33:03,386 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:33:03,425 - INFO - Generated client metrics for 8 creators
2025-05-17 22:33:03,426 - WARNING - No Status column found, using default status distribution
2025-05-17 22:33:03,426 - INFO - Analysis completed successfully
2025-05-17 22:33:18,762 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\tickets_prod.xls_G6I7u5p.xlsx
2025-05-17 22:33:18,815 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:33:18,816 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:33:18,816 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:33:18,817 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:33:18,819 - INFO - Cleaned Creator column
2025-05-17 22:33:18,820 - INFO - Removed duplicates based on Key column
2025-05-17 22:33:18,820 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:33:34,097 - INFO - Using 'Created' as creation date
2025-05-17 22:33:34,097 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:33:34,101 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:33:34,102 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:33:34,134 - INFO - Generated client metrics for 8 creators
2025-05-17 22:33:34,136 - WARNING - No Status column found, using default status distribution
2025-05-17 22:33:34,136 - INFO - Analysis completed successfully
2025-05-17 22:53:13,809 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls_WgQ3XQ8.xlsx
2025-05-17 22:53:13,990 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:53:13,991 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:53:13,991 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:53:13,993 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:53:13,994 - INFO - Cleaned Creator column
2025-05-17 22:53:13,994 - INFO - Removed duplicates based on Key column
2025-05-17 22:53:13,995 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:53:31,391 - INFO - Using 'Created' as creation date
2025-05-17 22:53:31,392 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:53:31,396 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:53:31,397 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:53:31,421 - INFO - Generated client metrics for 11 creators
2025-05-17 22:53:31,422 - WARNING - No Status column found, using default status distribution
2025-05-17 22:53:31,423 - INFO - Analysis completed successfully
2025-05-17 22:53:37,861 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Ticket_UAT.xls_mbe42p3.xlsx
2025-05-17 22:53:38,010 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:53:38,011 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:53:38,011 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:53:38,012 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:53:38,013 - INFO - Cleaned Creator column
2025-05-17 22:53:38,014 - INFO - Removed duplicates based on Key column
2025-05-17 22:53:38,014 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:53:55,936 - INFO - Using 'Created' as creation date
2025-05-17 22:53:55,936 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:53:55,940 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:53:55,941 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:53:55,963 - INFO - Generated client metrics for 8 creators
2025-05-17 22:53:55,965 - WARNING - No Status column found, using default status distribution
2025-05-17 22:53:55,965 - INFO - Analysis completed successfully
2025-05-17 22:54:02,345 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\tickets_prod.xls_G6I7u5p.xlsx
2025-05-17 22:54:02,394 - INFO - Successfully loaded 'general_report' sheet
2025-05-17 22:54:02,395 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-17 22:54:02,395 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-17 22:54:02,396 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-17 22:54:02,397 - INFO - Cleaned Creator column
2025-05-17 22:54:02,397 - INFO - Removed duplicates based on Key column
2025-05-17 22:54:02,398 - INFO - Using 'Description.1' column for text analysis
2025-05-17 22:54:17,841 - INFO - Using 'Created' as creation date
2025-05-17 22:54:17,842 - INFO - Using 'Date of First Response' as response date
2025-05-17 22:54:17,847 - INFO - Using 'Priority' for priority impact calculation
2025-05-17 22:54:17,848 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-17 22:54:17,871 - INFO - Generated client metrics for 8 creators
2025-05-17 22:54:17,873 - WARNING - No Status column found, using default status distribution
2025-05-17 22:54:17,873 - INFO - Analysis completed successfully
2025-05-18 13:41:23,739 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls_mfC68le.xlsx
2025-05-18 13:41:24,140 - INFO - Successfully loaded 'general_report' sheet
2025-05-18 13:41:24,145 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-18 13:41:24,145 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-18 13:41:24,147 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-18 13:41:24,150 - INFO - Cleaned Creator column
2025-05-18 13:41:24,154 - INFO - Removed duplicates based on Key column
2025-05-18 13:41:29,138 - INFO - Using 'Description.1' column for text analysis
2025-05-18 13:41:49,012 - INFO - Using 'Created' as creation date
2025-05-18 13:41:49,013 - INFO - Using 'Date of First Response' as response date
2025-05-18 13:41:49,023 - INFO - Using 'Priority' for priority impact calculation
2025-05-18 13:41:49,024 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-18 13:41:49,074 - INFO - Generated client metrics for 11 creators
2025-05-18 13:41:49,076 - WARNING - No Status column found, using default status distribution
2025-05-18 13:41:49,077 - INFO - Analysis completed successfully
2025-05-19 16:06:10,666 - WARNING - Not Found: /analysis/66/
2025-05-19 16:06:10,727 - WARNING - Not Found: /favicon.ico
2025-05-19 16:06:13,122 - WARNING - Not Found: /analysis/66/
2025-05-19 16:06:36,824 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\tickets_prod.xls_qtF465F.xlsx
2025-05-19 16:06:37,114 - INFO - Successfully loaded 'general_report' sheet
2025-05-19 16:06:37,118 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-19 16:06:37,118 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-19 16:06:37,119 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-19 16:06:37,121 - INFO - Cleaned Creator column
2025-05-19 16:06:37,123 - INFO - Removed duplicates based on Key column
2025-05-19 16:06:40,885 - INFO - Using 'Description.1' column for text analysis
2025-05-19 16:07:37,603 - INFO - Using 'Created' as creation date
2025-05-19 16:07:37,604 - INFO - Using 'Date of First Response' as response date
2025-05-19 16:07:37,612 - INFO - Using 'Priority' for priority impact calculation
2025-05-19 16:07:37,614 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-19 16:07:37,709 - INFO - Generated client metrics for 8 creators
2025-05-19 16:07:37,713 - WARNING - No Status column found, using default status distribution
2025-05-19 16:07:37,714 - INFO - Analysis completed successfully
2025-05-19 16:16:39,307 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-19 16:16:53,669 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-19 16:33:48,836 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Ticket_UAT.xls_HpduQxM.xlsx
2025-05-19 16:33:49,386 - INFO - Successfully loaded 'general_report' sheet
2025-05-19 16:33:49,389 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-19 16:33:49,389 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-19 16:33:49,390 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-19 16:33:49,392 - INFO - Cleaned Creator column
2025-05-19 16:33:49,394 - INFO - Removed duplicates based on Key column
2025-05-19 16:33:55,968 - INFO - Using 'Description.1' column for text analysis
2025-05-19 16:34:32,585 - INFO - Using 'Created' as creation date
2025-05-19 16:34:32,585 - INFO - Using 'Date of First Response' as response date
2025-05-19 16:34:32,592 - INFO - Using 'Priority' for priority impact calculation
2025-05-19 16:34:32,593 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-19 16:34:32,867 - INFO - Generated client metrics for 8 creators
2025-05-19 16:34:32,869 - WARNING - No Status column found, using default status distribution
2025-05-19 16:34:32,869 - INFO - Analysis completed successfully
2025-05-20 08:30:04,492 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Ticket_UAT.xls_mbRrzUf.xlsx
2025-05-20 08:30:04,956 - INFO - Successfully loaded 'general_report' sheet
2025-05-20 08:30:04,962 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-20 08:30:04,963 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-20 08:30:04,965 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-20 08:30:04,969 - INFO - Cleaned Creator column
2025-05-20 08:30:04,972 - INFO - Removed duplicates based on Key column
2025-05-20 08:30:11,470 - INFO - Using 'Description.1' column for text analysis
2025-05-20 08:31:47,314 - INFO - Using 'Created' as creation date
2025-05-20 08:31:47,316 - INFO - Using 'Date of First Response' as response date
2025-05-20 08:31:47,332 - INFO - Using 'Priority' for priority impact calculation
2025-05-20 08:31:47,336 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-20 08:31:47,451 - INFO - Generated client metrics for 8 creators
2025-05-20 08:31:47,455 - WARNING - No Status column found, using default status distribution
2025-05-20 08:31:47,456 - INFO - Analysis completed successfully
2025-05-20 09:07:19,484 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-20 09:07:48,215 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-20 09:07:56,293 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-20 09:08:03,334 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-20 09:08:26,971 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-20 09:08:53,728 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-20 10:21:14,201 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-20 10:21:27,074 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-20 10:21:36,521 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-20 10:21:50,389 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-20 10:24:55,763 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\utils.py changed, reloading.
2025-05-20 10:25:50,431 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls_mfC68le.xlsx
2025-05-20 10:25:51,310 - INFO - Successfully loaded 'general_report' sheet
2025-05-20 10:25:51,312 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-20 10:25:51,312 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-20 10:25:51,314 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-20 10:25:51,316 - INFO - Cleaned Creator column
2025-05-20 10:25:51,319 - INFO - Removed duplicates based on Key column
2025-05-20 10:25:57,752 - INFO - Using 'Description.1' column for text analysis
2025-05-20 10:27:25,385 - INFO - Using 'Created' as creation date
2025-05-20 10:27:25,387 - INFO - Using 'Date of First Response' as response date
2025-05-20 10:27:25,417 - INFO - Using 'Priority' for priority impact calculation
2025-05-20 10:27:25,421 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-20 10:27:25,544 - INFO - Generated client metrics for 11 creators
2025-05-20 10:27:25,553 - WARNING - No Status column found, using default status distribution
2025-05-20 10:27:25,554 - INFO - Analysis completed successfully
2025-05-20 10:29:05,267 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\tickets_prod.xls_qtF465F.xlsx
2025-05-20 10:29:05,355 - INFO - Successfully loaded 'general_report' sheet
2025-05-20 10:29:05,357 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-20 10:29:05,358 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-20 10:29:05,360 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-20 10:29:05,362 - INFO - Cleaned Creator column
2025-05-20 10:29:05,363 - INFO - Removed duplicates based on Key column
2025-05-20 10:29:05,364 - INFO - Using 'Description.1' column for text analysis
2025-05-20 10:30:23,322 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\tickets_prod.xls_qtF465F.xlsx
2025-05-20 10:30:23,468 - INFO - Successfully loaded 'general_report' sheet
2025-05-20 10:30:23,471 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-20 10:30:23,471 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-20 10:30:23,473 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-20 10:30:23,483 - INFO - Cleaned Creator column
2025-05-20 10:30:23,489 - INFO - Removed duplicates based on Key column
2025-05-20 10:30:23,502 - INFO - Using 'Description.1' column for text analysis
2025-05-20 10:30:30,556 - INFO - Using 'Created' as creation date
2025-05-20 10:30:30,564 - INFO - Using 'Date of First Response' as response date
2025-05-20 10:30:30,584 - INFO - Using 'Priority' for priority impact calculation
2025-05-20 10:30:30,593 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-20 10:30:30,684 - INFO - Generated client metrics for 8 creators
2025-05-20 10:30:30,691 - WARNING - No Status column found, using default status distribution
2025-05-20 10:30:30,692 - INFO - Analysis completed successfully
2025-05-20 10:31:53,269 - INFO - Using 'Created' as creation date
2025-05-20 10:31:53,270 - INFO - Using 'Date of First Response' as response date
2025-05-20 10:31:53,278 - INFO - Using 'Priority' for priority impact calculation
2025-05-20 10:31:53,280 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-20 10:31:53,335 - INFO - Generated client metrics for 8 creators
2025-05-20 10:31:53,338 - WARNING - No Status column found, using default status distribution
2025-05-20 10:31:53,339 - INFO - Analysis completed successfully
2025-05-20 10:32:16,887 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Ticket_UAT.xls_mbRrzUf.xlsx
2025-05-20 10:32:17,311 - INFO - Successfully loaded 'general_report' sheet
2025-05-20 10:32:17,313 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-20 10:32:17,316 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-20 10:32:17,317 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-20 10:32:17,324 - INFO - Cleaned Creator column
2025-05-20 10:32:17,330 - INFO - Removed duplicates based on Key column
2025-05-20 10:32:17,330 - INFO - Using 'Description.1' column for text analysis
2025-05-20 10:34:01,927 - INFO - Using 'Created' as creation date
2025-05-20 10:34:01,929 - INFO - Using 'Date of First Response' as response date
2025-05-20 10:34:01,936 - INFO - Using 'Priority' for priority impact calculation
2025-05-20 10:34:01,938 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-20 10:34:02,000 - INFO - Generated client metrics for 8 creators
2025-05-20 10:34:02,003 - WARNING - No Status column found, using default status distribution
2025-05-20 10:34:02,003 - INFO - Analysis completed successfully
2025-05-20 10:34:48,556 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Jira_OeKB.xls_mfC68le.xlsx
2025-05-20 10:34:48,882 - INFO - Successfully loaded 'general_report' sheet
2025-05-20 10:34:48,884 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-05-20 10:34:48,885 - INFO - Found description columns: ['Description', 'Description.1']
2025-05-20 10:34:48,887 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-05-20 10:34:48,889 - INFO - Cleaned Creator column
2025-05-20 10:34:48,897 - INFO - Removed duplicates based on Key column
2025-05-20 10:34:48,898 - INFO - Using 'Description.1' column for text analysis
2025-05-20 10:36:31,624 - INFO - Using 'Created' as creation date
2025-05-20 10:36:31,626 - INFO - Using 'Date of First Response' as response date
2025-05-20 10:36:31,637 - INFO - Using 'Priority' for priority impact calculation
2025-05-20 10:36:31,645 - INFO - Using 'Issue Type' for issue type impact calculation
2025-05-20 10:36:31,717 - INFO - Generated client metrics for 11 creators
2025-05-20 10:36:31,719 - WARNING - No Status column found, using default status distribution
2025-05-20 10:36:31,720 - INFO - Analysis completed successfully
2025-05-20 11:07:16,120 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\views.py changed, reloading.
2025-05-20 11:48:06,857 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Test_Issues_since_march.xls
2025-05-20 11:48:06,871 - WARNING - Could not load 'general_report' sheet: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:48:06,872 - ERROR - Failed to load Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:48:06,872 - ERROR - Error processing file: Could not read Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:49:21,940 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Test_Issues_since_march_9FLwO44.xls
2025-05-20 11:49:21,958 - WARNING - Could not load 'general_report' sheet: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:49:21,959 - ERROR - Failed to load Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:49:21,960 - ERROR - Error processing file: Could not read Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:49:45,194 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Test_Issues_since_march_9FLwO44.xls
2025-05-20 11:49:45,196 - WARNING - Could not load 'general_report' sheet: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:49:45,198 - ERROR - Failed to load Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:49:45,198 - ERROR - Error processing file: Could not read Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:51:23,220 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Production_issues_since_march.csv
2025-05-20 11:51:23,234 - WARNING - Could not load 'general_report' sheet: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:51:23,235 - ERROR - Failed to load Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:51:23,235 - ERROR - Error processing file: Could not read Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:51:33,782 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Production_issues_since_march.csv
2025-05-20 11:51:33,783 - WARNING - Could not load 'general_report' sheet: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:51:33,784 - ERROR - Failed to load Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:51:33,784 - ERROR - Error processing file: Could not read Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:52:29,982 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Production_issues_since_march.xls
2025-05-20 11:52:29,998 - WARNING - Could not load 'general_report' sheet: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:52:29,998 - ERROR - Failed to load Excel file: Excel file format cannot be determined, you must specify an engine manually.
2025-05-20 11:52:29,999 - ERROR - Error processing file: Could not read Excel file: Excel file format cannot be determined, you must specify an engine manually.
