from django import forms
from .models import JiraFile

class JiraFileUploadForm(forms.ModelForm):
    class Meta:
        model = JiraFile
        fields = ['file']
        widgets = {
            'file': forms.FileInput(attrs={'class': 'form-control'})
        }

    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            ext = file.name.split('.')[-1].lower()
            if ext not in ['csv', 'xlsx', 'xls']:
                raise forms.ValidationError("Only CSV and Excel files are supported.")

            # Set the file_type field based on the file extension
            if ext == 'csv':
                self.instance.file_type = 'csv'
            else:
                self.instance.file_type = 'xlsx'

            # Save the original filename
            self.instance.original_filename = file.name

        return file